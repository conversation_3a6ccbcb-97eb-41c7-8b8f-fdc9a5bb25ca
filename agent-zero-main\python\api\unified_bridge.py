from agent import AgentContext, UserMessage
from python.helpers.api import <PERSON><PERSON><PERSON><PERSON><PERSON>
from flask import Request, Response
from python.helpers.defer import DeferredTask
from python.helpers.print_style import PrintStyle


class UnifiedBridge(ApiHandler):
    
    @classmethod
    def requires_auth(cls) -> bool:
        return False
    
    @classmethod
    def requires_csrf(cls) -> bool:
        return False
    
    @classmethod
    def requires_api_key(cls) -> bool:
        return True
    
    @classmethod
    def get_methods(cls) -> list[str]:
        return ["POST"]
    
    async def process(self, input: dict, request: Request) -> dict | Response:
        task, context = await self.communicate(input=input, request=request)
        return await self.respond(task, context)

    async def respond(self, task: DeferredTask, context: AgentContext):
        result = await task.result()  # type: ignore
        return {
            "message": result,
            "context": context.id,
        }

    async def communicate(self, input: dict, request: Request):
        # Handle JSON request
        input_data = request.get_json()
        text = input_data.get("text", "")
        ctxid = input_data.get("context", "")
        message_id = input_data.get("message_id", None)
        
        # Now process the message
        message = text

        # Obtain agent context
        context = self.get_context(ctxid)

        # Print to console and log
        PrintStyle(
            background_color="#6C3483", font_color="white", bold=True, padding=True
        ).print(f"Unified Bridge message:")
        PrintStyle(font_color="white", padding=False).print(f"> {message}")

        # Log the message with message_id
        context.log.log(
            type="user",
            heading="Unified Bridge message",
            content=message,
            kvps={},
            id=message_id,
        )

        return context.communicate(UserMessage(message, [])), context
