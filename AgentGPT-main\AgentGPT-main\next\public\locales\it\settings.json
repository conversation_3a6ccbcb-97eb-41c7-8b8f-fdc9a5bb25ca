{"ADVANCED_SETTINGS": "Impostazioni avanzate", "API_KEY": "Chiave API", "AUTOMATIC_MODE": "Modalità automatica", "AUTOMATIC_MODE_DESCRIPTION": "(Predefinito): L'agente esegue automaticamente ogni compito.", "CONTROL_MAXIMUM_OF_TOKENS_DESCRIPTION": "Controlla il numero massimo di token utilizzati in ogni chiamata API (un valore più alto fornisce risposte più dettagliate ma ha un costo maggiore).", "CONTROL_THE_MAXIMUM_NUM_OF_LOOPS": "Controlla il numero massimo di loop eseguiti dall'agente (un valore più alto comporta più chiamate API).", "GET_YOUR_OWN_APIKEY": "Ottieni la tua chiave API OpenAI", "HERE": "qui", "HERE_YOU_CAN_ADD_YOUR_OPENAI_API_KEY": "Qui puoi aggiungere la tua chiave API OpenAI. Ciò significa che dovrai pagare l'utilizzo dei tuoi token OpenAI, ma avrai un maggiore accesso a ChatGPT! Inoltre, puoi selezionare qualsiasi modello offerto da OpenAI.", "HIGHER_VALUES_MAKE_OUTPUT_MORE_RANDOM": "Valori più alti rendono l'output più casuale, mentre valori più bassi lo rendono più focal<PERSON>zato e determinato.", "INFO_TO_USE_GPT4": "Per utilizzare il modello GPT-4 è necessario fornire la chiave API. Puoi ottenerla", "INVALID_OPENAI_API_KEY": "Chiave API non valida!", "LABEL_MODE": "Modalità", "LABEL_MODEL": "<PERSON><PERSON>", "LANG": "<PERSON><PERSON>", "LINK": "Richiesta chiave API", "LOOP": "Loop", "MUST_CONNECT_CREDIT_CARD": "Nota: Devi collegare una carta di credito al tuo account", "NOTE_API_KEY_USAGE": "Questa chiave verrà utilizzata solo durante la sessione del browser corrente.", "NOTE_TO_GET_OPENAI_KEY": "NOTA: Per ottenere la chiave API è necessario registrarsi per un account OpenAI, che può essere fatto al seguente link:", "PAUSE": "<PERSON><PERSON> in pausa", "PAUSE_MODE": "Modalità pausa", "PAUSE_MODE_DESCRIPTION": "L'agente si mette in pausa dopo ogni insieme di compiti.", "PENAI_API_KEY": "Chiave API OpenAI non valida", "PLAY": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SETTINGS_DIALOG_HEADER": "Impostazioni ⚙", "SUBSCRIPTION_WILL_NOT_WORK": "(La sottoscrizione ChatGPT Plus non funzionerà)", "TEMPERATURE": "Temperatura", "TOKENS": "Token"}