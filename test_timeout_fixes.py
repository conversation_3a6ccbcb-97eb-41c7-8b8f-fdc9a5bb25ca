#!/usr/bin/env python3
"""
Test script to verify timeout fixes for research and code generation endpoints
"""

import asyncio
import aiohttp
import json
import time
from typing import Dict, Any

async def test_endpoint(session: aiohttp.ClientSession, endpoint: str, data: Dict[str, Any]) -> Dict[str, Any]:
    """Test a specific endpoint and measure response time"""
    start_time = time.time()
    
    try:
        async with session.post(f"http://localhost:9002/{endpoint}", json=data, timeout=45) as response:
            end_time = time.time()
            response_time = end_time - start_time
            
            if response.status == 200:
                result = await response.json()
                return {
                    "endpoint": endpoint,
                    "status": "SUCCESS",
                    "response_time": f"{response_time:.2f}s",
                    "response_length": len(result.get("response", "")),
                    "response_preview": result.get("response", "")[:200] + "..." if len(result.get("response", "")) > 200 else result.get("response", "")
                }
            else:
                return {
                    "endpoint": endpoint,
                    "status": "ERROR",
                    "response_time": f"{response_time:.2f}s",
                    "error": f"HTTP {response.status}",
                    "response": await response.text()
                }
    except asyncio.TimeoutError:
        end_time = time.time()
        response_time = end_time - start_time
        return {
            "endpoint": endpoint,
            "status": "TIMEOUT",
            "response_time": f"{response_time:.2f}s",
            "error": "Request timed out"
        }
    except Exception as e:
        end_time = time.time()
        response_time = end_time - start_time
        return {
            "endpoint": endpoint,
            "status": "ERROR",
            "response_time": f"{response_time:.2f}s",
            "error": str(e)
        }

async def main():
    """Test all endpoints that were previously timing out"""
    
    print("🧪 TESTING TIMEOUT FIXES")
    print("=" * 50)
    
    # Test cases for previously problematic endpoints
    test_cases = [
        {
            "endpoint": "chat",
            "data": {"message": "write a python function to calculate fibonacci numbers"},
            "description": "Code Generation Test"
        },
        {
            "endpoint": "chat", 
            "data": {"message": "search for latest AI developments"},
            "description": "Web Search Test"
        },
        {
            "endpoint": "chat",
            "data": {"message": "research quantum computing breakthroughs"},
            "description": "Research Test"
        },
        {
            "endpoint": "chat",
            "data": {"message": "create a simple web server in python"},
            "description": "Advanced Code Generation Test"
        },
        {
            "endpoint": "chat",
            "data": {"message": "hello, how are you?"},
            "description": "Fast Chat Test"
        }
    ]
    
    async with aiohttp.ClientSession() as session:
        results = []
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n🔍 Test {i}/5: {test_case['description']}")
            print(f"   Query: {test_case['data']['message']}")
            print("   Testing...", end="", flush=True)
            
            result = await test_endpoint(session, test_case["endpoint"], test_case["data"])
            results.append(result)
            
            # Print immediate result
            if result["status"] == "SUCCESS":
                print(f" ✅ {result['response_time']} ({result['response_length']} chars)")
            elif result["status"] == "TIMEOUT":
                print(f" ❌ TIMEOUT ({result['response_time']})")
            else:
                print(f" ❌ ERROR ({result['response_time']})")
    
    # Summary report
    print("\n" + "=" * 50)
    print("📊 TIMEOUT FIX TEST RESULTS")
    print("=" * 50)
    
    success_count = 0
    timeout_count = 0
    error_count = 0
    total_time = 0
    
    for result in results:
        status_icon = "✅" if result["status"] == "SUCCESS" else "❌"
        print(f"{status_icon} {result['endpoint']}: {result['status']} ({result['response_time']})")
        
        if result["status"] == "SUCCESS":
            success_count += 1
            total_time += float(result["response_time"].replace("s", ""))
            print(f"   Preview: {result['response_preview']}")
        elif result["status"] == "TIMEOUT":
            timeout_count += 1
        else:
            error_count += 1
            print(f"   Error: {result['error']}")
        print()
    
    # Final statistics
    print("📈 STATISTICS:")
    print(f"   ✅ Successful: {success_count}/5 ({success_count/5*100:.1f}%)")
    print(f"   ⏱️ Timeouts: {timeout_count}/5 ({timeout_count/5*100:.1f}%)")
    print(f"   ❌ Errors: {error_count}/5 ({error_count/5*100:.1f}%)")
    
    if success_count > 0:
        avg_time = total_time / success_count
        print(f"   📊 Average response time: {avg_time:.2f}s")
    
    if success_count >= 4:
        print("\n🎉 TIMEOUT FIXES SUCCESSFUL! System is ready for testing.")
    elif success_count >= 2:
        print("\n⚠️ PARTIAL SUCCESS - Some endpoints still need work.")
    else:
        print("\n❌ TIMEOUT FIXES FAILED - Need more debugging.")

if __name__ == "__main__":
    asyncio.run(main())
