{"ADVANCED_SETTINGS": "Расширенные настройки", "API_KEY": "API-к<PERSON><PERSON><PERSON>", "AUTOMATIC_MODE": "Автоматический режим", "AUTOMATIC_MODE_DESCRIPTION": "(По умолчанию): Агент автоматически выполняет каждую задачу.", "CONTROL_MAXIMUM_OF_TOKENS_DESCRIPTION": "Ограничивает максимальное количество токенов, используемых в каждом вызове API (более высокое значение дает более подробные ответы, но стоит дороже).", "CONTROL_THE_MAXIMUM_NUM_OF_LOOPS": "Управляет максимальным числом циклов, запущенных агентом (большее значение приводит к большему числу вызовов API).", "GET_YOUR_OWN_APIKEY": "Получите свой собственный ключ API OpenAI", "HERE": "здесь", "HERE_YOU_CAN_ADD_YOUR_OPENAI_API_KEY": "Здесь вы можете добавить свой ключ OpenAI API. Это означает, что вам нужно заплатить за использование своего токена OpenAI, но вы получите больший доступ к ChatGPT! Кроме того, вы можете выбрать любую модель, предлагаемую OpenAI.", "HIGHER_VALUES_MAKE_OUTPUT_MORE_RANDOM": "Большие значения делают вывод более случайным, в то время как более низкие значения делают его более фокусированным и определенным.", "INFO_TO_USE_GPT4": "Для использования модели GPT-4 необходимо также указать API-ключ. Вы можете получить его", "INVALID_OPENAI_API_KEY": "Неверный API-ключ!", "LABEL_MODE": "Режим", "LABEL_MODEL": "Модель", "LANG": "Язык", "LINK": "запросить API-ключ", "LOOP": "<PERSON>и<PERSON><PERSON>", "MUST_CONNECT_CREDIT_CARD": "Примечание: Вы должны подключить кредитную карту к вашему аккаунту", "NOTE_API_KEY_USAGE": "Этот ключ используется только в текущей сессии браузера.", "NOTE_TO_GET_OPENAI_KEY": "ПРИМЕЧАНИЕ: Для получения API-ключа вам необходимо зарегистрироваться в учетной записи OpenAI по следующей ссылке:", "PAUSE": "Пауза", "PAUSE_MODE": "Режим паузы", "PAUSE_MODE_DESCRIPTION": "Агент приостанавливает выполнение после каждого набора задач(и)", "PENAI_API_KEY": "Недействительный API-ключ OpenAI", "PLAY": "Воспроизвести", "SETTINGS_DIALOG_HEADER": "Настройки ⚙", "SUBSCRIPTION_WILL_NOT_WORK": "(Подписка ChatGPT Plus не будет работать)", "TEMPERATURE": "Температура", "TOKENS": "Токены"}