#!/usr/bin/env python3
"""
Test task planning and autonomous execution capabilities
"""

import requests
import json
import time

def test_task_planning():
    """Test task planning capability"""
    print("📋 Testing Task Planning Capability")
    print("=" * 50)
    
    base_url = "http://localhost:9001"
    
    try:
        payload = {
            "message": "Help me plan a software development project",
            "capability": "task_planning"
        }
        
        print(f"📤 Sending request: {payload['message']}")
        
        start_time = time.time()
        response = requests.post(f"{base_url}/chat", json=payload, timeout=60)
        end_time = time.time()
        
        print(f"⏱️ Response time: {end_time - start_time:.2f}s")
        print(f"📊 Status code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Success!")
            print(f"🤖 Model used: {result.get('model_used', 'unknown')}")
            print(f"🎯 Capability: {result.get('capability_used', 'unknown')}")
            print(f"💬 Response preview: {result.get('response', 'No response')[:200]}...")
            return True
        else:
            print(f"❌ Failed with status: {response.status_code}")
            print(f"Error: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print(f"⏰ Timeout after 60 seconds")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_autonomous_execution():
    """Test autonomous execution capability"""
    print("\n🤖 Testing Autonomous Execution Capability")
    print("=" * 50)
    
    base_url = "http://localhost:9001"
    
    try:
        payload = {
            "message": "Autonomously analyze the current system status and provide recommendations",
            "capability": "autonomous_execution"
        }
        
        print(f"📤 Sending request: {payload['message']}")
        
        start_time = time.time()
        response = requests.post(f"{base_url}/chat", json=payload, timeout=60)
        end_time = time.time()
        
        print(f"⏱️ Response time: {end_time - start_time:.2f}s")
        print(f"📊 Status code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Success!")
            print(f"🤖 Model used: {result.get('model_used', 'unknown')}")
            print(f"🎯 Capability: {result.get('capability_used', 'unknown')}")
            print(f"💬 Response preview: {result.get('response', 'No response')[:200]}...")
            return True
        else:
            print(f"❌ Failed with status: {response.status_code}")
            print(f"Error: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print(f"⏰ Timeout after 60 seconds")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_capability_detection():
    """Test capability detection for planning and execution"""
    print("\n🎯 Testing Capability Detection")
    print("=" * 50)
    
    base_url = "http://localhost:9001"
    
    test_cases = [
        {
            "name": "Planning Keywords",
            "message": "I need help planning a project",
            "expected_capability": "task_planning"
        },
        {
            "name": "Execution Keywords", 
            "message": "Execute this task autonomously",
            "expected_capability": "autonomous_execution"
        },
        {
            "name": "Auto-detect Planning",
            "message": "Create a step-by-step plan for building a web app",
            "expected_capability": None  # Let it auto-detect
        }
    ]
    
    results = []
    
    for test_case in test_cases:
        print(f"\n🧪 Test: {test_case['name']}")
        print(f"📝 Message: {test_case['message']}")
        
        try:
            payload = {
                "message": test_case["message"]
            }
            
            if test_case["expected_capability"]:
                payload["capability"] = test_case["expected_capability"]
            
            start_time = time.time()
            response = requests.post(f"{base_url}/chat", json=payload, timeout=30)
            end_time = time.time()
            
            if response.status_code == 200:
                result = response.json()
                capability_used = result.get('capability_used', 'unknown')
                print(f"✅ Success! ({end_time - start_time:.2f}s)")
                print(f"🎯 Capability detected: {capability_used}")
                results.append(True)
            else:
                print(f"❌ Failed with status: {response.status_code}")
                results.append(False)
                
        except Exception as e:
            print(f"❌ Error: {e}")
            results.append(False)
    
    return results

def main():
    """Run planning and execution tests"""
    print("🚀 Testing Task Planning & Autonomous Execution")
    print("=" * 70)
    
    # Test individual capabilities
    planning_success = test_task_planning()
    execution_success = test_autonomous_execution()
    
    # Test capability detection
    detection_results = test_capability_detection()
    
    # Summary
    print("\n" + "=" * 70)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 70)
    
    print(f"📋 Task Planning: {'✅ PASS' if planning_success else '❌ FAIL'}")
    print(f"🤖 Autonomous Execution: {'✅ PASS' if execution_success else '❌ FAIL'}")
    print(f"🎯 Capability Detection: {sum(detection_results)}/{len(detection_results)} {'✅ PASS' if all(detection_results) else '❌ FAIL'}")
    
    total_tests = 2 + len(detection_results)
    passed_tests = sum([planning_success, execution_success] + detection_results)
    
    print(f"\n🏆 Overall: {passed_tests}/{total_tests} tests passed ({passed_tests/total_tests*100:.1f}%)")
    
    if passed_tests == total_tests:
        print("🎉 All tests passed! Task planning and autonomous execution are working!")
    else:
        print("⚠️ Some tests failed. Check the configuration and model availability.")

if __name__ == "__main__":
    main()
