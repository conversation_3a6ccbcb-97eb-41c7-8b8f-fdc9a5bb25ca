var z=function(J,G){var A=Object.keys(J);if(Object.getOwnPropertySymbols){var X=Object.getOwnPropertySymbols(J);G&&(X=X.filter(function(R){return Object.getOwnPropertyDescriptor(J,R).enumerable})),A.push.apply(A,X)}return A},v=function(J){for(var G=1;G<arguments.length;G++){var A=arguments[G]!=null?arguments[G]:{};G%2?z(Object(A),!0).forEach(function(X){E1(J,X,A[X])}):Object.getOwnPropertyDescriptors?Object.defineProperties(J,Object.getOwnPropertyDescriptors(A)):z(Object(A)).forEach(function(X){Object.defineProperty(J,X,Object.getOwnPropertyDescriptor(A,X))})}return J},E1=function(J,G,A){if(G=Q1(G),G in J)Object.defineProperty(J,G,{value:A,enumerable:!0,configurable:!0,writable:!0});else J[G]=A;return J},Q1=function(J){var G=q1(J,"string");return N(G)=="symbol"?G:String(G)},q1=function(J,G){if(N(J)!="object"||!J)return J;var A=J[Symbol.toPrimitive];if(A!==void 0){var X=A.call(J,G||"default");if(N(X)!="object")return X;throw new TypeError("@@toPrimitive must return a primitive value.")}return(G==="string"?String:Number)(J)},N=function(J){return N=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(G){return typeof G}:function(G){return G&&typeof Symbol=="function"&&G.constructor===Symbol&&G!==Symbol.prototype?"symbol":typeof G},N(J)};(function(J){var G=Object.defineProperty,A=function H(B,U){for(var C in U)G(B,C,{get:U[C],enumerable:!0,configurable:!0,set:function Y(Z){return U[C]=function(){return Z}}})},X=function H(B){return function(U,C){if(U===1)if(C!==null&&C!==void 0&&C.addSuffix)return B.one[0].replace("{{time}}",B.one[2]);else return B.one[0].replace("{{time}}",B.one[1]);else{var Y=U%10===1&&U%100!==11;if(C!==null&&C!==void 0&&C.addSuffix)return B.other[0].replace("{{time}}",Y?B.other[3]:B.other[4]).replace("{{count}}",String(U));else return B.other[0].replace("{{time}}",Y?B.other[1]:B.other[2]).replace("{{count}}",String(U))}}},R={lessThanXSeconds:X({one:["maz\u0101k par {{time}}","sekundi","sekundi"],other:["maz\u0101k nek\u0101 {{count}} {{time}}","sekunde","sekundes","sekundes","sekund\u0113m"]}),xSeconds:X({one:["1 {{time}}","sekunde","sekundes"],other:["{{count}} {{time}}","sekunde","sekundes","sekundes","sekund\u0113m"]}),halfAMinute:function H(B,U){if(U!==null&&U!==void 0&&U.addSuffix)return"pusmin\u016Btes";else return"pusmin\u016Bte"},lessThanXMinutes:X({one:["maz\u0101k par {{time}}","min\u016Bti","min\u016Bti"],other:["maz\u0101k nek\u0101 {{count}} {{time}}","min\u016Bte","min\u016Btes","min\u016Btes","min\u016Bt\u0113m"]}),xMinutes:X({one:["1 {{time}}","min\u016Bte","min\u016Btes"],other:["{{count}} {{time}}","min\u016Bte","min\u016Btes","min\u016Btes","min\u016Bt\u0113m"]}),aboutXHours:X({one:["apm\u0113ram 1 {{time}}","stunda","stundas"],other:["apm\u0113ram {{count}} {{time}}","stunda","stundas","stundas","stund\u0101m"]}),xHours:X({one:["1 {{time}}","stunda","stundas"],other:["{{count}} {{time}}","stunda","stundas","stundas","stund\u0101m"]}),xDays:X({one:["1 {{time}}","diena","dienas"],other:["{{count}} {{time}}","diena","dienas","dienas","dien\u0101m"]}),aboutXWeeks:X({one:["apm\u0113ram 1 {{time}}","ned\u0113\u013Ca","ned\u0113\u013Cas"],other:["apm\u0113ram {{count}} {{time}}","ned\u0113\u013Ca","ned\u0113\u013Cu","ned\u0113\u013Cas","ned\u0113\u013C\u0101m"]}),xWeeks:X({one:["1 {{time}}","ned\u0113\u013Ca","ned\u0113\u013Cas"],other:["{{count}} {{time}}","ned\u0113\u013Ca","ned\u0113\u013Cu","ned\u0113\u013Cas","ned\u0113\u013C\u0101m"]}),aboutXMonths:X({one:["apm\u0113ram 1 {{time}}","m\u0113nesis","m\u0113ne\u0161a"],other:["apm\u0113ram {{count}} {{time}}","m\u0113nesis","m\u0113ne\u0161i","m\u0113ne\u0161a","m\u0113ne\u0161iem"]}),xMonths:X({one:["1 {{time}}","m\u0113nesis","m\u0113ne\u0161a"],other:["{{count}} {{time}}","m\u0113nesis","m\u0113ne\u0161i","m\u0113ne\u0161a","m\u0113ne\u0161iem"]}),aboutXYears:X({one:["apm\u0113ram 1 {{time}}","gads","gada"],other:["apm\u0113ram {{count}} {{time}}","gads","gadi","gada","gadiem"]}),xYears:X({one:["1 {{time}}","gads","gada"],other:["{{count}} {{time}}","gads","gadi","gada","gadiem"]}),overXYears:X({one:["ilg\u0101k par 1 {{time}}","gadu","gadu"],other:["vair\u0101k nek\u0101 {{count}} {{time}}","gads","gadi","gada","gadiem"]}),almostXYears:X({one:["gandr\u012Bz 1 {{time}}","gads","gada"],other:["vair\u0101k nek\u0101 {{count}} {{time}}","gads","gadi","gada","gadiem"]})},O=function H(B,U,C){var Y=R[B](U,C);if(C!==null&&C!==void 0&&C.addSuffix)if(C.comparison&&C.comparison>0)return"p\u0113c "+Y;else return"pirms "+Y;return Y};function W(H){return function(){var B=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},U=B.width?String(B.width):H.defaultWidth,C=H.formats[U]||H.formats[H.defaultWidth];return C}}var P={full:"EEEE, y. 'gada' d. MMMM",long:"y. 'gada' d. MMMM",medium:"dd.MM.y.",short:"dd.MM.y."},$={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},L={full:"{{date}} 'plkst.' {{time}}",long:"{{date}} 'plkst.' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},w={date:W({formats:P,defaultWidth:"full"}),time:W({formats:$,defaultWidth:"full"}),dateTime:W({formats:L,defaultWidth:"full"})};function F(H){var B=Object.prototype.toString.call(H);if(H instanceof Date||N(H)==="object"&&B==="[object Date]")return new H.constructor(+H);else if(typeof H==="number"||B==="[object Number]"||typeof H==="string"||B==="[object String]")return new Date(H);else return new Date(NaN)}function f(){return j}function x1(H){j=H}var j={};function D(H,B){var U,C,Y,Z,I,E,q=f(),Q=(U=(C=(Y=(Z=B===null||B===void 0?void 0:B.weekStartsOn)!==null&&Z!==void 0?Z:B===null||B===void 0||(I=B.locale)===null||I===void 0||(I=I.options)===null||I===void 0?void 0:I.weekStartsOn)!==null&&Y!==void 0?Y:q.weekStartsOn)!==null&&C!==void 0?C:(E=q.locale)===null||E===void 0||(E=E.options)===null||E===void 0?void 0:E.weekStartsOn)!==null&&U!==void 0?U:0,x=F(H),K=x.getDay(),A1=(K<Q?7:0)+K-Q;return x.setDate(x.getDate()-A1),x.setHours(0,0,0,0),x}function T(H,B,U){var C=D(H,U),Y=D(B,U);return+C===+Y}var S=["sv\u0113tdien\u0101","pirmdien\u0101","otrdien\u0101","tre\u0161dien\u0101","ceturtdien\u0101","piektdien\u0101","sestdien\u0101"],b={lastWeek:function H(B,U,C){if(T(B,U,C))return"eeee 'plkst.' p";var Y=S[B.getDay()];return"'Pag\u0101ju\u0161\u0101 "+Y+" plkst.' p"},yesterday:"'Vakar plkst.' p",today:"'\u0160odien plkst.' p",tomorrow:"'R\u012Bt plkst.' p",nextWeek:function H(B,U,C){if(T(B,U,C))return"eeee 'plkst.' p";var Y=S[B.getDay()];return"'N\u0101kamaj\u0101 "+Y+" plkst.' p"},other:"P"},_=function H(B,U,C,Y){var Z=b[B];if(typeof Z==="function")return Z(U,C,Y);return Z};function M(H){return function(B,U){var C=U!==null&&U!==void 0&&U.context?String(U.context):"standalone",Y;if(C==="formatting"&&H.formattingValues){var Z=H.defaultFormattingWidth||H.defaultWidth,I=U!==null&&U!==void 0&&U.width?String(U.width):Z;Y=H.formattingValues[I]||H.formattingValues[Z]}else{var E=H.defaultWidth,q=U!==null&&U!==void 0&&U.width?String(U.width):H.defaultWidth;Y=H.values[q]||H.values[E]}var Q=H.argumentCallback?H.argumentCallback(B):B;return Y[Q]}}var k={narrow:["p.m.\u0113","m.\u0113"],abbreviated:["p. m. \u0113.","m. \u0113."],wide:["pirms m\u016Bsu \u0113ras","m\u016Bsu \u0113r\u0101"]},h={narrow:["1","2","3","4"],abbreviated:["1. cet.","2. cet.","3. cet.","4. cet."],wide:["pirmais ceturksnis","otrais ceturksnis","tre\u0161ais ceturksnis","ceturtais ceturksnis"]},y={narrow:["1","2","3","4"],abbreviated:["1. cet.","2. cet.","3. cet.","4. cet."],wide:["pirmaj\u0101 ceturksn\u012B","otraj\u0101 ceturksn\u012B","tre\u0161aj\u0101 ceturksn\u012B","ceturtaj\u0101 ceturksn\u012B"]},m={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["janv.","febr.","marts","apr.","maijs","j\u016Bn.","j\u016Bl.","aug.","sept.","okt.","nov.","dec."],wide:["janv\u0101ris","febru\u0101ris","marts","apr\u012Blis","maijs","j\u016Bnijs","j\u016Blijs","augusts","septembris","oktobris","novembris","decembris"]},g={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["janv.","febr.","mart\u0101","apr.","maijs","j\u016Bn.","j\u016Bl.","aug.","sept.","okt.","nov.","dec."],wide:["janv\u0101r\u012B","febru\u0101r\u012B","mart\u0101","apr\u012Bl\u012B","maij\u0101","j\u016Bnij\u0101","j\u016Blij\u0101","august\u0101","septembr\u012B","oktobr\u012B","novembr\u012B","decembr\u012B"]},c={narrow:["S","P","O","T","C","P","S"],short:["Sv","P","O","T","C","Pk","S"],abbreviated:["sv\u0113td.","pirmd.","otrd.","tre\u0161d.","ceturtd.","piektd.","sestd."],wide:["sv\u0113tdiena","pirmdiena","otrdiena","tre\u0161diena","ceturtdiena","piektdiena","sestdiena"]},u={narrow:["S","P","O","T","C","P","S"],short:["Sv","P","O","T","C","Pk","S"],abbreviated:["sv\u0113td.","pirmd.","otrd.","tre\u0161d.","ceturtd.","piektd.","sestd."],wide:["sv\u0113tdien\u0101","pirmdien\u0101","otrdien\u0101","tre\u0161dien\u0101","ceturtdien\u0101","piektdien\u0101","sestdien\u0101"]},p={narrow:{am:"am",pm:"pm",midnight:"pusn.",noon:"pusd.",morning:"r\u012Bts",afternoon:"diena",evening:"vakars",night:"nakts"},abbreviated:{am:"am",pm:"pm",midnight:"pusn.",noon:"pusd.",morning:"r\u012Bts",afternoon:"p\u0113cpusd.",evening:"vakars",night:"nakts"},wide:{am:"am",pm:"pm",midnight:"pusnakts",noon:"pusdienlaiks",morning:"r\u012Bts",afternoon:"p\u0113cpusdiena",evening:"vakars",night:"nakts"}},d={narrow:{am:"am",pm:"pm",midnight:"pusn.",noon:"pusd.",morning:"r\u012Bt\u0101",afternoon:"dien\u0101",evening:"vakar\u0101",night:"nakt\u012B"},abbreviated:{am:"am",pm:"pm",midnight:"pusn.",noon:"pusd.",morning:"r\u012Bt\u0101",afternoon:"p\u0113cpusd.",evening:"vakar\u0101",night:"nakt\u012B"},wide:{am:"am",pm:"pm",midnight:"pusnakt\u012B",noon:"pusdienlaik\u0101",morning:"r\u012Bt\u0101",afternoon:"p\u0113cpusdien\u0101",evening:"vakar\u0101",night:"nakt\u012B"}},l=function H(B,U){var C=Number(B);return C+"."},i={ordinalNumber:l,era:M({values:k,defaultWidth:"wide"}),quarter:M({values:h,defaultWidth:"wide",formattingValues:y,defaultFormattingWidth:"wide",argumentCallback:function H(B){return B-1}}),month:M({values:m,defaultWidth:"wide",formattingValues:g,defaultFormattingWidth:"wide"}),day:M({values:c,defaultWidth:"wide",formattingValues:u,defaultFormattingWidth:"wide"}),dayPeriod:M({values:p,defaultWidth:"wide",formattingValues:d,defaultFormattingWidth:"wide"})};function V(H){return function(B){var U=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},C=U.width,Y=C&&H.matchPatterns[C]||H.matchPatterns[H.defaultMatchWidth],Z=B.match(Y);if(!Z)return null;var I=Z[0],E=C&&H.parsePatterns[C]||H.parsePatterns[H.defaultParseWidth],q=Array.isArray(E)?n(E,function(K){return K.test(I)}):r(E,function(K){return K.test(I)}),Q;Q=H.valueCallback?H.valueCallback(q):q,Q=U.valueCallback?U.valueCallback(Q):Q;var x=B.slice(I.length);return{value:Q,rest:x}}}var r=function H(B,U){for(var C in B)if(Object.prototype.hasOwnProperty.call(B,C)&&U(B[C]))return C;return},n=function H(B,U){for(var C=0;C<B.length;C++)if(U(B[C]))return C;return};function s(H){return function(B){var U=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},C=B.match(H.matchPattern);if(!C)return null;var Y=C[0],Z=B.match(H.parsePattern);if(!Z)return null;var I=H.valueCallback?H.valueCallback(Z[0]):Z[0];I=U.valueCallback?U.valueCallback(I):I;var E=B.slice(Y.length);return{value:I,rest:E}}}var a=/^(\d+)\./i,o=/\d+/i,e={narrow:/^(p\.m\.ē|m\.ē)/i,abbreviated:/^(p\. m\. ē\.|m\. ē\.)/i,wide:/^(pirms mūsu ēras|mūsu ērā)/i},t={any:[/^p/i,/^m/i]},B1={narrow:/^[1234]/i,abbreviated:/^[1234](\. cet\.)/i,wide:/^(pirma(is|jā)|otra(is|jā)|treša(is|jā)|ceturta(is|jā)) ceturksn(is|ī)/i},H1={narrow:[/^1/i,/^2/i,/^3/i,/^4/i],abbreviated:[/^1/i,/^2/i,/^3/i,/^4/i],wide:[/^p/i,/^o/i,/^t/i,/^c/i]},U1={narrow:/^[jfmasond]/i,abbreviated:/^(janv\.|febr\.|marts|apr\.|maijs|jūn\.|jūl\.|aug\.|sept\.|okt\.|nov\.|dec\.)/i,wide:/^(janvār(is|ī)|februār(is|ī)|mart[sā]|aprīl(is|ī)|maij[sā]|jūnij[sā]|jūlij[sā]|august[sā]|septembr(is|ī)|oktobr(is|ī)|novembr(is|ī)|decembr(is|ī))/i},C1={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^mai/i,/^jūn/i,/^jūl/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},G1={narrow:/^[spotc]/i,short:/^(sv|pi|o|t|c|pk|s)/i,abbreviated:/^(svētd\.|pirmd\.|otrd.\|trešd\.|ceturtd\.|piektd\.|sestd\.)/i,wide:/^(svētdien(a|ā)|pirmdien(a|ā)|otrdien(a|ā)|trešdien(a|ā)|ceturtdien(a|ā)|piektdien(a|ā)|sestdien(a|ā))/i},J1={narrow:[/^s/i,/^p/i,/^o/i,/^t/i,/^c/i,/^p/i,/^s/i],any:[/^sv/i,/^pi/i,/^o/i,/^t/i,/^c/i,/^p/i,/^se/i]},X1={narrow:/^(am|pm|pusn\.|pusd\.|rīt(s|ā)|dien(a|ā)|vakar(s|ā)|nakt(s|ī))/,abbreviated:/^(am|pm|pusn\.|pusd\.|rīt(s|ā)|pēcpusd\.|vakar(s|ā)|nakt(s|ī))/,wide:/^(am|pm|pusnakt(s|ī)|pusdienlaik(s|ā)|rīt(s|ā)|pēcpusdien(a|ā)|vakar(s|ā)|nakt(s|ī))/i},Y1={any:{am:/^am/i,pm:/^pm/i,midnight:/^pusn/i,noon:/^pusd/i,morning:/^r/i,afternoon:/^(d|pēc)/i,evening:/^v/i,night:/^n/i}},Z1={ordinalNumber:s({matchPattern:a,parsePattern:o,valueCallback:function H(B){return parseInt(B,10)}}),era:V({matchPatterns:e,defaultMatchWidth:"wide",parsePatterns:t,defaultParseWidth:"any"}),quarter:V({matchPatterns:B1,defaultMatchWidth:"wide",parsePatterns:H1,defaultParseWidth:"wide",valueCallback:function H(B){return B+1}}),month:V({matchPatterns:U1,defaultMatchWidth:"wide",parsePatterns:C1,defaultParseWidth:"any"}),day:V({matchPatterns:G1,defaultMatchWidth:"wide",parsePatterns:J1,defaultParseWidth:"any"}),dayPeriod:V({matchPatterns:X1,defaultMatchWidth:"wide",parsePatterns:Y1,defaultParseWidth:"any"})},I1={code:"lv",formatDistance:O,formatLong:w,formatRelative:_,localize:i,match:Z1,options:{weekStartsOn:1,firstWeekContainsDate:4}};window.dateFns=v(v({},window.dateFns),{},{locale:v(v({},(J=window.dateFns)===null||J===void 0?void 0:J.locale),{},{lv:I1})})})();

//# debugId=F7D5073C17B04D5E64756e2164756e21
