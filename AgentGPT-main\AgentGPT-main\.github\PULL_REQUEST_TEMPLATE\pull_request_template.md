<!--- Provide a general summary of your changes in the Title above -->

# Description

<!--- Describe your changes in detail -->

## Motivation and Context

<!--- Why is this change required? What problem does it solve? -->
<!--- If it fixes an open issue, please link to the issue here. -->

## How has this been tested?

<!--- Please describe in detail how you tested your changes. -->
<!--- Include details of your testing environment, tests ran to see how -->
<!--- your change affects other areas of the code, etc. -->

## Screenshots (if appropriate)

## Types of changes

<!--- What types of changes does your code introduce? Put an `x` in all the boxes that apply: -->

Changes visible to users:

- [ ] **Bug fix** (prefix: `fix` - non-breaking change which fixes an issue)
- [ ] **New feature** (prefix: `feat` - non-breaking change which adds functionality)
- [ ] **Breaking change** (prefix: `feat!!` or `fix!!` - fix or feature that would cause existing functionality to not work as expected)
- [ ] **Documentation** (prefix: `docs` - improvements to any documentation content **for users**)
- [ ] **Homepage** (prefix: `page` - improvements to the [Homepage](https://agentgpt.reworkd.ai/) #{HOMEPAGE} should lead to the place where one could actually change the homepage
- [ ] **Contributing Guidelines** (prefix: `contrib` - any improvements to documentation content **for contributors** - see [Contributing](https://github.com/reworkd/AgentGPT/tree/main/.github/CONTRIBUTING.md)

Internal changes:

- [ ] **Refactor** (prefix: `refactor` - non-breaking change which only improves the design or structure of existing code, and making no changes to its external behaviour)
- [ ] **Tests** (prefix: `test` - additions and improvements to unit tests and the smoke tests)
- [ ] **Infrastructure** (prefix: `chore` - examples include GitHub Actions, issue templates)

## Checklist

<!--- Go over all the following points, and put an `x` in all the boxes that apply. -->
<!--- If you're unsure about any of these, don't hesitate to ask. We're here to help! -->

- [ ] My code follows the code style of this project and passes `npm run lint`.
- [ ] My change requires a change to the documentation.
- [ ] I have [updated the documentation](https://reworkd.ai/docs) accordingly.  #   {DOCUMENTATION}     <------- this should lead to the doc that could be changed/ didnt find it
- [ ] My change has adequate [Unit Test coverage]({PLACEHOLDER}).

## Terms

<!--
By submitting this pull request, you must agree to follow our
[contributing guide](https://github.com/reworkd/AgentGPT/tree/main/.github/CONTRIBUTING.md) and
[Code of Conduct](https://github.com/reworkd/AgentGPT/tree/main/.github/CODE_OF_CONDUCT.md).
Put an x in the boxes to confirm you agree.
-->

- [ ] My contribution follow this project's [contributing guide](https://github.com/reworkd/AgentGPT/tree/main/.github/CONTRIBUTING.md)
- [ ] I agree to follow this project's [Code of Conduct](https://github.com/reworkd/AgentGPT/tree/main/.github/CODE_OF_CONDUCT.md)
