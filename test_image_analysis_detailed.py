#!/usr/bin/env python3
"""
Detailed test of image analysis functionality
"""

import requests
import json
import base64
import time

def test_image_analysis():
    """Test image analysis with detailed output"""
    
    # Create a simple test image (1x1 red pixel PNG)
    test_image_b64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="
    
    print("🖼️ Testing Image Analysis...")
    
    try:
        start_time = time.time()
        
        # Test via unified agent
        files = {'file': ('test.png', base64.b64decode(test_image_b64), 'image/png')}
        data = {'analysis_type': 'general'}
        
        response = requests.post(
            "http://localhost:9002/image",
            files=files,
            data=data,
            timeout=60
        )
        
        end_time = time.time()
        response_time = end_time - start_time
        
        print(f"⏱️ Response time: {response_time:.2f}s")
        print(f"📊 Status code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ SUCCESS!")
            print(f"📄 Full Response: {json.dumps(result, indent=2)}")
            
            # Extract analysis
            analysis = result.get("analysis", "No analysis found")
            print(f"📝 Analysis: {analysis}")
            return True
        else:
            print(f"❌ FAILED: HTTP {response.status_code}")
            print(f"📄 Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_image_analysis()
    if success:
        print("\n🎉 Image analysis is working!")
    else:
        print("\n💥 Image analysis has issues!")
