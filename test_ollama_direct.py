#!/usr/bin/env python3
"""
Direct test of Ollama API to check performance
"""

import requests
import json
import time

def test_ollama_direct():
    """Test Ollama API directly"""
    
    # Test simple model first
    models_to_test = [
        "llama3.2:latest",
        "nemotron-mini:4b",  # This should be fast
        "hermes3:8b"
    ]
    
    for model in models_to_test:
        print(f"\n🧪 Testing model: {model}")
        
        payload = {
            "model": model,
            "prompt": "Hello! Please respond with just 'Hi there!'",
            "stream": False,
            "options": {
                "temperature": 0.1,
                "num_predict": 10  # Limit response length
            }
        }
        
        try:
            start_time = time.time()
            print(f"⏱️ Sending request...")
            
            response = requests.post(
                "http://localhost:11434/api/generate",
                json=payload,
                timeout=60
            )
            
            end_time = time.time()
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Success! ({end_time - start_time:.2f}s)")
                print(f"📝 Response: {result.get('response', 'No response')}")
                print(f"⚡ Load time: {result.get('load_duration', 0) / 1e9:.2f}s")
                print(f"🔥 Eval time: {result.get('eval_duration', 0) / 1e9:.2f}s")
                break  # If one works, we're good
            else:
                print(f"❌ Failed with status: {response.status_code}")
                print(f"Error: {response.text}")
                
        except requests.exceptions.Timeout:
            print(f"⏰ Timeout after 60 seconds")
        except Exception as e:
            print(f"❌ Error: {e}")
        
        print("-" * 60)

if __name__ == "__main__":
    print("🚀 Testing Ollama API directly")
    print("=" * 60)
    test_ollama_direct()
