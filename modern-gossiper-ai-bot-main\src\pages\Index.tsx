import { useState, useEffect } from 'react';
import { ChatBox } from '@/components/ChatBox';
import { Button } from '@/components/ui/button';
import { MessageSquare, Sparkles, Zap, Shield, Brain, Search, Code, Mic, Eye, Cpu } from 'lucide-react';

const Index = () => {
  const [showChat, setShowChat] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    setIsLoaded(true);

    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  if (showChat) {
    return <ChatBox />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
      {/* Enhanced Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-900/20 via-purple-900/20 to-pink-900/20"></div>

      {/* Animated Background Orbs */}
      <div className="absolute top-1/4 left-1/4 w-72 h-72 bg-gradient-to-r from-blue-400/20 to-purple-600/20 rounded-full blur-3xl animate-float"></div>
      <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-gradient-to-r from-purple-400/20 to-pink-600/20 rounded-full blur-3xl animate-float [animation-delay:1s]"></div>
      <div className="absolute top-3/4 left-1/3 w-64 h-64 bg-gradient-to-r from-cyan-400/20 to-blue-600/20 rounded-full blur-3xl animate-float [animation-delay:2s]"></div>
      <div className="absolute top-1/2 right-1/3 w-80 h-80 bg-gradient-to-r from-pink-400/15 to-purple-600/15 rounded-full blur-3xl animate-float [animation-delay:3s]"></div>

      {/* Interactive Mouse Follower */}
      <div
        className="absolute w-96 h-96 bg-gradient-to-r from-blue-400/10 to-purple-600/10 rounded-full blur-3xl pointer-events-none transition-all duration-1000 ease-out"
        style={{
          left: mousePosition.x - 192,
          top: mousePosition.y - 192,
        }}
      ></div>

      {/* Animated Grid Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0" style={{
          backgroundImage: `radial-gradient(circle at 1px 1px, rgba(255,255,255,0.3) 1px, transparent 0)`,
          backgroundSize: '50px 50px',
          animation: 'float 20s linear infinite'
        }}></div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 min-h-screen flex items-center justify-center p-8">
        <div className="text-center max-w-6xl mx-auto">
          {/* Hero Section */}
          <div className={`mb-16 transition-all duration-1000 ${isLoaded ? 'animate-slide-in-up' : 'opacity-0'}`}>
            <div className="relative mb-8">
              <h1 className="text-7xl md:text-8xl font-bold gradient-text-animated mb-6 tracking-tight">
                Unified AI System
              </h1>
              <div className="absolute -inset-4 bg-gradient-to-r from-blue-600/20 via-purple-600/20 to-pink-600/20 blur-2xl rounded-full animate-pulse-glow"></div>
            </div>

            <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-4xl mx-auto leading-relaxed font-light">
              Experience the future of AI with our unified system featuring
              <span className="text-blue-400 font-semibold"> web search</span>,
              <span className="text-purple-400 font-semibold"> code generation</span>,
              <span className="text-pink-400 font-semibold"> task planning</span>,
              <span className="text-cyan-400 font-semibold"> voice processing</span>, and
              <span className="text-green-400 font-semibold"> multimodal capabilities</span>.
            </p>

            {/* Floating Capability Icons */}
            <div className="flex justify-center space-x-8 mb-8">
              {[
                { icon: Search, color: 'text-blue-400', delay: '0s' },
                { icon: Code, color: 'text-purple-400', delay: '0.2s' },
                { icon: Brain, color: 'text-pink-400', delay: '0.4s' },
                { icon: Mic, color: 'text-cyan-400', delay: '0.6s' },
                { icon: Eye, color: 'text-green-400', delay: '0.8s' },
              ].map((item, index) => (
                <div
                  key={index}
                  className={`p-4 glass-card rounded-2xl hover-lift hover-glow animate-bounce-in ${item.color}`}
                  style={{ animationDelay: item.delay }}
                >
                  <item.icon className="w-8 h-8" />
                </div>
              ))}
            </div>
          </div>

          {/* Enhanced Feature Cards */}
          <div className={`grid md:grid-cols-3 gap-8 mb-16 transition-all duration-1000 ${isLoaded ? 'animate-slide-in-up' : 'opacity-0'}`} style={{ animationDelay: '0.3s' }}>
            {[
              {
                icon: Search,
                title: "Web Search & Research",
                description: "Real-time web search with multiple sources, current information, and cited results.",
                gradient: "from-blue-500 to-cyan-500",
                bgGradient: "from-blue-500/10 to-cyan-500/10"
              },
              {
                icon: Code,
                title: "Code Generation",
                description: "Multi-language programming assistance with advanced code analysis and generation.",
                gradient: "from-purple-500 to-pink-500",
                bgGradient: "from-purple-500/10 to-pink-500/10"
              },
              {
                icon: Shield,
                title: "Local & Private",
                description: "All AI processing runs locally with Ollama - your data never leaves your machine.",
                gradient: "from-green-500 to-emerald-500",
                bgGradient: "from-green-500/10 to-emerald-500/10"
              }
            ].map((feature, index) => (
              <div
                key={index}
                className={`glass-card-intense p-8 rounded-3xl hover-lift hover-glow group cursor-pointer transition-all duration-500 bg-gradient-to-br ${feature.bgGradient} animate-scale-in`}
                style={{ animationDelay: `${0.4 + index * 0.1}s` }}
              >
                <div className={`w-16 h-16 rounded-2xl bg-gradient-to-r ${feature.gradient} p-4 mb-6 mx-auto group-hover:scale-110 transition-transform duration-300 animate-pulse-glow`}>
                  <feature.icon className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-white mb-4 group-hover:text-transparent group-hover:bg-gradient-to-r group-hover:bg-clip-text group-hover:from-white group-hover:to-gray-300 transition-all duration-300">
                  {feature.title}
                </h3>
                <p className="text-gray-300 text-sm leading-relaxed group-hover:text-gray-200 transition-colors duration-300">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>

          {/* CTA Button */}
          <div className="animate-fade-in-up [animation-delay:0.4s]">
            <Button
              onClick={() => setShowChat(true)}
              size="lg"
              className="bg-gradient-primary hover:shadow-glow text-lg px-8 py-6 rounded-2xl transition-spring group"
            >
              <MessageSquare className="mr-2 h-5 w-5 group-hover:scale-110 transition-spring" />
              Start Chatting Now
            </Button>
          </div>

          {/* Demo Stats */}
          <div className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8 animate-fade-in-up [animation-delay:0.6s]">
            <div className="text-center">
              <div className="text-3xl font-bold bg-gradient-to-r from-glass-primary to-glass-secondary bg-clip-text text-transparent">10K+</div>
              <div className="text-sm text-muted-foreground">Active Users</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold bg-gradient-to-r from-glass-secondary to-glass-accent bg-clip-text text-transparent">99.9%</div>
              <div className="text-sm text-muted-foreground">Uptime</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold bg-gradient-to-r from-glass-accent to-glass-primary bg-clip-text text-transparent">24/7</div>
              <div className="text-sm text-muted-foreground">Support</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold bg-gradient-to-r from-glass-primary to-glass-accent bg-clip-text text-transparent">&lt; 100ms</div>
              <div className="text-sm text-muted-foreground">Response Time</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Index;
