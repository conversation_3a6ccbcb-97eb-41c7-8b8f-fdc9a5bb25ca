import { useState } from 'react';
import { ChatBox } from '@/components/ChatBox';
import { Button } from '@/components/ui/button';
import { MessageSquare, Sparkles, Zap, Shield } from 'lucide-react';

const Index = () => {
  const [showChat, setShowChat] = useState(false);

  if (showChat) {
    return <ChatBox />;
  }

  return (
    <div className="min-h-screen bg-background relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-br from-glass-bg via-glass-surface to-glass-bg"></div>
      <div className="absolute top-1/4 left-1/4 w-72 h-72 bg-glass-primary/10 rounded-full blur-3xl animate-float"></div>
      <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-glass-secondary/10 rounded-full blur-3xl animate-float [animation-delay:1s]"></div>
      <div className="absolute top-3/4 left-1/3 w-64 h-64 bg-glass-accent/10 rounded-full blur-3xl animate-float [animation-delay:2s]"></div>

      {/* Main Content */}
      <div className="relative z-10 min-h-screen flex items-center justify-center p-8">
        <div className="text-center max-w-4xl mx-auto">
          {/* Hero Section */}
          <div className="mb-12 animate-fade-in-up">
            <h1 className="text-6xl md:text-7xl font-bold bg-gradient-to-r from-glass-primary via-glass-secondary to-glass-accent bg-clip-text text-transparent mb-6 animate-glow">
              AI Chat Assistant
            </h1>
            <p className="text-xl md:text-2xl text-muted-foreground mb-8 max-w-3xl mx-auto leading-relaxed">
              Experience the future of conversation with our advanced AI chatbot. 
              Powered by cutting-edge technology with a beautiful glassmorphism design.
            </p>
          </div>

          {/* Feature Cards */}
          <div className="grid md:grid-cols-3 gap-6 mb-12 animate-fade-in-up [animation-delay:0.2s]">
            <div className="p-6 rounded-2xl bg-gradient-glass backdrop-blur-glass border border-glass-border/20 shadow-glass hover:shadow-intense transition-smooth group">
              <div className="h-12 w-12 rounded-xl bg-gradient-primary mx-auto mb-4 flex items-center justify-center shadow-glow group-hover:scale-110 transition-spring">
                <Sparkles className="h-6 w-6 text-primary-foreground" />
              </div>
              <h3 className="text-lg font-semibold text-foreground mb-2">Intelligent Responses</h3>
              <p className="text-muted-foreground text-sm">Advanced AI that understands context and provides meaningful, helpful responses.</p>
            </div>

            <div className="p-6 rounded-2xl bg-gradient-glass backdrop-blur-glass border border-glass-border/20 shadow-glass hover:shadow-intense transition-smooth group">
              <div className="h-12 w-12 rounded-xl bg-gradient-primary mx-auto mb-4 flex items-center justify-center shadow-glow group-hover:scale-110 transition-spring">
                <Zap className="h-6 w-6 text-primary-foreground" />
              </div>
              <h3 className="text-lg font-semibold text-foreground mb-2">Lightning Fast</h3>
              <p className="text-muted-foreground text-sm">Real-time responses with minimal latency for seamless conversations.</p>
            </div>

            <div className="p-6 rounded-2xl bg-gradient-glass backdrop-blur-glass border border-glass-border/20 shadow-glass hover:shadow-intense transition-smooth group">
              <div className="h-12 w-12 rounded-xl bg-gradient-primary mx-auto mb-4 flex items-center justify-center shadow-glow group-hover:scale-110 transition-spring">
                <Shield className="h-6 w-6 text-primary-foreground" />
              </div>
              <h3 className="text-lg font-semibold text-foreground mb-2">Secure & Private</h3>
              <p className="text-muted-foreground text-sm">Your conversations are protected with enterprise-grade security measures.</p>
            </div>
          </div>

          {/* CTA Button */}
          <div className="animate-fade-in-up [animation-delay:0.4s]">
            <Button
              onClick={() => setShowChat(true)}
              size="lg"
              className="bg-gradient-primary hover:shadow-glow text-lg px-8 py-6 rounded-2xl transition-spring group"
            >
              <MessageSquare className="mr-2 h-5 w-5 group-hover:scale-110 transition-spring" />
              Start Chatting Now
            </Button>
          </div>

          {/* Demo Stats */}
          <div className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8 animate-fade-in-up [animation-delay:0.6s]">
            <div className="text-center">
              <div className="text-3xl font-bold bg-gradient-to-r from-glass-primary to-glass-secondary bg-clip-text text-transparent">10K+</div>
              <div className="text-sm text-muted-foreground">Active Users</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold bg-gradient-to-r from-glass-secondary to-glass-accent bg-clip-text text-transparent">99.9%</div>
              <div className="text-sm text-muted-foreground">Uptime</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold bg-gradient-to-r from-glass-accent to-glass-primary bg-clip-text text-transparent">24/7</div>
              <div className="text-sm text-muted-foreground">Support</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold bg-gradient-to-r from-glass-primary to-glass-accent bg-clip-text text-transparent">&lt; 100ms</div>
              <div className="text-sm text-muted-foreground">Response Time</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Index;
