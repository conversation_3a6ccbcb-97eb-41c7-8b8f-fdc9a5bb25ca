#!/usr/bin/env python3
"""
Test web search quality with multiple queries
"""

import asyncio
import time
from local_web_search import LocalWebSearchProcessor

async def test_web_search_quality():
    print("🧪 Testing Web Search Quality...")
    
    test_queries = [
        "Python programming basics",
        "AI machine learning 2024",
        "JavaScript frameworks comparison",
        "climate change latest research",
        "cryptocurrency market trends"
    ]
    
    try:
        # Initialize search processor
        processor = LocalWebSearchProcessor()
        await processor.initialize()
        
        results = {}
        
        for query in test_queries:
            print(f"\n🔍 Testing query: '{query}'")
            start_time = time.time()
            
            try:
                search_results = await processor.search_web(query)
                duration = time.time() - start_time

                if search_results:
                    # Combine all search results into a response
                    response_text = f"Search results for '{query}':\n\n"
                    for i, result in enumerate(search_results[:5], 1):  # Top 5 results
                        response_text += f"{i}. {result.title}\n"
                        response_text += f"   URL: {result.url}\n"
                        response_text += f"   Content: {result.content[:200]}...\n\n"

                    print(f"✅ Query completed in {duration:.2f}s")
                    print(f"   Response length: {len(response_text)} chars")
                    print(f"   Sources found: {len(search_results)}")
                    print(f"   Preview: {response_text[:150]}...")

                    # Analyze content quality
                    quality_score = analyze_content_quality(response_text, search_results)
                    print(f"   Quality score: {quality_score}/100")

                    results[query] = {
                        'success': True,
                        'duration': duration,
                        'response_length': len(response_text),
                        'sources_count': len(search_results),
                        'quality_score': quality_score,
                        'preview': response_text[:200]
                    }
                else:
                    print(f"❌ Query failed - no response")
                    results[query] = {'success': False, 'error': 'No response'}
                    
            except Exception as e:
                duration = time.time() - start_time
                print(f"💥 Query failed after {duration:.2f}s: {str(e)}")
                results[query] = {'success': False, 'error': str(e), 'duration': duration}
        
        # Summary
        print("\n📊 WEB SEARCH QUALITY SUMMARY:")
        successful = sum(1 for r in results.values() if r.get('success', False))
        total = len(test_queries)
        
        print(f"Success rate: {successful}/{total} ({successful/total*100:.1f}%)")
        
        if successful > 0:
            avg_duration = sum(r['duration'] for r in results.values() if r.get('success', False)) / successful
            avg_length = sum(r['response_length'] for r in results.values() if r.get('success', False)) / successful
            avg_quality = sum(r['quality_score'] for r in results.values() if r.get('success', False)) / successful
            
            print(f"Average response time: {avg_duration:.2f}s")
            print(f"Average response length: {avg_length:.0f} chars")
            print(f"Average quality score: {avg_quality:.1f}/100")
        
        # Detailed results
        print("\n📋 DETAILED RESULTS:")
        for query, result in results.items():
            if result.get('success', False):
                print(f"✅ {query}: {result['quality_score']}/100 ({result['duration']:.1f}s)")
            else:
                print(f"❌ {query}: {result.get('error', 'Unknown error')}")
        
        return results
        
    except Exception as e:
        print(f"💥 Test failed: {e}")
        return {}

def analyze_content_quality(response_text: str, sources) -> int:
    """Analyze content quality and return score 0-100"""
    score = 0
    
    # Length check (good responses should be substantial)
    if len(response_text) > 1000:
        score += 20
    elif len(response_text) > 500:
        score += 15
    elif len(response_text) > 200:
        score += 10
    
    # Sources check
    if len(sources) >= 5:
        score += 20
    elif len(sources) >= 3:
        score += 15
    elif len(sources) >= 1:
        score += 10
    
    # Content structure check
    if '##' in response_text or '###' in response_text:  # Has headers
        score += 15
    if '1.' in response_text or '2.' in response_text:  # Has numbered lists
        score += 10
    if '- ' in response_text or '* ' in response_text:  # Has bullet points
        score += 10
    
    # Avoid generic/fake responses
    fake_indicators = ['lorem ipsum', 'placeholder', 'example text', 'sample content']
    if any(indicator in response_text.lower() for indicator in fake_indicators):
        score -= 30
    
    # Check for actual information
    if any(word in response_text.lower() for word in ['research', 'study', 'analysis', 'data', 'results']):
        score += 15
    
    return min(100, max(0, score))

if __name__ == '__main__':
    asyncio.run(test_web_search_quality())
