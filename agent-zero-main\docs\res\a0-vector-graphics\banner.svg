<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg width="100%" height="100%" viewBox="0 0 1500 500" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" xmlns:serif="http://www.serif.com/" style="fill-rule:evenodd;clip-rule:evenodd;stroke-linejoin:round;stroke-miterlimit:2;">
    <g transform="matrix(0.984135,0,0,0.982522,6.50547,-0.188557)">
        <rect x="-12.422" y="-10.789" width="1535.8" height="528.428" style="fill:url(#_Radial1);"/>
    </g>
    <g transform="matrix(1,0,0,1,-12.7156,-0.879672)">
        <g transform="matrix(0.419509,0,0,0.419509,460.669,-51.1647)">
            <path d="M922.78,720.83C854.26,603.08 785.44,484.81 715.23,364.16C645.76,484.51 577.31,603.12 508.95,721.56L402.84,721.56C507.14,541.3 715.52,183.82 715.52,183.82C715.52,183.82 924.82,540.47 1029.09,720.82L922.77,720.82L922.78,720.83Z" style="fill:white;fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(0.419509,0,0,0.419509,460.669,-51.1647)">
            <path d="M849.11,721.33L579.87,721.33C597.8,690.16 615.14,659.99 632.35,630.07L797.8,630.07C814.57,659.9 831.26,689.59 849.1,721.33L849.11,721.33Z" style="fill:white;fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(0.419509,0,0,0.419509,460.669,-51.1647)">
            <path d="M855.05,1206.57C865.74,1222.92 875.81,1238.31 887.07,1255.53L852.23,1255.53C842.21,1241.9 831.33,1227.1 820.62,1212.54L781.05,1212.54L781.05,1255.36L753.06,1255.36L753.06,1108.1C769.51,1108.1 785.22,1107.98 800.92,1108.14C813.78,1108.27 826.84,1107.45 839.46,1109.35C860.09,1112.45 876.13,1123.43 880.48,1144.9C884.99,1167.11 881.36,1187.82 861.03,1202.2C859.31,1203.42 857.63,1204.69 855.05,1206.58L855.05,1206.57ZM781.1,1184.46C799.72,1184.46 817.64,1185.48 835.37,1184.1C847.13,1183.18 852.33,1174.28 852.66,1162.31C853,1149.78 847.72,1140.09 835.69,1138.91C817.73,1137.15 799.46,1138.45 781.11,1138.45L781.11,1184.46L781.1,1184.46Z" style="fill:white;fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(0.419509,0,0,0.419509,460.669,-51.1647)">
            <path d="M403.72,1108.89L403.72,1129.91C375.04,1162.13 346.62,1194.07 316,1228.46L403.05,1228.46L403.05,1255.56L278.37,1255.56L278.37,1227.14C304.1,1198.59 330.42,1169.4 359.39,1137.26L278.72,1137.26L278.72,1108.9L403.7,1108.9L403.72,1108.89Z" style="fill:white;fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(0.419509,0,0,0.419509,460.669,-51.1647)">
            <path d="M900.13,840.53C935.45,875.53 968.66,908.44 1003.84,943.31L1003.84,859L1029.8,859L1029.8,1007.54C994.64,973.07 961.31,940.39 925.71,905.5L925.71,994.47L900.12,994.47L900.12,840.52L900.13,840.53Z" style="fill:white;fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(0.419509,0,0,0.419509,460.669,-51.1647)">
            <path d="M1006.95,1219.73C1014.51,1215.98 1025.31,1211.1 1031.82,1207.87C1054.52,1238.14 1089.37,1231.17 1104.3,1217.73C1119.23,1204.29 1123.71,1184.88 1118.11,1165.44C1110.79,1146.63 1094.57,1131.95 1070.81,1132.78C1047.05,1133.61 1026.14,1150.53 1024.15,1178.98L996.59,1178.98C996.27,1151.53 1016.46,1119.08 1048.15,1109.55C1088.01,1097.56 1127.52,1116.8 1142.4,1155.43C1156.19,1191.23 1139.26,1231.34 1102.85,1249.09C1069.61,1265.3 1026.07,1252.91 1006.95,1219.73Z" style="fill:white;fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(0.419509,0,0,0.419509,460.669,-51.1647)">
            <path d="M562.28,879C556.22,883.6 548.83,888.69 543.59,892.67C512.92,867.29 478.07,875.76 465.63,907.61C458.16,928.52 469.07,955.92 492.03,964.37C512,971.71 534.11,964.03 539.86,947.13C535.12,945.7 530.39,944.27 524.39,942.45C523.88,935.42 523.37,928.39 522.77,920.05L575.8,920.05C579.77,950.73 555.32,984.75 524.08,992.45C489.54,1000.96 453.23,981.93 440.65,948.73C428.25,915.99 443.29,876.83 474.54,860.46C505.17,844.41 543.97,852.04 562.27,879.02L562.28,879Z" style="fill:white;fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(0.419509,0,0,0.419509,460.669,-51.1647)">
            <path d="M270.94,848.74C297.34,899.29 321.9,946.31 347.56,995.45L318.28,995.45C303,967.76 287.35,939.4 270.78,909.38C254.93,939.12 240.05,967.05 224.87,995.54L195,995.54C220.13,946.97 244.73,899.41 270.94,848.74Z" style="fill:white;fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(0.419509,0,0,0.419509,460.669,-51.1647)">
            <path d="M1170.37,881.96L1121.09,881.96L1121.09,856.64L1245,856.64L1245,881.97L1197.66,881.97L1197.66,994.75L1170.37,994.75L1170.37,881.95L1170.37,881.96Z" style="fill:white;fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(0.419509,0,0,0.419509,460.669,-51.1647)">
            <path d="M553.53,1135.42L553.53,1171.44L588.09,1171.94C588.09,1171.94 588.08,1188.39 588.08,1199.51C574.01,1200 559,1199.75 545.16,1196.48C533.45,1193.71 525.04,1184.09 524.52,1171.26C523.7,1151.01 524.3,1129.7 524.3,1108.14L642.89,1108.14L642.89,1135.42L553.53,1135.42Z" style="fill:white;fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(0.419509,0,0,0.419509,460.669,-51.1647)">
            <path d="M524.56,1255.14L524.56,1217.55C532.29,1217.2 539.47,1216.87 547,1216.52C548.9,1220.35 548.68,1227.37 552.45,1227.53C556.22,1227.69 611.42,1227.53 642.31,1227.53L642.31,1255.14L524.56,1255.14Z" style="fill:white;fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(0.419509,0,0,0.419509,460.669,-51.1647)">
            <path d="M712.11,883.49L712.11,916.81L744.08,917.27C744.08,917.27 744.07,932.49 744.07,942.78C731,943 717.17,943 704.37,939.98C693.54,937.42 685.76,928.52 685.28,916.65C684.52,897.91 685.08,878.2 685.08,858.26L794.79,858.26L794.79,883.49L712.11,883.49Z" style="fill:white;fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(0.419509,0,0,0.419509,460.669,-51.1647)">
            <path d="M685.31,994.24L685.31,959.47C692.46,959.14 699.1,958.84 706.07,958.52C707.82,962.06 707.62,968.56 711.11,968.7C714.6,968.85 765.66,968.7 794.23,968.7L794.23,994.24L685.3,994.24L685.31,994.24Z" style="fill:white;fill-rule:nonzero;"/>
        </g>
    </g>
    <defs>
        <radialGradient id="_Radial1" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="matrix(336.036,-3.00149,2.99293,336.036,756.875,288.649)"><stop offset="0" style="stop-color:rgb(44,49,94);stop-opacity:1"/><stop offset="1" style="stop-color:rgb(30,31,55);stop-opacity:1"/></radialGradient>
    </defs>
</svg>
