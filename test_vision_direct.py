#!/usr/bin/env python3
"""
Direct test of qwen2.5vl:32b vision model
"""

import requests
import json
import base64
import time

def test_vision_model():
    """Test the vision model directly"""
    
    # Test image (1x1 red pixel PNG)
    test_image_b64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="
    
    print("🔍 Testing qwen2.5vl:32b vision model directly...")
    
    payload = {
        "model": "llava:7b",
        "prompt": "Describe this image in detail. What do you see?",
        "images": [test_image_b64],
        "stream": False
    }
    
    try:
        start_time = time.time()
        
        response = requests.post(
            "http://localhost:11434/api/generate",
            json=payload,
            timeout=120
        )
        
        end_time = time.time()
        response_time = end_time - start_time
        
        print(f"⏱️ Response time: {response_time:.2f}s")
        print(f"📊 Status code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            analysis = result.get("response", "No response")
            print(f"✅ SUCCESS!")
            print(f"📝 Analysis: {analysis}")
            return True
        else:
            print(f"❌ FAILED: HTTP {response.status_code}")
            print(f"📄 Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_vision_model()
    if success:
        print("\n🎉 Vision model is working!")
    else:
        print("\n💥 Vision model has issues!")
