daemon            off;
worker_processes  2;
user              www-data;

events {
    use           epoll;
    worker_connections  128;
}

error_log         /var/log/nginx/error.log info;

http {
    server_tokens off;
    include       mime.types;
    charset       utf-8;

    access_log    /var/log/nginx/access.log  combined;

    server {
        server_name   127.0.0.1:31735;
        listen        127.0.0.1:31735;

        error_page    500 502 503 504  /50x.html;

        location      / {
            root      /;
        }

    }

}
