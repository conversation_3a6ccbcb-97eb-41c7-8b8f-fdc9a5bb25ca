@echo off
echo 🤖 UNIFIED AI SYSTEM LAUNCHER
echo ================================
echo.

echo 🔍 Checking system status...
curl -s http://localhost:9001/health >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ AI System is not running!
    echo 🚀 Please start the system first with: python unified_local_agent.py
    pause
    exit /b 1
)

echo ✅ AI System is running!
echo.

echo 🎯 Choose your interface:
echo 1. Enhanced User Interface (Recommended)
echo 2. Simple Test Interface
echo 3. Web Interface (Browser)
echo 4. Direct API Test
echo.

set /p choice="Enter your choice (1-4): "

if "%choice%"=="1" (
    echo 🚀 Starting Enhanced User Interface...
    python enhanced_user_interface.py
) else if "%choice%"=="2" (
    echo 🚀 Starting Simple Test Interface...
    python test_system.py
) else if "%choice%"=="3" (
    echo 🌐 Opening Web Interface...
    start simple_web_interface.html
) else if "%choice%"=="4" (
    echo 🧪 Running API Test...
    echo Testing basic functionality...
    curl -X POST http://localhost:9001/chat -H "Content-Type: application/json" -d "{\"message\": \"Hello, how are you?\"}"
    echo.
    pause
) else (
    echo ❌ Invalid choice!
    pause
)

echo.
echo 👋 Thank you for using the Unified AI System!
pause
