{"unified_ai_system": {"name": "Unified AI Bridge", "version": "1.0.0", "description": "Integration bridge connecting Agent-Zero and AgenticSeek", "bridge_port": 9000, "auto_start_systems": true, "health_check_interval": 30, "request_timeout": 30}, "systems": {"agent_zero": {"name": "Agent-<PERSON>", "enabled": true, "port": 50001, "directory": "agent-zero-main", "startup_script": "run_ui.py", "health_endpoint": "/api/health", "api_endpoint": "/api/message", "capabilities": ["code_execution", "file_operations", "system_tasks", "terminal_access", "autonomous_agents", "tool_usage", "memory_management"], "routing_keywords": ["code", "file", "script", "program", "execute", "run", "terminal", "command", "system", "local", "create", "modify", "delete", "agent", "autonomous", "tool"], "startup_delay": 5, "max_retries": 3}, "agentic_seek": {"name": "AgenticSeek", "enabled": true, "port": 8000, "directory": "agenticSeek-main", "startup_script": "api.py", "health_endpoint": "/health", "api_endpoint": "/query", "capabilities": ["web_search", "web_browsing", "research", "information_gathering", "multi_agent_coordination", "content_analysis", "screenshot_capture"], "routing_keywords": ["search", "browse", "web", "internet", "research", "find online", "look up", "website", "url", "google", "information", "news", "current", "latest", "online"], "startup_delay": 3, "max_retries": 3}, "crew_ai": {"name": "CrewAI Orchestrator", "enabled": true, "port": 8001, "directory": ".", "startup_script": "crew_service.py", "health_endpoint": "/health", "api_endpoint": "/crew/execute", "capabilities": ["multi_agent_coordination", "workflow_orchestration", "task_delegation", "role_based_agents", "sequential_processing", "hierarchical_management", "team_collaboration", "complex_task_breakdown"], "routing_keywords": ["crew", "team", "collaborate", "workflow", "orchestrate", "delegate", "coordinate", "multi-agent", "sequential", "hierarchical", "complex task", "multiple steps", "project", "research project", "analysis"], "startup_delay": 5, "max_retries": 3}, "ten_framework": {"name": "Ten Framework RT Agent", "enabled": true, "port": 8002, "directory": ".", "startup_script": "ten_service.py", "health_endpoint": "/health", "api_endpoint": "/session/create", "capabilities": ["real_time_conversation", "multimodal_processing", "voice_interaction", "video_processing", "streaming_responses", "low_latency_communication", "session_management", "websocket_support"], "routing_keywords": ["voice", "audio", "video", "real-time", "streaming", "conversation", "multimodal", "live", "interactive", "session", "speak", "listen", "watch", "camera", "microphone"], "startup_delay": 3, "max_retries": 3}}, "routing": {"default_system": "agent_zero", "fallback_enabled": true, "intelligent_routing": true, "routing_confidence_threshold": 0.7, "load_balancing": false, "session_affinity": true}, "api": {"cors_enabled": true, "cors_origins": ["*"], "rate_limiting": {"enabled": false, "requests_per_minute": 60}, "authentication": {"enabled": false, "api_key_required": false}, "logging": {"level": "INFO", "file": "unified_bridge.log", "max_size_mb": 10, "backup_count": 5}}, "monitoring": {"health_checks": {"enabled": true, "interval_seconds": 30, "timeout_seconds": 5, "failure_threshold": 3}, "metrics": {"enabled": true, "endpoint": "/metrics", "collect_system_metrics": true}, "alerts": {"enabled": false, "webhook_url": null, "email_notifications": false}}, "features": {"cross_system_memory": {"enabled": true, "shared_context": true, "session_persistence": true}, "query_enhancement": {"enabled": true, "context_injection": true, "query_preprocessing": true}, "response_aggregation": {"enabled": false, "multi_system_queries": false, "result_fusion": false}, "auto_scaling": {"enabled": false, "load_threshold": 80, "scale_up_delay": 60}}, "security": {"input_validation": {"enabled": true, "max_query_length": 10000, "sanitize_input": true}, "output_filtering": {"enabled": true, "filter_sensitive_data": true, "content_moderation": false}, "network_security": {"localhost_only": false, "allowed_ips": [], "ssl_enabled": false}}, "performance": {"caching": {"enabled": true, "ttl_seconds": 300, "max_cache_size": 100}, "connection_pooling": {"enabled": true, "max_connections": 10, "connection_timeout": 30}, "async_processing": {"enabled": true, "max_concurrent_requests": 5, "queue_size": 50}}, "development": {"debug_mode": false, "verbose_logging": false, "auto_reload": false, "test_mode": false}, "integration_examples": {"curl_examples": {"basic_query": "curl -X POST http://localhost:9000/query -H 'Content-Type: application/json' -d '{\"query\": \"Hello, what can you do?\"}'", "agent_zero_specific": "curl -X POST http://localhost:9000/query -H 'Content-Type: application/json' -d '{\"query\": \"Create a Python script\", \"system_preference\": \"agent-zero\"}'", "agentic_seek_specific": "curl -X POST http://localhost:9000/query -H 'Content-Type: application/json' -d '{\"query\": \"Search for latest AI news\", \"system_preference\": \"agentic-seek\"}'"}, "javascript_examples": {"fetch_query": "fetch('http://localhost:9000/query', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ query: 'Your question here' }) })", "with_system_preference": "fetch('http://localhost:9000/query', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ query: 'Your question', system_preference: 'agent-zero' }) })"}}}