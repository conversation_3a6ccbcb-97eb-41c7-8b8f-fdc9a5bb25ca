{"ADVANCED_SETTINGS": "Advanced settings", "API_KEY": "Key", "AUTOMATIC_MODE": "Automatic mode", "AUTOMATIC_MODE_DESCRIPTION": "(Default): Agent automatically executes every task.", "CONTROL_MAXIMUM_OF_TOKENS_DESCRIPTION": "Controls the maximum number of tokens used in each API call (higher value will make responses more detailed but cost more).", "CONTROL_THE_MAXIMUM_NUM_OF_LOOPS": "Controls the maximum number of loops that the agent will run (higher value will make more API calls).", "GET_YOUR_OWN_APIKEY": "Get your own OpenAI API key", "HERE": "here", "HERE_YOU_CAN_ADD_YOUR_OPENAI_API_KEY": "Here you can add your OpenAI API key. This will require you to pay for your own OpenAI usage but give you greater access to AgentGPT! You can additionally select any model OpenAI offers.", "HIGHER_VALUES_MAKE_OUTPUT_MORE_RANDOM": "Higher values will make the output more random, while lower values make the output more focused and deterministic.", "INFO_TO_USE_GPT4": "To use the GPT-4 model, you need to also provide the API key for GPT-4. You can request for it", "INVALID_OPENAI_API_KEY": "Invalid API key!", "LABEL_MODE": "Mode", "LABEL_MODEL": "Model", "LANG": "Language", "LINK": "link", "LOOP": "Loop", "MUST_CONNECT_CREDIT_CARD": "Note: You must connect a credit card to your account", "NOTE_API_KEY_USAGE": "This key is only used in the current browser session", "NOTE_TO_GET_OPENAI_KEY": "NOTE: To get a key, sign up for an OpenAI account and visit the following", "PAUSE": "Pause", "PAUSE_MODE": "Pause mode", "PAUSE_MODE_DESCRIPTION": "Agent pauses after every set of task(s)", "PENAI_API_KEY": "Invalid OpenAI API-key", "PLAY": "Play", "SETTINGS_DIALOG_HEADER": "Settings ⚙", "SUBSCRIPTION_WILL_NOT_WORK": "(ChatGPT Plus subscription will not work)", "TEMPERATURE": "Temp.", "TOKENS": "Tokens"}