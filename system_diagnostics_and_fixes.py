#!/usr/bin/env python3
"""
System Diagnostics and Fixes - Comprehensive system improvement
"""

import asyncio
import json
import logging
import requests
import time
from typing import Dict, List, Any

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("system-diagnostics")

class SystemDiagnostics:
    """Comprehensive system diagnostics and fixes"""
    
    def __init__(self):
        self.base_url = "http://localhost:9001"
        self.ollama_url = "http://localhost:11434"
        self.issues_found = []
        self.fixes_applied = []
    
    def run_full_diagnostics(self):
        """Run comprehensive system diagnostics"""
        print("🔍 RUNNING COMPREHENSIVE SYSTEM DIAGNOSTICS")
        print("=" * 60)
        
        # Test 1: Basic connectivity
        self.test_basic_connectivity()
        
        # Test 2: Capability routing
        self.test_capability_routing()
        
        # Test 3: Web search functionality
        self.test_web_search()
        
        # Test 4: Code generation quality
        self.test_code_generation()
        
        # Test 5: Model performance
        self.test_model_performance()
        
        # Test 6: Response quality
        self.test_response_quality()
        
        # Generate report
        self.generate_report()
    
    def test_basic_connectivity(self):
        """Test basic system connectivity"""
        print("\n🔌 Testing Basic Connectivity...")
        
        try:
            # Test main system
            response = requests.get(f"{self.base_url}/health", timeout=10)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Main system: {data}")
            else:
                self.issues_found.append("Main system not responding properly")
                print(f"❌ Main system: Status {response.status_code}")
        except Exception as e:
            self.issues_found.append(f"Main system connection failed: {e}")
            print(f"❌ Main system: {e}")
        
        try:
            # Test Ollama
            response = requests.get(f"{self.ollama_url}/api/tags", timeout=10)
            if response.status_code == 200:
                print("✅ Ollama server: Connected")
            else:
                self.issues_found.append("Ollama server not responding")
                print(f"❌ Ollama server: Status {response.status_code}")
        except Exception as e:
            self.issues_found.append(f"Ollama connection failed: {e}")
            print(f"❌ Ollama server: {e}")
    
    def test_capability_routing(self):
        """Test capability routing accuracy"""
        print("\n🎯 Testing Capability Routing...")
        
        test_cases = [
            ("Search for latest AI news", "web_search"),
            ("Write a Python function", "code_generation"),
            ("Plan a software project", "task_planning"),
            ("Analyze this image", "multimodal_processing"),
            ("Convert text to speech", "voice_processing"),
        ]
        
        for message, expected_capability in test_cases:
            try:
                response = requests.post(
                    f"{self.base_url}/chat",
                    json={"message": message},
                    timeout=30
                )
                
                if response.status_code == 200:
                    data = response.json()
                    actual_capability = data.get("capability_used", "unknown")
                    
                    if actual_capability == expected_capability:
                        print(f"✅ '{message}' → {actual_capability}")
                    else:
                        print(f"❌ '{message}' → {actual_capability} (expected {expected_capability})")
                        self.issues_found.append(f"Routing issue: '{message}' routed to {actual_capability} instead of {expected_capability}")
                else:
                    print(f"❌ Request failed: {response.status_code}")
                    self.issues_found.append(f"Request failed for: {message}")
                    
            except Exception as e:
                print(f"❌ Error testing '{message}': {e}")
                self.issues_found.append(f"Error testing '{message}': {e}")
    
    def test_web_search(self):
        """Test web search functionality specifically"""
        print("\n🔍 Testing Web Search Functionality...")
        
        search_queries = [
            "latest Python developments",
            "current AI research trends",
            "recent technology news"
        ]
        
        for query in search_queries:
            try:
                response = requests.post(
                    f"{self.base_url}/chat",
                    json={"message": f"Search for {query}", "capability": "web_search"},
                    timeout=45
                )
                
                if response.status_code == 200:
                    data = response.json()
                    response_text = data.get("response", "")
                    
                    # Check if response contains actual search results
                    if any(indicator in response_text.lower() for indicator in 
                           ["source", "url", "according to", "based on", "search results"]):
                        print(f"✅ Web search working for: {query}")
                    else:
                        print(f"❌ Web search not providing real results for: {query}")
                        self.issues_found.append(f"Web search not working properly for: {query}")
                else:
                    print(f"❌ Web search failed: {response.status_code}")
                    self.issues_found.append(f"Web search request failed for: {query}")
                    
            except Exception as e:
                print(f"❌ Web search error for '{query}': {e}")
                self.issues_found.append(f"Web search error for '{query}': {e}")
    
    def test_code_generation(self):
        """Test code generation quality"""
        print("\n💻 Testing Code Generation Quality...")
        
        code_requests = [
            "Write a simple Python hello world function",
            "Create a JavaScript function to add two numbers",
            "Write a SQL query to select all users"
        ]
        
        for request in code_requests:
            try:
                response = requests.post(
                    f"{self.base_url}/chat",
                    json={"message": request, "capability": "code_generation"},
                    timeout=30
                )
                
                if response.status_code == 200:
                    data = response.json()
                    response_text = data.get("response", "")
                    
                    # Check if response contains actual code
                    if "```" in response_text or "def " in response_text or "function" in response_text:
                        print(f"✅ Code generation working for: {request}")
                    else:
                        print(f"❌ Code generation not providing code for: {request}")
                        self.issues_found.append(f"Code generation issue for: {request}")
                else:
                    print(f"❌ Code generation failed: {response.status_code}")
                    
            except Exception as e:
                print(f"❌ Code generation error for '{request}': {e}")
                self.issues_found.append(f"Code generation error for '{request}': {e}")
    
    def test_model_performance(self):
        """Test model performance and response times"""
        print("\n⚡ Testing Model Performance...")
        
        start_time = time.time()
        try:
            response = requests.post(
                f"{self.base_url}/chat",
                json={"message": "Hello, how are you?"},
                timeout=30
            )
            
            end_time = time.time()
            response_time = end_time - start_time
            
            if response.status_code == 200:
                print(f"✅ Response time: {response_time:.2f}s")
                if response_time > 15:
                    self.issues_found.append(f"Slow response time: {response_time:.2f}s")
            else:
                print(f"❌ Performance test failed: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Performance test error: {e}")
            self.issues_found.append(f"Performance test error: {e}")
    
    def test_response_quality(self):
        """Test response quality and coherence"""
        print("\n📝 Testing Response Quality...")
        
        quality_tests = [
            "Explain what artificial intelligence is",
            "What are the benefits of using Python?",
            "How does machine learning work?"
        ]
        
        for test in quality_tests:
            try:
                response = requests.post(
                    f"{self.base_url}/chat",
                    json={"message": test},
                    timeout=30
                )
                
                if response.status_code == 200:
                    data = response.json()
                    response_text = data.get("response", "")
                    
                    # Basic quality checks
                    if len(response_text) > 50 and not "error" in response_text.lower():
                        print(f"✅ Quality response for: {test}")
                    else:
                        print(f"❌ Poor quality response for: {test}")
                        self.issues_found.append(f"Poor quality response for: {test}")
                else:
                    print(f"❌ Quality test failed: {response.status_code}")
                    
            except Exception as e:
                print(f"❌ Quality test error for '{test}': {e}")
    
    def generate_report(self):
        """Generate comprehensive diagnostic report"""
        print("\n" + "=" * 60)
        print("📊 DIAGNOSTIC REPORT")
        print("=" * 60)
        
        if not self.issues_found:
            print("🎉 NO ISSUES FOUND! System is working optimally.")
        else:
            print(f"⚠️  FOUND {len(self.issues_found)} ISSUES:")
            for i, issue in enumerate(self.issues_found, 1):
                print(f"   {i}. {issue}")
        
        print("\n🔧 RECOMMENDED FIXES:")
        print("1. Restart the unified agent to apply recent improvements")
        print("2. Check Ollama models are properly loaded")
        print("3. Verify web search dependencies (beautifulsoup4, numpy)")
        print("4. Test individual capabilities with explicit capability parameter")
        print("5. Monitor system logs for detailed error information")
        
        print("\n💡 USAGE TIPS:")
        print("• Use explicit capability parameter for better routing")
        print("• Start queries with clear action words (Search, Write, Plan, etc.)")
        print("• For web search, use phrases like 'Search for', 'Find information about'")
        print("• For code generation, use 'Write code', 'Create function', etc.")

def main():
    """Run system diagnostics"""
    diagnostics = SystemDiagnostics()
    diagnostics.run_full_diagnostics()

if __name__ == "__main__":
    main()
