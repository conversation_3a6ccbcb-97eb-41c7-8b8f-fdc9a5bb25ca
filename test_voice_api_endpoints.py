#!/usr/bin/env python3
"""
Test voice API endpoints with proper requests
"""

import requests
import json
import time
import base64
import tempfile
import wave
import numpy as np

def create_test_audio():
    """Create a simple test audio file"""
    # Generate a simple sine wave (1 second, 440Hz)
    sample_rate = 16000
    duration = 1.0
    frequency = 440.0
    
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    audio_data = np.sin(2 * np.pi * frequency * t)
    
    # Convert to 16-bit PCM
    audio_data = (audio_data * 32767).astype(np.int16)
    
    # Create temporary WAV file
    with tempfile.NamedTemporaryFile(delete=False, suffix='.wav') as f:
        with wave.open(f.name, 'wb') as wav_file:
            wav_file.setnchannels(1)  # Mono
            wav_file.setsampwidth(2)  # 16-bit
            wav_file.setframerate(sample_rate)
            wav_file.writeframes(audio_data.tobytes())
        return f.name

def test_voice_endpoints():
    """Test voice API endpoints properly"""
    
    print("🎤 TESTING VOICE API ENDPOINTS")
    print("=" * 50)
    
    # Test 1: TTS endpoint with proper request format
    print("\n🔊 TEST 1: Text-to-Speech API")
    print("-" * 30)
    
    try:
        payload = {"text": "Hello, this is a test of the text to speech API."}
        
        start_time = time.time()
        response = requests.post(
            "http://localhost:9002/tts",
            json=payload,
            timeout=30
        )
        end_time = time.time()
        
        print(f"📊 Status: {response.status_code}")
        print(f"⏱️ Time: {end_time - start_time:.2f}s")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ TTS SUCCESS")
            print(f"📄 Response: {json.dumps(result, indent=2)}")
            tts_works = True
        else:
            print(f"❌ TTS FAILED: {response.text}")
            tts_works = False
            
    except Exception as e:
        print(f"❌ TTS ERROR: {str(e)}")
        tts_works = False
    
    # Test 2: Voice endpoint with proper audio file
    print("\n🎤 TEST 2: Speech-to-Text API")
    print("-" * 30)
    
    try:
        # Create test audio file
        audio_file = create_test_audio()
        print(f"📁 Created test audio: {audio_file}")
        
        with open(audio_file, 'rb') as f:
            files = {'audio_file': ('test.wav', f, 'audio/wav')}
            
            start_time = time.time()
            response = requests.post(
                "http://localhost:9002/voice",
                files=files,
                timeout=60
            )
            end_time = time.time()
        
        print(f"📊 Status: {response.status_code}")
        print(f"⏱️ Time: {end_time - start_time:.2f}s")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ STT SUCCESS")
            print(f"📄 Response: {json.dumps(result, indent=2)}")
            stt_works = True
        else:
            print(f"❌ STT FAILED: {response.text}")
            stt_works = False
            
    except Exception as e:
        print(f"❌ STT ERROR: {str(e)}")
        stt_works = False
    
    # Test 3: Chat with voice_enabled flag
    print("\n💬 TEST 3: Chat with Voice Output")
    print("-" * 30)
    
    try:
        payload = {
            "message": "Hello, please respond with voice output enabled.",
            "voice_enabled": True
        }
        
        start_time = time.time()
        response = requests.post(
            "http://localhost:9002/chat",
            json=payload,
            timeout=30
        )
        end_time = time.time()
        
        print(f"📊 Status: {response.status_code}")
        print(f"⏱️ Time: {end_time - start_time:.2f}s")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ VOICE CHAT SUCCESS")
            print(f"📄 Keys: {list(result.keys())}")
            
            # Check for voice output
            has_voice = any(key in result for key in ['voice_output', 'audio', 'audio_data', 'speech'])
            print(f"🔊 Has Voice Output: {has_voice}")
            
            if has_voice:
                voice_chat_works = True
            else:
                print("⚠️ No voice output in response")
                voice_chat_works = False
        else:
            print(f"❌ VOICE CHAT FAILED: {response.text}")
            voice_chat_works = False
            
    except Exception as e:
        print(f"❌ VOICE CHAT ERROR: {str(e)}")
        voice_chat_works = False
    
    # HONEST SUMMARY
    print("\n" + "=" * 50)
    print("🎯 HONEST VOICE CONVERSATION ASSESSMENT")
    print("=" * 50)
    
    print(f"🔊 Text-to-Speech API: {'✅ WORKING' if tts_works else '❌ NOT WORKING'}")
    print(f"🎤 Speech-to-Text API: {'✅ WORKING' if stt_works else '❌ NOT WORKING'}")
    print(f"💬 Voice Chat API: {'✅ WORKING' if voice_chat_works else '❌ NOT WORKING'}")
    
    if tts_works and stt_works:
        print("\n🎉 VOICE CONVERSATION: FULLY FUNCTIONAL")
        print("   You can have complete voice conversations!")
        return True
    elif tts_works or stt_works:
        print("\n⚠️ VOICE CONVERSATION: PARTIALLY FUNCTIONAL")
        print("   Some voice features work, but not complete conversations")
        return False
    else:
        print("\n❌ VOICE CONVERSATION: NOT FUNCTIONAL")
        print("   Voice conversation is not working")
        return False

if __name__ == "__main__":
    success = test_voice_endpoints()
    print(f"\n🎯 FINAL VERDICT: {'VOICE WORKS' if success else 'VOICE BROKEN'}")
