{"ADDING_TASK": "Adding Task", "AGENTGPT_DOCUMENTATION": "AgentGPT's Documentation", "CLOSE": "Close", "CONTINUE": "Continue", "COPIED_TO_CLIPBOARD": "Copied to clipboard! 🚀", "COPY": "Copy", "CREATE_AN_AGENT_DESCRIPTION": "Create an agent by adding a name / goal, and hitting deploy!", "CURRENT_TASKS": "Current Tasks", "EXECUTING": "Executing", "EXPORT": "Export", "IMAGE": "Image", "LOOP": "Loop", "PAUSED": "Paused", "RESET": "Reset", "RUNNING": "Running", "SAVE": "Save", "TO_LEARN_MORE_ABOUT_AGENTGPT": "To get more information about AgentGPT, its Roadmap, etc, visit the following link", "create-a-comprehensive-report-of-the-nike-company": "Create a comprehensive report of the Nike company", "if-you-are-facing-issues-please-head-over-to-our": "If you are facing issues, please head over to our", "plan-a-detailed-trip-to-hawaii": "Plan a detailed trip to Hawaii.", "platformergpt": "PlatformerGPT 🎮", "researchgpt": "ResearchGPT 📜", "travelgpt": "TravelGPT 🌴", "web-search": "Web search"}