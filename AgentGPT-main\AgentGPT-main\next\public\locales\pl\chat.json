{"COMPLETING": "Wykonywanie:", "CONSIDER_SPONSORING_ON_GITHUB": "Rozważ wsparcie projektu na GitHub.", "CONSOLE_TEXT_COPIED_TO_CLIPBOARD": "Tekst skopiowany do schowka", "CONSOLE_UNABLE_TO_COPY_TO_CLIPBOARD": "Nie można skopiować tekstu do schowka", "CREATE_AN_AGENT_DESCRIPTION": "Utw<PERSON>rz agenta, dodając nazwę i cel, a następnie kliknij przycisk \"Uruchom agenta!\"", "EMBARKING_ON_NEW_GOAL": "Nowe cel:", "EXPERIENCING_EXCEPTIONAL_TRAFFIC": "🚨 Doświadczamy wyjątkowo dużego ruchu, oczekiwane są opóźnienia i błędy, jeśli nie używasz własnego klucza API 🚨", "HELP_SUPPORT_THE_ADVANCEMENT_OF_AGENTGPT": "💝️ Wsparcie dla rozwoju AgentGPT. 💝️", "NO_MORE_TASKS": "<PERSON>rak kolejnych zadań dla tego:", "RESTART_IF_IT_TAKES_X_SEC": "(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> stronę lub uruchom agenta ponownie ręcznie, je<PERSON><PERSON> zajmie to ponad 30 sekund)", "SUPPORT_NOW": "<PERSON><PERSON><PERSON><PERSON> te<PERSON> 🚀", "TASK_ADDED": "<PERSON><PERSON><PERSON>:", "THINKING": "Myślenie...", "YOU_CAN_PROVIDE_YOUR_API_KEY": "📢 Możesz podać swój własny klucz API OpenAI w zakładce Ustawienia, aby uzyskać podwyższone limity!", "👉 Create an agent by adding a name / goal, and hitting deploy!": "👉 Utw<PERSON><PERSON>a, dodaj<PERSON>c nazwę/cel i naciskając przycisk wdrażania!"}