#!/usr/bin/env python3
"""
Test all model endpoints through the unified agent
"""

import requests
import time
import json

def test_unified_agent_endpoints():
    base_url = "http://localhost:9002"
    
    test_cases = [
        {
            "name": "Fast Chat (llama3.2)",
            "payload": {"message": "Hello, quick response test", "capability": "general_chat"},
            "expected_model": "llama3.2:latest"
        },
        {
            "name": "Research (deepseek-r1)",
            "payload": {"message": "research AI basics", "capability": "web_search"},
            "expected_model": "deepseek-r1:latest"
        },
        {
            "name": "Code Generation (deepseek-r1)",
            "payload": {"message": "write a python hello world", "capability": "code_generation"},
            "expected_model": "deepseek-r1:latest"
        },
        {
            "name": "Multimodal (qwen2.5vl)",
            "payload": {"message": "analyze this image", "capability": "multimodal_processing"},
            "expected_model": "qwen2.5vl:32b"
        },
        {
            "name": "Voice Processing",
            "payload": {"message": "process voice input", "capability": "voice_processing"},
            "expected_model": "deepseek-r1:latest"
        }
    ]
    
    print("🧪 Testing Unified Agent Model Endpoints...")
    results = {}
    
    for test in test_cases:
        print(f"\n🔍 Testing {test['name']}...")
        try:
            start_time = time.time()
            response = requests.post(f"{base_url}/chat", 
                json=test['payload'], 
                timeout=30
            )
            duration = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                response_text = data.get('response', '')
                print(f"✅ {test['name']}: {len(response_text)} chars ({duration:.2f}s)")
                results[test['name']] = {
                    'status': 'success',
                    'response_time': duration,
                    'response_length': len(response_text),
                    'preview': response_text[:100] + "..." if len(response_text) > 100 else response_text
                }
            else:
                print(f"❌ {test['name']}: HTTP {response.status_code}")
                results[test['name']] = {
                    'status': 'http_error',
                    'code': response.status_code,
                    'error': response.text
                }
                
        except requests.exceptions.Timeout:
            print(f"⏰ {test['name']}: Timeout (>30s)")
            results[test['name']] = {'status': 'timeout'}
        except Exception as e:
            print(f"💥 {test['name']}: Error - {str(e)}")
            results[test['name']] = {'status': 'error', 'error': str(e)}
    
    print("\n📊 ENDPOINT TEST SUMMARY:")
    success_count = 0
    for name, result in results.items():
        if result['status'] == 'success':
            print(f"✅ {name}: {result['response_time']:.2f}s")
            success_count += 1
        else:
            print(f"❌ {name}: {result['status']}")
    
    print(f"\n🎯 Results: {success_count}/{len(test_cases)} endpoints working")
    
    # Save detailed results
    with open('endpoint_test_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    return results

if __name__ == '__main__':
    test_unified_agent_endpoints()
