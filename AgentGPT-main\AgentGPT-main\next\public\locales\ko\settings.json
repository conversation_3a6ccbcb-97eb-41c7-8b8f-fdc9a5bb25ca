{"ADVANCED_SETTINGS": "고급 설정", "API_KEY": "API 키", "AUTOMATIC_MODE": "자동 모드", "AUTOMATIC_MODE_DESCRIPTION": "(기본값) 에이전트는 모든 작업을 자동으로 실행합니다.", "CONTROL_MAXIMUM_OF_TOKENS_DESCRIPTION": "각 API 호출에서 사용되는 토큰의 최대 수를 제어합니다 (높은 값은 자세한 응답을 제공하지만 더 비용이 듭니다).", "CONTROL_THE_MAXIMUM_NUM_OF_LOOPS": "에이전트가 실행하는 루프의 최대 수를 제어합니다 (높은 값은 더 많은 API 호출을 의미합니다).", "GET_YOUR_OWN_APIKEY": "당신만의 OpenAI API 키를 가져오세요", "HERE": "여기", "HERE_YOU_CAN_ADD_YOUR_OPENAI_API_KEY": "여기에서 OpenAI API 키를 추가할 수 있습니다. 이것은 사용자의 OpenAI 토큰을 사용하고 이를 위해 비용을 지불해야 하지만 ChatGPT에 대한 더 큰 액세스 권한을 얻을 수 있습니다. 또한 OpenAI에서 제공하는 어떤 모델도 선택할 수 있습니다.", "HIGHER_VALUES_MAKE_OUTPUT_MORE_RANDOM": "높은 값은 출력을 더 무작위하게 만듭니다. 반면, 낮은 값은 출력을 집중하고 명확하게 만듭니다.", "INFO_TO_USE_GPT4": "GPT-4 모델을 사용하려면 API 키를 입력해야 합니다. 여기서", "INVALID_OPENAI_API_KEY": "잘못된 API 키입니다!", "LABEL_MODE": "모드", "LABEL_MODEL": "모델", "LANG": "언어", "LINK": "API 키 요청", "LOOP": "루프", "MUST_CONNECT_CREDIT_CARD": "참고: 계정에 신용카드를 연결해야 합니다", "NOTE_API_KEY_USAGE": "이 키는 현재 브라우저 세션에서만 사용됩니다.", "NOTE_TO_GET_OPENAI_KEY": "참고: API 키를 얻으려면 OpenAI 계정을 등록해야 합니다. 다음 링크에서 등록할 수 있습니다:", "PAUSE": "일시 정지", "PAUSE_MODE": "일시 정지 모드", "PAUSE_MODE_DESCRIPTION": "에이전트는 각 작업 집합 후에 일시 정지됩니다.", "PENAI_API_KEY": "잘못된 OpenAI API 키", "PLAY": "재생", "SETTINGS_DIALOG_HEADER": "설정 ⚙", "SUBSCRIPTION_WILL_NOT_WORK": "(ChatGPT Plus 구독은 작동하지 않습니다)", "TEMPERATURE": "온도", "TOKENS": "토큰"}