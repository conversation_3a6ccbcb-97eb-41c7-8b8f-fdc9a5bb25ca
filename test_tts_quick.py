#!/usr/bin/env python3
"""
Quick test of TTS endpoint
"""

import requests
import json

def test_tts_endpoint():
    """Test the TTS endpoint"""
    print("🔊 Testing TTS Endpoint")
    print("=" * 40)
    
    base_url = "http://localhost:9001"
    
    try:
        payload = {
            "text": "Hello! This is a test of the text to speech API endpoint."
        }
        
        response = requests.post(
            f"{base_url}/tts",
            json=payload,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ TTS endpoint test successful")
            print(f"Response: {json.dumps(result, indent=2)}")
        else:
            print(f"❌ TTS endpoint failed with status: {response.status_code}")
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"❌ TTS endpoint error: {e}")

if __name__ == "__main__":
    test_tts_endpoint()
