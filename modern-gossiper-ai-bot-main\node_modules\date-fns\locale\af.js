"use strict";
exports.af = void 0;
var _index = require("./af/_lib/formatDistance.js");
var _index2 = require("./af/_lib/formatLong.js");
var _index3 = require("./af/_lib/formatRelative.js");
var _index4 = require("./af/_lib/localize.js");
var _index5 = require("./af/_lib/match.js");

/**
 * @category Locales
 * @summary Afrikaans locale.
 * @language Afrikaans
 * @iso-639-2 afr
 * <AUTHOR> Weststrate [@marnusw](https://github.com/marnusw)
 */
const af = (exports.af = {
  code: "af",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 0 /* Sunday */,
    firstWeekContainsDate: 1,
  },
});
