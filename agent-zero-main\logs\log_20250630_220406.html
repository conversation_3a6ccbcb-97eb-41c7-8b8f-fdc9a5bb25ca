<html><body style='background-color:black;font-family: Arial, Helvetica, sans-serif;'><pre>
<span style=" ">Initializing framework...</span><br>
<span style=" ">Starting server...</span><br>
<br><span style="color: rgb(128, 128, 128); ">Debug: Registered middleware for MCP and MCP token</span><br>
<br><span style="color: rgb(128, 128, 128); ">Debug: Starting server at localhost:50001...</span><br>
<br><span style="color: rgb(128, 128, 128); ">Debug: Changing timezone from None to America/New_York</span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: Failed to pause job loop by development instance: Cannot connect to host localhost:55080 ssl:default [The remote computer refused the network connection]</span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: Failed to pause job loop by development instance: Cannot connect to host localhost:55080 ssl:default [The remote computer refused the network connection]</span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: Failed to pause job loop by development instance: Cannot connect to host localhost:55080 ssl:default [The remote computer refused the network connection]</span><br>
