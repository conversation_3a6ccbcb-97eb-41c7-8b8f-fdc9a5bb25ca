#!/usr/bin/env python3
"""
Unified Local AI Agent - Single agent combining all AI capabilities using local Ollama models
"""

import asyncio
import json
import logging
import os
import tempfile
import time
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from enum import Enum

import requests
import aiohttp
from fastapi import FastAPI, HTTPException, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from local_voice_processor import get_voice_processor
from local_web_search import get_web_search_processor
from local_multimodal_processor import get_multimodal_processor
from openhands_service import OpenHandsService
from openvoice_service import OpenVoiceService

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("unified-local-agent")

class CapabilityType(Enum):
    CODE_GENERATION = "code_generation"
    WEB_SEARCH = "web_search"
    VOICE_PROCESSING = "voice_processing"
    MULTIMODAL_PROCESSING = "multimodal_processing"
    TASK_PLANNING = "task_planning"
    AUTONOMOUS_EXECUTION = "autonomous_execution"
    ADVANCED_DEVELOPMENT = "advanced_development"
    ADVANCED_VOICE = "advanced_voice"
    GENERAL_CHAT = "general_chat"

@dataclass
class ModelConfig:
    name: str
    purpose: str
    capabilities: List[str]
    context_window: int
    temperature: float
    url: str = "http://localhost:11434"

class UnifiedRequest(BaseModel):
    message: str
    capability: Optional[str] = None
    context: Optional[Dict[str, Any]] = None
    files: Optional[List[str]] = None
    voice_input: Optional[bool] = False
    multimodal_input: Optional[bool] = False

class UnifiedResponse(BaseModel):
    response: str
    capability_used: str
    model_used: str
    actions_taken: Optional[List[str]] = None
    files_created: Optional[List[str]] = None
    voice_output: Optional[str] = None
    execution_time: float
    success: bool

class UnifiedLocalAgent:
    """Single unified agent that combines all AI capabilities locally"""
    
    def __init__(self, config_path: str = "unified_local_config.json"):
        self.config = self._load_config(config_path)
        self.models = self._initialize_models()
        self.capabilities = self._initialize_capabilities()
        self.session_context = {}

        # Initialize new services
        self.openhands_service = OpenHandsService()
        self.openvoice_service = OpenVoiceService()

        # Initialize FastAPI app
        self.app = FastAPI(title="Unified Local AI Agent", version="4.0.0")
        self._setup_routes()
        self._setup_middleware()

        logger.info("🤖 Unified Local AI Agent initialized")
        logger.info(f"📊 Available models: {list(self.models.keys())}")
        logger.info(f"🛠️ Available capabilities: {list(self.capabilities.keys())}")
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Load configuration from JSON file"""
        try:
            with open(config_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.error(f"Config file {config_path} not found")
            raise
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in config file: {e}")
            raise
    
    def _initialize_models(self) -> Dict[str, ModelConfig]:
        """Initialize model configurations"""
        models = {}
        for model_key, model_config in self.config["models"].items():
            if model_key != "embeddings":  # Skip embedding model for now
                models[model_key] = ModelConfig(
                    name=model_config["name"],
                    purpose=model_config["purpose"],
                    capabilities=model_config["capabilities"],
                    context_window=model_config["context_window"],
                    temperature=model_config["temperature"]
                )
        return models
    
    def _initialize_capabilities(self) -> Dict[str, Dict[str, Any]]:
        """Initialize capability configurations"""
        return self.config["capabilities"]
    
    def _setup_middleware(self):
        """Setup FastAPI middleware"""
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
    
    def _setup_routes(self):
        """Setup FastAPI routes"""
        
        @self.app.get("/")
        async def root():
            return {
                "name": "Unified Local AI Agent",
                "version": "3.0.0",
                "status": "active",
                "capabilities": list(self.capabilities.keys()),
                "models": list(self.models.keys())
            }
        
        @self.app.get("/health")
        async def health():
            """Health check endpoint"""
            ollama_status = await self._check_ollama_health()
            return {
                "status": "healthy" if ollama_status else "unhealthy",
                "ollama": ollama_status,
                "models_loaded": len(self.models),
                "capabilities": len(self.capabilities)
            }
        
        @self.app.post("/chat", response_model=UnifiedResponse)
        async def chat(request: UnifiedRequest):
            """Main chat endpoint - handles all types of requests"""
            return await self.process_request(request)
        
        @self.app.post("/voice")
        async def voice_chat(audio_file: UploadFile = File(...)):
            """Voice input endpoint - processes audio and returns text response"""
            try:
                # Get voice processor
                voice_proc = await get_voice_processor()

                if not voice_proc.initialized:
                    raise HTTPException(status_code=503, detail="Voice processor not available")

                # Save uploaded audio to temporary file
                with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as temp_file:
                    content = await audio_file.read()
                    temp_file.write(content)
                    temp_audio_path = temp_file.name

                try:
                    # Convert speech to text
                    transcribed_text = await voice_proc.speech_to_text(temp_audio_path)

                    if not transcribed_text:
                        raise HTTPException(status_code=400, detail="Could not transcribe audio")

                    # Process the transcribed text through the unified agent
                    text_request = UnifiedRequest(
                        message=transcribed_text,
                        capability=CapabilityType.VOICE_PROCESSING
                    )

                    response = await self.process_request(text_request)

                    return {
                        "transcribed_text": transcribed_text,
                        "response": response.response,
                        "model_used": response.model_used,
                        "capability_used": response.capability_used
                    }

                finally:
                    # Clean up temporary file
                    if os.path.exists(temp_audio_path):
                        os.unlink(temp_audio_path)

            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"❌ Voice processing error: {e}")
                raise HTTPException(status_code=500, detail=f"Voice processing failed: {str(e)}")
        
        @self.app.post("/tts")
        async def text_to_speech(request: dict):
            """Text-to-speech endpoint"""
            try:
                text = request.get("text", "")
                if not text:
                    raise HTTPException(status_code=400, detail="Text is required")

                # Get voice processor
                voice_proc = await get_voice_processor()

                if not voice_proc.initialized:
                    raise HTTPException(status_code=503, detail="Voice processor not available")

                # Generate audio file
                with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as temp_file:
                    output_path = temp_file.name

                success = await voice_proc.text_to_speech(text, output_path)

                if success:
                    return {
                        "success": True,
                        "message": "Audio generated successfully",
                        "audio_file": output_path
                    }
                else:
                    raise HTTPException(status_code=500, detail="Failed to generate audio")

            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"❌ TTS error: {e}")
                raise HTTPException(status_code=500, detail=f"TTS failed: {str(e)}")

        @self.app.post("/image")
        async def analyze_image(file: UploadFile = File(...), analysis_type: str = "general"):
            """Image analysis endpoint for multimodal processing"""
            try:
                # Validate file type
                if not file.content_type or not file.content_type.startswith('image/'):
                    raise HTTPException(status_code=400, detail="File must be an image")

                # Get multimodal processor
                multimodal = await get_multimodal_processor()

                if not multimodal.initialized:
                    raise HTTPException(status_code=503, detail="Multimodal processor not available")

                # Read image data
                image_data = await file.read()

                # Analyze image
                result = await multimodal.analyze_image_from_bytes(
                    image_data,
                    file.filename or "uploaded_image.jpg",
                    analysis_type
                )

                if result:
                    return {
                        "success": True,
                        "filename": file.filename,
                        "analysis_type": analysis_type,
                        "description": result.description,
                        "objects_detected": result.objects_detected,
                        "text_extracted": result.text_extracted,
                        "confidence": result.analysis_confidence,
                        "model_used": result.model_used,
                        "processing_time": result.processing_time
                    }
                else:
                    raise HTTPException(status_code=500, detail="Image analysis failed")

            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"❌ Image analysis error: {e}")
                raise HTTPException(status_code=500, detail=f"Image analysis failed: {str(e)}")
    
    async def _check_ollama_health(self) -> bool:
        """Check if Ollama server is healthy"""
        try:
            response = requests.get("http://localhost:11434/api/tags", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def _determine_capability(self, message: str, context: Optional[Dict] = None) -> CapabilityType:
        """Determine which capability to use based on message content"""
        message_lower = message.lower()
        
        # Code generation patterns
        code_patterns = ['write code', 'create script', 'generate function', 'debug', 'fix code', 
                        'python', 'javascript', 'html', 'css', 'sql', 'programming']
        if any(pattern in message_lower for pattern in code_patterns):
            return CapabilityType.CODE_GENERATION
        
        # Web search patterns  
        search_patterns = ['search', 'find information', 'look up', 'research', 'web search']
        if any(pattern in message_lower for pattern in search_patterns):
            return CapabilityType.WEB_SEARCH
        
        # Voice processing patterns
        voice_patterns = ['voice', 'speak', 'listen', 'audio', 'speech']
        if any(pattern in message_lower for pattern in voice_patterns):
            return CapabilityType.VOICE_PROCESSING
        
        # Multimodal patterns
        multimodal_patterns = ['image', 'picture', 'photo', 'document', 'pdf', 'analyze image']
        if any(pattern in message_lower for pattern in multimodal_patterns):
            return CapabilityType.MULTIMODAL_PROCESSING
        
        # Task planning patterns
        planning_patterns = ['plan', 'organize', 'schedule', 'workflow', 'steps', 'project']
        if any(pattern in message_lower for pattern in planning_patterns):
            return CapabilityType.TASK_PLANNING
        
        # Autonomous execution patterns
        autonomous_patterns = ['execute', 'run', 'perform task', 'automate', 'do this']
        if any(pattern in message_lower for pattern in autonomous_patterns):
            return CapabilityType.AUTONOMOUS_EXECUTION

        # Advanced development patterns
        advanced_dev_patterns = ['analyze code', 'review code', 'audit code', 'advanced coding',
                                'development task', 'software architecture', 'code quality']
        if any(pattern in message_lower for pattern in advanced_dev_patterns):
            return CapabilityType.ADVANCED_DEVELOPMENT

        # Advanced voice patterns
        advanced_voice_patterns = ['voice clone', 'voice synthesis', 'multilingual voice',
                                  'voice analysis', 'custom voice', 'voice characteristics']
        if any(pattern in message_lower for pattern in advanced_voice_patterns):
            return CapabilityType.ADVANCED_VOICE

        # Default to general chat
        return CapabilityType.GENERAL_CHAT
    
    def _select_model(self, capability: CapabilityType) -> str:
        """Select the best model for a given capability"""
        capability_config = self.capabilities.get(capability.value, {})
        model_key = capability_config.get("model", "fast_general")
        return model_key
    
    async def _call_ollama(self, model_name: str, prompt: str, temperature: float = 0.7, timeout: int = 120) -> str:
        """Make API call to Ollama with fallback to faster model on timeout"""
        try:
            payload = {
                "model": model_name,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": temperature
                }
            }

            response = requests.post(
                "http://localhost:11434/api/generate",
                json=payload,
                timeout=timeout
            )

            if response.status_code == 200:
                result = response.json()
                return result.get("response", "")
            else:
                logger.error(f"Ollama API error: {response.status_code}")
                return f"Error: Failed to get response from model {model_name}"

        except requests.exceptions.Timeout:
            logger.warning(f"Model {model_name} timed out, trying fallback model")
            # Fallback to faster model on timeout
            if model_name != "llama3.2:latest":
                return await self._call_ollama("llama3.2:latest", prompt, temperature, 30)
            else:
                return f"Error: Model timeout - {model_name} took too long to respond"
        except Exception as e:
            logger.error(f"Error calling Ollama: {e}")
            return f"Error: {str(e)}"
    
    async def process_request(self, request: UnifiedRequest) -> UnifiedResponse:
        """Process a unified request using appropriate capabilities"""
        start_time = time.time()
        
        try:
            # Determine capability needed
            capability = CapabilityType(request.capability) if request.capability else self._determine_capability(request.message, request.context)
            
            # Select appropriate model
            model_key = self._select_model(capability)
            model_config = self.models[model_key]
            
            logger.info(f"🎯 Processing request with capability: {capability.value}")
            logger.info(f"🤖 Using model: {model_config.name}")
            
            # Process based on capability
            response_text = await self._process_by_capability(capability, request, model_config)
            
            execution_time = time.time() - start_time
            
            return UnifiedResponse(
                response=response_text,
                capability_used=capability.value,
                model_used=model_config.name,
                execution_time=execution_time,
                success=True
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Error processing request: {e}")
            
            return UnifiedResponse(
                response=f"Error processing request: {str(e)}",
                capability_used="error",
                model_used="none",
                execution_time=execution_time,
                success=False
            )
    
    async def _process_by_capability(self, capability: CapabilityType, request: UnifiedRequest, model_config: ModelConfig) -> str:
        """Process request based on specific capability"""
        
        if capability == CapabilityType.CODE_GENERATION:
            return await self._handle_code_generation(request, model_config)
        elif capability == CapabilityType.WEB_SEARCH:
            return await self._handle_web_search(request, model_config)
        elif capability == CapabilityType.VOICE_PROCESSING:
            return await self._handle_voice_processing(request, model_config)
        elif capability == CapabilityType.MULTIMODAL_PROCESSING:
            return await self._handle_multimodal_processing(request, model_config)
        elif capability == CapabilityType.TASK_PLANNING:
            return await self._handle_task_planning(request, model_config)
        elif capability == CapabilityType.AUTONOMOUS_EXECUTION:
            return await self._handle_autonomous_execution(request, model_config)
        elif capability == CapabilityType.ADVANCED_DEVELOPMENT:
            return await self._handle_advanced_development(request, model_config)
        elif capability == CapabilityType.ADVANCED_VOICE:
            return await self._handle_advanced_voice(request, model_config)
        else:  # GENERAL_CHAT
            return await self._handle_general_chat(request, model_config)
    
    async def _handle_code_generation(self, request: UnifiedRequest, model_config: ModelConfig) -> str:
        """Handle code generation requests"""
        prompt = f"""You are an expert programmer. The user wants: {request.message}

Please provide clean, well-commented code. If creating files, specify the filename and content clearly.

User request: {request.message}"""
        
        return await self._call_ollama(model_config.name, prompt, model_config.temperature)
    
    async def _handle_web_search(self, request: UnifiedRequest, model_config: ModelConfig) -> str:
        """Handle web search requests with local web scraping"""
        try:
            # Get web search processor
            web_search = await get_web_search_processor()

            if not web_search.initialized:
                return "Web search is not available. Please ensure the web search processor is properly configured."

            # Extract search query from the request
            search_query = request.message.lower()

            # Remove common search prefixes
            search_prefixes = ['search for', 'find information about', 'look up', 'research', 'web search']
            for prefix in search_prefixes:
                if search_query.startswith(prefix):
                    search_query = search_query[len(prefix):].strip()
                    break

            logger.info(f"🔍 Performing web search for: {search_query}")

            # Perform web search
            search_results = await web_search.search_web(search_query)

            if not search_results:
                return f"I couldn't find any relevant web results for '{search_query}'. Let me provide information based on my knowledge instead."

            # Prepare context from search results
            search_context = ""
            for i, result in enumerate(search_results[:3], 1):  # Use top 3 results
                search_context += f"\nSource {i}: {result.title}\nURL: {result.url}\nContent: {result.snippet}\nRelevance: {result.relevance_score:.3f}\n"

            # Create enhanced prompt with search results
            prompt = f"""You are a helpful assistant with access to current web information. I've searched the web for information about "{search_query}" and found the following relevant results:

{search_context}

Based on these search results and your knowledge, please provide a comprehensive, accurate response to the user's request.

Guidelines:
- Synthesize information from the search results
- Cite sources when appropriate (mention the source titles/URLs)
- If the search results don't fully answer the question, supplement with your knowledge
- Be clear about what information comes from the search vs your knowledge

User's original request: {request.message}

Please provide a helpful response:"""

            return await self._call_ollama(model_config.name, prompt, model_config.temperature)

        except Exception as e:
            logger.error(f"❌ Web search error: {e}")
            return f"I encountered an error while searching the web: {str(e)}. Let me provide information based on my knowledge instead."
    
    async def _handle_voice_processing(self, request: UnifiedRequest, model_config: ModelConfig) -> str:
        """Handle voice processing requests"""

        # Check if voice processor is available
        try:
            voice_proc = await get_voice_processor()
            if not voice_proc.initialized:
                return "Voice processing is not available. Please ensure Whisper and TTS are properly installed."
        except Exception as e:
            return f"Voice processing error: {str(e)}"

        # Create a voice-optimized prompt
        prompt = f"""You are a voice-enabled AI assistant. The user has sent you a voice message that was transcribed to text.
Please provide a clear, conversational response that would work well when spoken aloud.

Keep your response:
- Conversational and natural
- Clear and easy to understand when spoken
- Appropriately paced for speech
- Friendly and engaging

User's voice message: {request.message}

Please respond in a way that's optimized for text-to-speech conversion:"""

        return await self._call_ollama(model_config.name, prompt, model_config.temperature)
    
    async def _handle_multimodal_processing(self, request: UnifiedRequest, model_config: ModelConfig) -> str:
        """Handle multimodal processing requests"""
        try:
            # Get multimodal processor
            multimodal = await get_multimodal_processor()

            if not multimodal.initialized:
                return "Multimodal processing is not available. Please ensure the vision model (qwen2.5vl:32b) is properly configured."

            # For now, return information about capabilities since we need image input
            # In a real implementation, this would handle image uploads via the API

            capabilities_info = f"""🖼️ **Multimodal Processing Capabilities Available**

I can analyze images using the local vision model ({multimodal.vision_model}). Here's what I can do:

**📋 Analysis Types:**
- **General Description**: Comprehensive scene analysis
- **Object Detection**: Identify and list objects in images
- **Text Extraction**: Read and transcribe text from images
- **Technical Analysis**: Composition, lighting, and quality assessment

**📁 Supported Formats:** {', '.join(multimodal.supported_formats)}
**🔧 Max Image Size:** {multimodal.max_image_size[0]}x{multimodal.max_image_size[1]} pixels

**💡 How to Use:**
1. Upload an image via the `/image` endpoint
2. Specify analysis type (general, objects, text, detailed, technical)
3. Get comprehensive AI-powered image analysis

**🎯 Current Status:** Ready for image analysis
**⚡ Processing:** Local vision model - no external APIs needed

To analyze an image, please use the image upload endpoint or describe what you'd like me to help you with regarding image analysis."""

            return capabilities_info

        except Exception as e:
            logger.error(f"❌ Multimodal processing error: {e}")
            return f"I encountered an error with multimodal processing: {str(e)}. The vision model may need to be loaded."
    
    async def _handle_task_planning(self, request: UnifiedRequest, model_config: ModelConfig) -> str:
        """Handle task planning requests"""
        prompt = f"""You are an expert project manager and task planner. The user wants help with: {request.message}

Please create a detailed, step-by-step plan. Include:
1. Clear objectives
2. Specific steps with priorities
3. Dependencies between tasks
4. Estimated timeframes
5. Resources needed

User request: {request.message}"""
        
        return await self._call_ollama(model_config.name, prompt, model_config.temperature)
    
    async def _handle_autonomous_execution(self, request: UnifiedRequest, model_config: ModelConfig) -> str:
        """Handle autonomous execution requests"""
        prompt = f"""You are an autonomous AI agent capable of planning and executing tasks. The user wants: {request.message}

Please:
1. Analyze what needs to be done
2. Create a step-by-step execution plan
3. Identify what actions you can take
4. Explain what you would do and in what order

User request: {request.message}"""
        
        return await self._call_ollama(model_config.name, prompt, model_config.temperature)
    
    async def _handle_general_chat(self, request: UnifiedRequest, model_config: ModelConfig) -> str:
        """Handle general chat requests"""
        prompt = f"""You are a helpful AI assistant. Please respond naturally and helpfully to the user's message.

User message: {request.message}"""

        return await self._call_ollama(model_config.name, prompt, model_config.temperature)

    async def _handle_advanced_development(self, request: UnifiedRequest, model_config: ModelConfig) -> str:
        """Handle advanced development requests using OpenHands"""
        try:
            # Use OpenHands service for advanced development tasks
            context = request.context or {}

            # Determine the type of development task
            if any(keyword in request.message.lower() for keyword in ['analyze', 'review', 'audit']):
                # Code analysis task
                result = await self.openhands_service.analyze_code(request.message, context.get('language', 'python'))
                return f"Code Analysis Results:\n{json.dumps(result, indent=2)}"

            elif any(keyword in request.message.lower() for keyword in ['generate', 'create', 'build', 'develop']):
                # Advanced code generation
                result = await self.openhands_service.generate_advanced_code(request.message, context.get('language', 'python'))
                return f"Generated Code:\n```{context.get('language', 'python')}\n{result['code']}\n```\n\nExplanation: {result['explanation']}"

            else:
                # General development task
                result = await self.openhands_service.execute_development_task(request.message, context)
                return f"Development Task Result:\n{result['result']}\n\nStatus: {result['status']}"

        except Exception as e:
            logger.error(f"Error in advanced development: {e}")
            return f"Advanced development error: {str(e)}"

    async def _handle_advanced_voice(self, request: UnifiedRequest, model_config: ModelConfig) -> str:
        """Handle advanced voice requests using OpenVoice"""
        try:
            context = request.context or {}

            # Determine the type of voice task
            if any(keyword in request.message.lower() for keyword in ['clone', 'mimic', 'copy']):
                # Voice cloning task
                if 'reference_audio' in context:
                    result = await self.openvoice_service.clone_voice(
                        context['reference_audio'],
                        request.message,
                        context.get('language', 'english')
                    )
                    return f"Voice Cloning Result:\nStatus: {result['status']}\nLanguage: {result['language']}\nQuality Score: {result['quality_score']}"
                else:
                    return "Voice cloning requires reference audio. Please provide reference_audio in context."

            elif any(keyword in request.message.lower() for keyword in ['synthesize', 'speak', 'generate']):
                # Multilingual synthesis
                language = context.get('language', 'english')
                voice_style = context.get('voice_style', 'neutral')
                result = await self.openvoice_service.synthesize_multilingual(request.message, language, voice_style)
                return f"Voice Synthesis Result:\nStatus: {result['status']}\nLanguage: {result['language']}\nDuration: {result['duration']}s\nQuality: {result['quality']}"

            elif any(keyword in request.message.lower() for keyword in ['analyze', 'examine', 'identify']):
                # Voice analysis
                if 'audio_data' in context:
                    result = await self.openvoice_service.analyze_voice_characteristics(context['audio_data'])
                    return f"Voice Analysis Result:\n{json.dumps(result, indent=2)}"
                else:
                    return "Voice analysis requires audio data. Please provide audio_data in context."

            else:
                # General voice processing with enhanced capabilities
                result = await self.openvoice_service.synthesize_multilingual(
                    request.message,
                    context.get('language', 'english'),
                    context.get('voice_style', 'neutral')
                )
                return f"Enhanced Voice Processing:\nProcessed: {request.message}\nLanguage: {result['language']}\nStatus: {result['status']}"

        except Exception as e:
            logger.error(f"Error in advanced voice: {e}")
            return f"Advanced voice error: {str(e)}"

# Global agent instance
agent = None

def create_agent() -> UnifiedLocalAgent:
    """Create and return the unified agent instance"""
    global agent
    if agent is None:
        agent = UnifiedLocalAgent()
    return agent

if __name__ == "__main__":
    import uvicorn
    
    # Create agent instance
    unified_agent = create_agent()
    
    # Run the server
    print("🚀 Starting Unified Local AI Agent on http://localhost:9001")
    print("📚 Available capabilities:")
    for cap in CapabilityType:
        print(f"   - {cap.value}")
    
    uvicorn.run(
        unified_agent.app,
        host="0.0.0.0",
        port=9001,
        log_level="info"
    )
