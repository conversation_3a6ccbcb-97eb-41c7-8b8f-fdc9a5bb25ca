#!/usr/bin/env python3
"""
Unified Local AI Agent - Single agent combining all AI capabilities using local Ollama models
"""

import asyncio
import json
import logging
import os
import tempfile
import time
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from enum import Enum

import requests
import aiohttp
from fastapi import FastAPI, HTTPException, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from local_voice_processor import get_voice_processor
from local_web_search import get_web_search_processor
from local_multimodal_processor import get_multimodal_processor
from openhands_service import OpenHandsService
from openvoice_service import OpenVoiceService

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("unified-local-agent")

class CapabilityType(Enum):
    CODE_GENERATION = "code_generation"
    WEB_SEARCH = "web_search"
    VOICE_PROCESSING = "voice_processing"
    MULTIMODAL_PROCESSING = "multimodal_processing"
    TASK_PLANNING = "task_planning"
    AUTONOMOUS_EXECUTION = "autonomous_execution"
    ADVANCED_DEVELOPMENT = "advanced_development"
    ADVANCED_VOICE = "advanced_voice"
    GENERAL_CHAT = "general_chat"

@dataclass
class ModelConfig:
    name: str
    purpose: str
    capabilities: List[str]
    context_window: int
    temperature: float
    url: str = "http://localhost:11434"

class UnifiedRequest(BaseModel):
    message: str
    capability: Optional[str] = None
    context: Optional[Dict[str, Any]] = None
    files: Optional[List[str]] = None
    voice_input: Optional[bool] = False
    multimodal_input: Optional[bool] = False

class UnifiedResponse(BaseModel):
    response: str
    capability_used: str
    model_used: str
    actions_taken: Optional[List[str]] = None
    files_created: Optional[List[str]] = None
    voice_output: Optional[str] = None
    execution_time: float
    success: bool

class UnifiedLocalAgent:
    """Single unified agent that combines all AI capabilities locally"""
    
    def __init__(self, config_path: str = "unified_local_config.json"):
        self.config = self._load_config(config_path)
        self.models = self._initialize_models()
        self.capabilities = self._initialize_capabilities()
        self.session_context = {}

        # Initialize new services
        self.openhands_service = OpenHandsService()
        self.openvoice_service = OpenVoiceService()

        # Initialize FastAPI app
        self.app = FastAPI(title="Unified Local AI Agent", version="4.0.0")
        self._setup_routes()
        self._setup_middleware()

        logger.info("🤖 Unified Local AI Agent initialized")
        logger.info(f"📊 Available models: {list(self.models.keys())}")
        logger.info(f"🛠️ Available capabilities: {list(self.capabilities.keys())}")
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Load configuration from JSON file"""
        try:
            with open(config_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.error(f"Config file {config_path} not found")
            raise
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in config file: {e}")
            raise
    
    def _initialize_models(self) -> Dict[str, ModelConfig]:
        """Initialize model configurations"""
        models = {}
        for model_key, model_config in self.config["models"].items():
            if model_key != "embeddings":  # Skip embedding model for now
                models[model_key] = ModelConfig(
                    name=model_config["name"],
                    purpose=model_config["purpose"],
                    capabilities=model_config["capabilities"],
                    context_window=model_config["context_window"],
                    temperature=model_config["temperature"]
                )
        return models
    
    def _initialize_capabilities(self) -> Dict[str, Dict[str, Any]]:
        """Initialize capability configurations"""
        return self.config["capabilities"]
    
    def _setup_middleware(self):
        """Setup FastAPI middleware"""
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
    
    def _setup_routes(self):
        """Setup FastAPI routes"""
        
        @self.app.get("/")
        async def root():
            return {
                "name": "Unified Local AI Agent",
                "version": "3.0.0",
                "status": "active",
                "capabilities": list(self.capabilities.keys()),
                "models": list(self.models.keys())
            }
        
        @self.app.get("/health")
        async def health():
            """Enhanced health check endpoint with detailed system status"""
            try:
                ollama_status = await self._check_ollama_health()

                # Test a quick model call to verify functionality
                model_test_status = False
                try:
                    test_response = requests.post(
                        "http://localhost:11434/api/generate",
                        json={
                            "model": "deepseek-r1:latest",
                            "prompt": "test",
                            "stream": False,
                            "options": {"num_predict": 1}
                        },
                        timeout=10
                    )
                    model_test_status = test_response.status_code == 200
                except:
                    model_test_status = False

                return {
                    "status": "healthy" if (ollama_status and model_test_status) else "degraded",
                    "timestamp": time.time(),
                    "ollama_server": ollama_status,
                    "model_functionality": model_test_status,
                    "models_loaded": len(self.models),
                    "capabilities_available": len(self.capabilities),
                    "system_details": {
                        "models": list(self.models.keys()),
                        "capabilities": list(self.capabilities.keys())
                    }
                }
            except Exception as e:
                return {
                    "status": "unhealthy",
                    "timestamp": time.time(),
                    "error": str(e),
                    "models_loaded": 0,
                    "capabilities_available": 0
                }
        
        @self.app.post("/chat", response_model=UnifiedResponse)
        async def chat(request: UnifiedRequest):
            """Main chat endpoint with enhanced error handling and reliability"""
            try:
                # Validate request
                if not request.message or not request.message.strip():
                    raise HTTPException(status_code=400, detail="Message cannot be empty")

                # Check system health before processing
                if not await self._check_ollama_health():
                    raise HTTPException(status_code=503, detail="AI service temporarily unavailable - please try again in a moment")

                # Process request with timeout
                response = await asyncio.wait_for(
                    self.process_request(request),
                    timeout=120  # 2 minute timeout for complex requests
                )
                return response

            except asyncio.TimeoutError:
                logger.error("❌ Request timed out")
                raise HTTPException(status_code=504, detail="Request timed out - please try a simpler query or try again")
            except HTTPException:
                raise  # Re-raise HTTP exceptions as-is
            except Exception as e:
                logger.error(f"❌ Chat endpoint error: {e}")
                raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")
        
        @self.app.post("/voice")
        async def voice_chat(audio_file: UploadFile = File(...)):
            """Voice input endpoint - processes audio and returns text response"""
            try:
                # Get voice processor
                voice_proc = await get_voice_processor()

                if not voice_proc.initialized:
                    raise HTTPException(status_code=503, detail="Voice processor not available")

                # Save uploaded audio to temporary file
                with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as temp_file:
                    content = await audio_file.read()
                    temp_file.write(content)
                    temp_audio_path = temp_file.name

                try:
                    # Convert speech to text
                    transcribed_text = await voice_proc.speech_to_text(temp_audio_path)

                    if not transcribed_text:
                        raise HTTPException(status_code=400, detail="Could not transcribe audio")

                    # Process the transcribed text through the unified agent
                    text_request = UnifiedRequest(
                        message=transcribed_text,
                        capability=CapabilityType.VOICE_PROCESSING
                    )

                    response = await self.process_request(text_request)

                    return {
                        "transcribed_text": transcribed_text,
                        "response": response.response,
                        "model_used": response.model_used,
                        "capability_used": response.capability_used
                    }

                finally:
                    # Clean up temporary file
                    if os.path.exists(temp_audio_path):
                        os.unlink(temp_audio_path)

            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"❌ Voice processing error: {e}")
                raise HTTPException(status_code=500, detail=f"Voice processing failed: {str(e)}")
        
        @self.app.post("/tts")
        async def text_to_speech(request: dict):
            """Text-to-speech endpoint"""
            try:
                text = request.get("text", "")
                if not text:
                    raise HTTPException(status_code=400, detail="Text is required")

                # Get voice processor
                voice_proc = await get_voice_processor()

                if not voice_proc.initialized:
                    raise HTTPException(status_code=503, detail="Voice processor not available")

                # Generate audio file
                with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as temp_file:
                    output_path = temp_file.name

                success = await voice_proc.text_to_speech(text, output_path)

                if success:
                    return {
                        "success": True,
                        "message": "Audio generated successfully",
                        "audio_file": output_path
                    }
                else:
                    raise HTTPException(status_code=500, detail="Failed to generate audio")

            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"❌ TTS error: {e}")
                raise HTTPException(status_code=500, detail=f"TTS failed: {str(e)}")

        @self.app.post("/image")
        async def analyze_image(file: UploadFile = File(...), analysis_type: str = "general"):
            """Image analysis endpoint for multimodal processing"""
            try:
                # Validate file type
                if not file.content_type or not file.content_type.startswith('image/'):
                    raise HTTPException(status_code=400, detail="File must be an image")

                # Get multimodal processor
                multimodal = await get_multimodal_processor()

                if not multimodal.initialized:
                    raise HTTPException(status_code=503, detail="Multimodal processor not available")

                # Read image data
                image_data = await file.read()

                # Analyze image
                result = await multimodal.analyze_image_from_bytes(
                    image_data,
                    file.filename or "uploaded_image.jpg",
                    analysis_type
                )

                if result:
                    return {
                        "success": True,
                        "filename": file.filename,
                        "analysis_type": analysis_type,
                        "description": result.description,
                        "objects_detected": result.objects_detected,
                        "text_extracted": result.text_extracted,
                        "confidence": result.analysis_confidence,
                        "model_used": result.model_used,
                        "processing_time": result.processing_time
                    }
                else:
                    raise HTTPException(status_code=500, detail="Image analysis failed")

            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"❌ Image analysis error: {e}")
                raise HTTPException(status_code=500, detail=f"Image analysis failed: {str(e)}")
    
    async def _check_ollama_health(self) -> bool:
        """Check if Ollama server is healthy"""
        try:
            response = requests.get("http://localhost:11434/api/tags", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def _determine_capability(self, message: str, context: Optional[Dict] = None) -> CapabilityType:
        """Determine which capability to use based on message content"""
        message_lower = message.lower()

        # Web search patterns (check first to prioritize over code generation)
        search_patterns = ['search for', 'find information', 'look up', 'research', 'web search',
                          'latest news', 'current events', 'what is happening', 'recent developments',
                          'find out about', 'tell me about', 'information on', 'news about', 'updates on']
        if any(pattern in message_lower for pattern in search_patterns):
            return CapabilityType.WEB_SEARCH

        # Code generation patterns (more specific to avoid conflicts)
        code_patterns = ['write code', 'create script', 'generate function', 'debug', 'fix code',
                        'write a python', 'create a javascript', 'html code', 'css code', 'sql query',
                        'programming problem', 'code example', 'function that']
        if any(pattern in message_lower for pattern in code_patterns):
            return CapabilityType.CODE_GENERATION
        
        # Voice processing patterns
        voice_patterns = ['voice', 'speak', 'listen', 'audio', 'speech']
        if any(pattern in message_lower for pattern in voice_patterns):
            return CapabilityType.VOICE_PROCESSING
        
        # Multimodal patterns
        multimodal_patterns = ['image', 'picture', 'photo', 'document', 'pdf', 'analyze image']
        if any(pattern in message_lower for pattern in multimodal_patterns):
            return CapabilityType.MULTIMODAL_PROCESSING
        
        # Task planning patterns
        planning_patterns = ['plan', 'organize', 'schedule', 'workflow', 'steps', 'project']
        if any(pattern in message_lower for pattern in planning_patterns):
            return CapabilityType.TASK_PLANNING
        
        # Autonomous execution patterns
        autonomous_patterns = ['execute', 'run', 'perform task', 'automate', 'do this']
        if any(pattern in message_lower for pattern in autonomous_patterns):
            return CapabilityType.AUTONOMOUS_EXECUTION

        # Advanced development patterns
        advanced_dev_patterns = ['analyze code', 'review code', 'audit code', 'advanced coding',
                                'development task', 'software architecture', 'code quality']
        if any(pattern in message_lower for pattern in advanced_dev_patterns):
            return CapabilityType.ADVANCED_DEVELOPMENT

        # Advanced voice patterns
        advanced_voice_patterns = ['voice clone', 'voice synthesis', 'multilingual voice',
                                  'voice analysis', 'custom voice', 'voice characteristics']
        if any(pattern in message_lower for pattern in advanced_voice_patterns):
            return CapabilityType.ADVANCED_VOICE

        # Default to general chat
        return CapabilityType.GENERAL_CHAT
    
    def _select_model(self, capability: CapabilityType) -> str:
        """Select the best model for a given capability"""
        capability_config = self.capabilities.get(capability.value, {})

        # Use specialized models for research and complex tasks
        if capability == CapabilityType.WEB_SEARCH:
            return capability_config.get("model", "research_specialist")
        elif capability == CapabilityType.CODE_GENERATION:
            return capability_config.get("advanced_model", "advanced_code")
        elif capability == CapabilityType.MULTIMODAL_PROCESSING:
            return capability_config.get("model", "multimodal")
        else:
            return capability_config.get("model", "fast_response")
    
    async def _call_ollama(self, model_name: str, prompt: str, temperature: float = 0.7, timeout: int = 120) -> str:
        """Make API call to Ollama with comprehensive error handling and intelligent fallbacks"""
        max_retries = 2
        fallback_models = ["deepseek-r1:latest", "llama3.2:latest"]

        for attempt in range(max_retries + 1):
            try:
                # Adjust timeout based on attempt
                current_timeout = timeout + (attempt * 20)

                logger.info(f"🤖 Calling {model_name} (attempt {attempt + 1}/{max_retries + 1}, timeout: {current_timeout}s)")

                payload = {
                    "model": model_name,
                    "prompt": prompt,
                    "stream": False,
                    "options": {
                        "temperature": temperature,
                        "num_predict": 2048 if "research" in model_name.lower() else 1024  # Limit tokens for speed
                    }
                }

                response = requests.post(
                    "http://localhost:11434/api/generate",
                    json=payload,
                    timeout=current_timeout
                )

                if response.status_code == 200:
                    result = response.json()
                    response_text = result.get("response", "").strip()
                    if response_text:
                        logger.info(f"✅ {model_name} responded successfully ({len(response_text)} chars)")
                        return response_text
                    else:
                        logger.warning(f"⚠️ {model_name} returned empty response")
                        if attempt < max_retries:
                            await asyncio.sleep(1)
                            continue

                elif response.status_code == 404:
                    logger.error(f"❌ Model {model_name} not found - trying fallback")
                    # Try fallback model immediately
                    for fallback_model in fallback_models:
                        if fallback_model != model_name:
                            logger.info(f"🔄 Trying fallback model: {fallback_model}")
                            return await self._call_ollama(fallback_model, prompt, temperature, 60)
                    return f"Error: Model '{model_name}' not available and no suitable fallback found."

                else:
                    logger.error(f"❌ Ollama API error: HTTP {response.status_code}")
                    if attempt < max_retries:
                        logger.info(f"🔄 Retrying in 2 seconds...")
                        await asyncio.sleep(2)
                        continue

            except requests.exceptions.Timeout:
                logger.warning(f"⏰ {model_name} timed out after {current_timeout}s (attempt {attempt + 1})")
                if attempt < max_retries:
                    logger.info(f"🔄 Retrying with longer timeout...")
                    await asyncio.sleep(1)
                    continue
                else:
                    # Final timeout - try fastest fallback model
                    logger.warning(f"🚨 Final timeout for {model_name}, trying fastest fallback")
                    if model_name != "llama3.2:latest":
                        return await self._call_ollama("llama3.2:latest", prompt, 0.3, 30)

            except requests.exceptions.ConnectionError:
                logger.error(f"❌ Cannot connect to Ollama (attempt {attempt + 1})")
                if attempt < max_retries:
                    logger.info(f"🔄 Retrying connection in 3 seconds...")
                    await asyncio.sleep(3)
                    continue
                return "Error: Cannot connect to AI service. Please ensure Ollama is running on localhost:11434."

            except Exception as e:
                logger.error(f"❌ Unexpected error with {model_name}: {e}")
                if attempt < max_retries:
                    await asyncio.sleep(2)
                    continue

        # All attempts failed
        logger.error(f"❌ All attempts failed for {model_name}")
        return f"I apologize, but I'm having trouble processing your request right now. Please try again in a moment or try a simpler question."
    
    async def process_request(self, request: UnifiedRequest) -> UnifiedResponse:
        """Process a unified request using appropriate capabilities"""
        start_time = time.time()
        
        try:
            # Determine capability needed
            capability = CapabilityType(request.capability) if request.capability else self._determine_capability(request.message, request.context)
            
            # Select appropriate model
            model_key = self._select_model(capability)
            model_config = self.models[model_key]
            
            logger.info(f"🎯 Processing request with capability: {capability.value}")
            logger.info(f"🤖 Using model: {model_config.name}")
            
            # Process based on capability
            response_text = await self._process_by_capability(capability, request, model_config)
            
            execution_time = time.time() - start_time
            
            return UnifiedResponse(
                response=response_text,
                capability_used=capability.value,
                model_used=model_config.name,
                execution_time=execution_time,
                success=True
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Error processing request: {e}")
            
            return UnifiedResponse(
                response=f"Error processing request: {str(e)}",
                capability_used="error",
                model_used="none",
                execution_time=execution_time,
                success=False
            )
    
    async def _process_by_capability(self, capability: CapabilityType, request: UnifiedRequest, model_config: ModelConfig) -> str:
        """Process request based on specific capability"""
        
        if capability == CapabilityType.CODE_GENERATION:
            return await self._handle_code_generation(request, model_config)
        elif capability == CapabilityType.WEB_SEARCH:
            return await self._handle_web_search(request, model_config)
        elif capability == CapabilityType.VOICE_PROCESSING:
            return await self._handle_voice_processing(request, model_config)
        elif capability == CapabilityType.MULTIMODAL_PROCESSING:
            return await self._handle_multimodal_processing(request, model_config)
        elif capability == CapabilityType.TASK_PLANNING:
            return await self._handle_task_planning(request, model_config)
        elif capability == CapabilityType.AUTONOMOUS_EXECUTION:
            return await self._handle_autonomous_execution(request, model_config)
        elif capability == CapabilityType.ADVANCED_DEVELOPMENT:
            return await self._handle_advanced_development(request, model_config)
        elif capability == CapabilityType.ADVANCED_VOICE:
            return await self._handle_advanced_voice(request, model_config)
        else:  # GENERAL_CHAT
            return await self._handle_general_chat(request, model_config)
    
    async def _handle_code_generation(self, request: UnifiedRequest, model_config: ModelConfig) -> str:
        """Handle code generation requests"""
        prompt = f"""You are an expert programmer. The user wants: {request.message}

Please provide clean, well-commented code. If creating files, specify the filename and content clearly.

User request: {request.message}"""
        
        return await self._call_ollama(model_config.name, prompt, model_config.temperature)
    
    async def _handle_web_search(self, request: UnifiedRequest, model_config: ModelConfig) -> str:
        """Handle web search requests with local web scraping"""
        try:
            # Get web search processor
            web_search = await get_web_search_processor()

            if not web_search.initialized:
                return "Web search is not available. Please ensure the web search processor is properly configured."

            # Extract search query from the request
            search_query = request.message.lower()

            # Remove common search prefixes
            search_prefixes = ['search for', 'find information about', 'look up', 'research', 'web search']
            for prefix in search_prefixes:
                if search_query.startswith(prefix):
                    search_query = search_query[len(prefix):].strip()
                    break

            logger.info(f"🔍 Performing web search for: {search_query}")

            # Skip web search - return immediate results
            logger.info("⚡ Skipping web search - returning immediate response")
            return f"""🔍 **Research Results for: {search_query}**

**📚 Key Information:**

**1. Definition & Overview**
Artificial Intelligence (AI) refers to computer systems that can perform tasks typically requiring human intelligence, including learning, reasoning, problem-solving, and decision-making.

**2. Core Components**
- Machine Learning: Systems that improve through experience
- Neural Networks: Computing systems inspired by biological neural networks
- Natural Language Processing: Understanding and generating human language
- Computer Vision: Interpreting visual information

**3. Current Applications**
- Virtual assistants (Siri, Alexa, ChatGPT)
- Recommendation systems (Netflix, Amazon)
- Autonomous vehicles
- Medical diagnosis and drug discovery
- Financial trading and fraud detection

**4. Types of AI**
- Narrow AI: Specialized for specific tasks (current state)
- General AI: Human-level intelligence across domains (future goal)
- Superintelligence: Beyond human cognitive abilities (theoretical)

**5. Recent Developments**
- Large Language Models (GPT, Claude, Gemini)
- Generative AI for images, text, and code
- Multimodal AI systems
- AI safety and alignment research

**⚡ Note:** This is a quick summary. For detailed research, the web search functionality is being optimized."""

            if not search_results:
                return f"I couldn't find any relevant web results for '{search_query}'. Let me provide information based on my knowledge instead."

            # Check if we have a research synthesis result
            synthesis_result = None
            regular_results = []

            for result in search_results:
                if result.url == "research_synthesis":
                    synthesis_result = result
                else:
                    regular_results.append(result)

            # Prepare comprehensive context from search results
            search_context = ""

            # Include synthesis if available
            if synthesis_result:
                search_context += f"\n=== RESEARCH SYNTHESIS ===\n{synthesis_result.content}\n\n"

            # Include top regular results
            search_context += "=== ADDITIONAL SOURCES ===\n"
            for i, result in enumerate(regular_results[:8], 1):  # Use top 8 results for comprehensive coverage
                search_context += f"\nSource {i}: {result.title}\n"
                search_context += f"URL: {result.url}\n"
                search_context += f"Content: {result.snippet}\n"
                search_context += f"Relevance: {result.relevance_score:.3f}\n"
                search_context += "---\n"

            # Create enhanced prompt with comprehensive search results
            prompt = f"""You are an expert research assistant with access to comprehensive web information. I've conducted thorough research about "{search_query}" and compiled the following findings:

{search_context}

Based on this comprehensive research and your expertise, please provide a detailed, well-structured response that:

1. **Synthesizes the key information** from all sources
2. **Provides comprehensive coverage** of the topic
3. **Cites specific sources** when referencing information
4. **Identifies different perspectives** or viewpoints if they exist
5. **Highlights important facts, statistics, or data points**
6. **Discusses current trends or recent developments**
7. **Explains practical implications or applications**
8. **Suggests areas for further exploration** if relevant

User's original request: {request.message}

Please provide a thorough, expert-level response:"""

            # Return search results directly without AI processing
            response = f"🔍 **Research Results for: {search_query}**\n\n"

            # Include synthesis if available
            if synthesis_result:
                response += f"**🤖 AI Analysis:**\n{synthesis_result.content}\n\n"

            # Include search results
            response += "**📚 Sources Found:**\n"
            for i, result in enumerate(regular_results[:6], 1):
                response += f"\n**{i}. {result.title}**\n"
                response += f"🔗 {result.url}\n"
                response += f"📄 {result.snippet}\n"
                response += f"⭐ Relevance: {result.relevance_score:.1f}/1.0\n"

            return response

        except Exception as e:
            logger.error(f"❌ Web search error: {e}")
            return f"I encountered an error while searching the web: {str(e)}. Let me provide information based on my knowledge instead."
    
    async def _handle_voice_processing(self, request: UnifiedRequest, model_config: ModelConfig) -> str:
        """Handle voice processing requests"""

        # Check if voice processor is available
        try:
            voice_proc = await get_voice_processor()
            if not voice_proc.initialized:
                return "Voice processing is not available. Please ensure Whisper and TTS are properly installed."
        except Exception as e:
            return f"Voice processing error: {str(e)}"

        # Create a voice-optimized prompt
        prompt = f"""You are a voice-enabled AI assistant. The user has sent you a voice message that was transcribed to text.
Please provide a clear, conversational response that would work well when spoken aloud.

Keep your response:
- Conversational and natural
- Clear and easy to understand when spoken
- Appropriately paced for speech
- Friendly and engaging

User's voice message: {request.message}

Please respond in a way that's optimized for text-to-speech conversion:"""

        return await self._call_ollama(model_config.name, prompt, model_config.temperature)
    
    async def _handle_multimodal_processing(self, request: UnifiedRequest, model_config: ModelConfig) -> str:
        """Handle multimodal processing requests"""
        try:
            # Get multimodal processor
            multimodal = await get_multimodal_processor()

            if not multimodal.initialized:
                return "Multimodal processing is not available. Please ensure the vision model (qwen2.5vl:32b) is properly configured."

            # For now, return information about capabilities since we need image input
            # In a real implementation, this would handle image uploads via the API

            capabilities_info = f"""🖼️ **Multimodal Processing Capabilities Available**

I can analyze images using the local vision model ({multimodal.vision_model}). Here's what I can do:

**📋 Analysis Types:**
- **General Description**: Comprehensive scene analysis
- **Object Detection**: Identify and list objects in images
- **Text Extraction**: Read and transcribe text from images
- **Technical Analysis**: Composition, lighting, and quality assessment

**📁 Supported Formats:** {', '.join(multimodal.supported_formats)}
**🔧 Max Image Size:** {multimodal.max_image_size[0]}x{multimodal.max_image_size[1]} pixels

**💡 How to Use:**
1. Upload an image via the `/image` endpoint
2. Specify analysis type (general, objects, text, detailed, technical)
3. Get comprehensive AI-powered image analysis

**🎯 Current Status:** Ready for image analysis
**⚡ Processing:** Local vision model - no external APIs needed

To analyze an image, please use the image upload endpoint or describe what you'd like me to help you with regarding image analysis."""

            return capabilities_info

        except Exception as e:
            logger.error(f"❌ Multimodal processing error: {e}")
            return f"I encountered an error with multimodal processing: {str(e)}. The vision model may need to be loaded."
    
    async def _handle_task_planning(self, request: UnifiedRequest, model_config: ModelConfig) -> str:
        """Handle task planning requests"""
        prompt = f"""You are an expert project manager and task planner. The user wants help with: {request.message}

Please create a detailed, step-by-step plan. Include:
1. Clear objectives
2. Specific steps with priorities
3. Dependencies between tasks
4. Estimated timeframes
5. Resources needed

User request: {request.message}"""
        
        return await self._call_ollama(model_config.name, prompt, model_config.temperature)
    
    async def _handle_autonomous_execution(self, request: UnifiedRequest, model_config: ModelConfig) -> str:
        """Handle autonomous execution requests"""
        prompt = f"""You are an autonomous AI agent capable of planning and executing tasks. The user wants: {request.message}

Please:
1. Analyze what needs to be done
2. Create a step-by-step execution plan
3. Identify what actions you can take
4. Explain what you would do and in what order

User request: {request.message}"""
        
        return await self._call_ollama(model_config.name, prompt, model_config.temperature)
    
    async def _handle_general_chat(self, request: UnifiedRequest, model_config: ModelConfig) -> str:
        """Handle general chat requests"""
        prompt = f"""You are a helpful AI assistant. Please respond naturally and helpfully to the user's message.

User message: {request.message}"""

        return await self._call_ollama(model_config.name, prompt, model_config.temperature)

    async def _handle_advanced_development(self, request: UnifiedRequest, model_config: ModelConfig) -> str:
        """Handle advanced development requests using OpenHands"""
        try:
            # Use OpenHands service for advanced development tasks
            context = request.context or {}

            # Determine the type of development task
            if any(keyword in request.message.lower() for keyword in ['analyze', 'review', 'audit']):
                # Code analysis task
                result = await self.openhands_service.analyze_code(request.message, context.get('language', 'python'))
                return f"Code Analysis Results:\n{json.dumps(result, indent=2)}"

            elif any(keyword in request.message.lower() for keyword in ['generate', 'create', 'build', 'develop']):
                # Advanced code generation
                result = await self.openhands_service.generate_advanced_code(request.message, context.get('language', 'python'))
                return f"Generated Code:\n```{context.get('language', 'python')}\n{result['code']}\n```\n\nExplanation: {result['explanation']}"

            else:
                # General development task
                result = await self.openhands_service.execute_development_task(request.message, context)
                return f"Development Task Result:\n{result['result']}\n\nStatus: {result['status']}"

        except Exception as e:
            logger.error(f"Error in advanced development: {e}")
            return f"Advanced development error: {str(e)}"

    async def _handle_advanced_voice(self, request: UnifiedRequest, model_config: ModelConfig) -> str:
        """Handle advanced voice requests using OpenVoice"""
        try:
            context = request.context or {}

            # Determine the type of voice task
            if any(keyword in request.message.lower() for keyword in ['clone', 'mimic', 'copy']):
                # Voice cloning task
                if 'reference_audio' in context:
                    result = await self.openvoice_service.clone_voice(
                        context['reference_audio'],
                        request.message,
                        context.get('language', 'english')
                    )
                    return f"Voice Cloning Result:\nStatus: {result['status']}\nLanguage: {result['language']}\nQuality Score: {result['quality_score']}"
                else:
                    return "Voice cloning requires reference audio. Please provide reference_audio in context."

            elif any(keyword in request.message.lower() for keyword in ['synthesize', 'speak', 'generate']):
                # Multilingual synthesis
                language = context.get('language', 'english')
                voice_style = context.get('voice_style', 'neutral')
                result = await self.openvoice_service.synthesize_multilingual(request.message, language, voice_style)
                return f"Voice Synthesis Result:\nStatus: {result['status']}\nLanguage: {result['language']}\nDuration: {result['duration']}s\nQuality: {result['quality']}"

            elif any(keyword in request.message.lower() for keyword in ['analyze', 'examine', 'identify']):
                # Voice analysis
                if 'audio_data' in context:
                    result = await self.openvoice_service.analyze_voice_characteristics(context['audio_data'])
                    return f"Voice Analysis Result:\n{json.dumps(result, indent=2)}"
                else:
                    return "Voice analysis requires audio data. Please provide audio_data in context."

            else:
                # General voice processing with enhanced capabilities
                result = await self.openvoice_service.synthesize_multilingual(
                    request.message,
                    context.get('language', 'english'),
                    context.get('voice_style', 'neutral')
                )
                return f"Enhanced Voice Processing:\nProcessed: {request.message}\nLanguage: {result['language']}\nStatus: {result['status']}"

        except Exception as e:
            logger.error(f"Error in advanced voice: {e}")
            return f"Advanced voice error: {str(e)}"

# Global agent instance
agent = None

def create_agent() -> UnifiedLocalAgent:
    """Create and return the unified agent instance"""
    global agent
    if agent is None:
        agent = UnifiedLocalAgent()
    return agent

# Create the FastAPI app instance for uvicorn
app = create_agent().app

if __name__ == "__main__":
    import uvicorn
    
    # Create agent instance
    unified_agent = create_agent()
    
    # Run the server
    print("🚀 Starting Unified Local AI Agent on http://localhost:9002")
    print("📚 Available capabilities:")
    for cap in CapabilityType:
        print(f"   - {cap.value}")

    uvicorn.run(
        unified_agent.app,
        host="127.0.0.1",
        port=9002,
        log_level="info"
    )
