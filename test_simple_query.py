#!/usr/bin/env python3
"""
Simple test for unified bridge
"""
import requests
import json

def test_agentic_seek_query():
    """Test AgenticSeek specific query"""
    try:
        payload = {
            "query": "search for latest AI news",
            "system_preference": "agentic-seek"
        }
        
        response = requests.post(
            "http://localhost:9000/query",
            headers={"Content-Type": "application/json"},
            json=payload,
            timeout=30
        )
        
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
        
        return response.status_code == 200
    except Exception as e:
        print(f"Query failed: {e}")
        return False

if __name__ == "__main__":
    print("Testing AgenticSeek query...")
    test_agentic_seek_query()
