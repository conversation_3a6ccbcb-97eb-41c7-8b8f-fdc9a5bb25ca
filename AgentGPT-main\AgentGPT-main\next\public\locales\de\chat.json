{"EXPERIENCING_EXCEPTIONAL_TRAFFIC": "🚨 Wir haben außergewöhnlichen Verkehr, erwarten Sie Verzögerungen und Ausfälle, wenn Sie keinen eigenen API-Schlüssel verwenden 🚨", "CREATE_AN_AGENT_DESCRIPTION": "<PERSON><PERSON><PERSON>n Sie einen Agenten, indem Sie einen Namen/<PERSON><PERSON> hi<PERSON> und auf Bereitstellen klicken!", "YOU_CAN_PROVIDE_YOUR_API_KEY": "📢 Sie können Ihren eigenen OpenAI API-Schlüssel im Einstellungen-Tab für höhere Limits bereitstellen!", "HELP_SUPPORT_THE_ADVANCEMENT_OF_AGENTGPT": "💝️ Unterstützen Sie die Weiterentwicklung von AgentGPT. 💝️", "CONSIDER_SPONSORING_ON_GITHUB": "<PERSON>te erwägen Sie, das Projekt auf GitHub zu unterstützen.", "SUPPORT_NOW": "<PERSON>zt unterstützen 🚀", "EMBARKING_ON_NEW_GOAL": "<PERSON><PERSON><PERSON> ein neues Ziel:", "THINKING": "Denke nach...", "TASK_ADDED": "Aufgabe hinzugefügt:", "COMPLETING": "Fertigstellen:", "NO_MORE_TASKS": "<PERSON>ine weiteren Unteraufgaben für:", "CONSOLE_TEXT_COPIED_TO_CLIPBOARD": "Text wurde in die Zwischenablage kopiert", "CONSOLE_UNABLE_TO_COPY_TO_CLIPBOARD": "Text konnte nicht in die Zwischenablage kopiert werden", "RESTART_IF_IT_TAKES_X_SEC": "(<PERSON>en Si<PERSON> neu, wenn dies länger als 30 Sekunden dauert)", "👉 Create an agent by adding a name / goal, and hitting deploy!": "👉 Erst<PERSON> einen Agenten, indem du einen Namen/ein Z<PERSON> hinzufügst und auf Deploy drückst!"}