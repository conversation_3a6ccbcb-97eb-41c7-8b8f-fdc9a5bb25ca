# Agent Zero Troubleshooting Guide

## Common Issues and Solutions

### 1. "Failed to pause job loop by development instance" Error

**Problem**: You see repeated errors like:
```
Error: Failed to pause job loop by development instance: Cannot connect to host localhost:55080
```

**Cause**: The system is running in development mode but there's no development instance running on port 55080.

**Solutions**:

#### Option A: Disable Development Mode (Recommended for most users)
1. Edit `.vscode/launch.json` and remove `--development=true` from the args
2. Or run the system without the `--development` flag

#### Option B: Set up Development Instance
If you need development mode coordination:
1. Ensure Docker is running
2. Start the RFC service on port 55080
3. Set the `RFC_PASSWORD` environment variable

### 2. Log File Rotation

The system now automatically manages log files:
- Keeps only the 10 most recent log files
- Older logs are automatically deleted
- Logs are stored in the `logs/` directory

### 3. Configuration

**Environment Variables**:
- `RFC_PASSWORD`: Password for RFC communication (default: unified_ai_system_2024)
- `WEB_UI_PORT`: Port for the web UI (default: 50001)
- `USE_CLOUDFLARE`: Enable Cloudflare tunnel (default: false)

**Settings**:
- `rfc_port_http`: Port for RFC HTTP communication (default: 55080)
- `rfc_auto_docker`: Auto-start Docker for RFC (default: true)

### 4. Running the System

**For Development**:
```bash
python run_ui.py --development
```

**For Production**:
```bash
python run_ui.py
```

**With Custom Port**:
```bash
python run_ui.py --port 8080
```

### 5. Log Analysis

Log files are stored in HTML format in the `logs/` directory:
- `log_YYYYMMDD_HHMMSS.html` - Main application logs
- Check the latest log file for recent errors
- Logs include timestamps, message types, and full context

### 6. Network Issues

If you encounter connection issues:
1. Check if the required ports are available
2. Verify firewall settings
3. Ensure no other services are using the same ports
4. Check the `.env` file for correct configuration

## Getting Help

If you continue to experience issues:
1. Check the latest log file in the `logs/` directory
2. Verify your configuration in `.env` and `settings.py`
3. Ensure all dependencies are properly installed
4. Try running without development mode first