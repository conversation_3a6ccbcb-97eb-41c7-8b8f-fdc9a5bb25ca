{"unified_local_ai": {"name": "Unified Local AI Agent", "version": "3.0.0", "description": "Single unified agent combining all AI capabilities using local Ollama models", "architecture": "unified_local", "ollama_server": "http://localhost:11434"}, "models": {"primary_reasoning": {"name": "deepseek-r1:14b", "purpose": "Main reasoning, coding, problem solving", "capabilities": ["coding", "analysis", "reasoning", "file_operations"], "context_window": 32768, "temperature": 0.1}, "complex_planning": {"name": "magistral:24b", "purpose": "Multi-step task planning and orchestration", "capabilities": ["planning", "orchestration", "workflow", "project_management"], "context_window": 32768, "temperature": 0.2}, "advanced_reasoning": {"name": "phi4-reasoning:plus", "purpose": "Complex reasoning and mathematical problems", "capabilities": ["math", "logic", "complex_reasoning", "analysis"], "context_window": 16384, "temperature": 0.1}, "multimodal": {"name": "qwen2.5vl:32b", "purpose": "Vision, image analysis, document processing", "capabilities": ["vision", "image_analysis", "document_processing", "multimodal"], "context_window": 32768, "temperature": 0.3}, "fast_general": {"name": "llama3.2:latest", "purpose": "Fast responses for simple queries", "capabilities": ["general", "chat", "quick_responses"], "context_window": 8192, "temperature": 0.7}, "embeddings": {"name": "nomic-embed-text:latest", "purpose": "Text embeddings for search and similarity", "capabilities": ["embeddings", "search", "similarity"], "dimensions": 768}}, "capabilities": {"code_generation": {"model": "fast_general", "tools": ["file_system", "terminal", "git"], "languages": ["python", "javascript", "html", "css", "sql", "bash"]}, "web_search": {"model": "fast_general", "embedding_model": "embeddings", "tools": ["web_scraper", "vector_db", "local_indexing"], "search_engines": ["local_scraping", "cached_results"]}, "voice_processing": {"stt_model": "whisper_local", "tts_model": "local_tts", "conversation_model": "fast_general", "tools": ["audio_processing", "voice_synthesis"]}, "multimodal_processing": {"model": "multimodal", "tools": ["image_analysis", "document_ocr", "visual_reasoning"], "formats": ["jpg", "png", "pdf", "docx", "txt"]}, "task_planning": {"model": "complex_planning", "reasoning_model": "advanced_reasoning", "tools": ["workflow_engine", "dependency_tracker", "progress_monitor"]}, "autonomous_execution": {"primary_model": "primary_reasoning", "planning_model": "complex_planning", "tools": ["all_capabilities"], "safety_checks": true}}, "local_services": {"ollama": {"url": "http://localhost:11434", "health_endpoint": "/api/tags", "required": true}, "whisper": {"url": "http://localhost:8004", "health_endpoint": "/health", "model": "whisper-base", "required_for": ["voice_processing"]}, "tts": {"url": "http://localhost:8005", "health_endpoint": "/health", "model": "local_tts", "required_for": ["voice_processing"]}, "vector_db": {"url": "http://localhost:8006", "health_endpoint": "/health", "storage": "local_chromadb", "required_for": ["web_search", "document_search"]}}, "unified_interface": {"port": 9001, "endpoints": {"chat": "/chat", "voice": "/voice", "upload": "/upload", "execute": "/execute", "search": "/search", "health": "/health"}, "supported_inputs": ["text", "voice", "images", "documents", "files"], "supported_outputs": ["text", "voice", "files", "actions", "code"]}, "security": {"local_only": true, "no_external_apis": true, "file_access_restricted": true, "safe_execution": true}}