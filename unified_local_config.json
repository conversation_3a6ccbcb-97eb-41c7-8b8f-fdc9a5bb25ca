{"unified_local_ai": {"name": "Unified Local AI Agent", "version": "3.0.0", "description": "Single unified agent combining all AI capabilities using local Ollama models", "architecture": "unified_local", "ollama_server": "http://localhost:11434"}, "models": {"fast_response": {"name": "deepseek-r1:latest", "purpose": "Quick responses and reasoning tasks", "capabilities": ["general", "chat", "reasoning", "research", "analysis"], "context_window": 32768, "temperature": 0.3}, "advanced_reasoning": {"name": "deepseek-r1:14b", "purpose": "Complex reasoning and research tasks", "capabilities": ["advanced_reasoning", "research", "analysis", "complex_queries"], "context_window": 65536, "temperature": 0.2}, "advanced_code": {"name": "deepseek-r1:14b", "purpose": "Complex coding and development tasks", "capabilities": ["advanced_coding", "debugging", "architecture"], "context_window": 32768, "temperature": 0.1}, "multimodal": {"name": "llava:7b", "purpose": "Vision and image analysis", "capabilities": ["vision", "image_analysis", "multimodal"], "context_window": 4096, "temperature": 0.3}, "research_specialist": {"name": "deepseek-r1:latest", "purpose": "Reliable research and analysis with vision capabilities", "capabilities": ["research", "analysis", "synthesis", "comprehensive_responses", "multimodal"], "context_window": 32768, "temperature": 0.3}, "embeddings": {"name": "nomic-embed-text:latest", "purpose": "Text embeddings for search", "capabilities": ["embeddings", "search", "similarity"], "dimensions": 768}, "fast_chat": {"name": "llama3.2:latest", "purpose": "Ultra-fast chat responses (3.6s avg)", "capabilities": ["chat", "general", "quick_responses"], "context_window": 8192, "temperature": 0.7}, "balanced_performance": {"name": "hermes3:8b", "purpose": "Balanced performance for general tasks (6.4s avg)", "capabilities": ["general", "chat", "reasoning", "coding"], "context_window": 16384, "temperature": 0.5}, "lightweight": {"name": "nemotron-mini:4b", "purpose": "Lightweight model for simple tasks (4.6s avg)", "capabilities": ["simple_tasks", "quick_responses", "basic_reasoning"], "context_window": 4096, "temperature": 0.6}}, "capabilities": {"code_generation": {"model": "fast_chat", "advanced_model": "balanced_performance", "tools": ["file_system", "terminal", "git"]}, "web_search": {"model": "research_specialist", "reasoning_model": "advanced_reasoning", "embedding_model": "embeddings", "tools": ["web_scraper", "vector_db", "content_analyzer", "fact_checker"]}, "voice_processing": {"conversation_model": "fast_response", "tools": ["audio_processing", "voice_synthesis"]}, "multimodal_processing": {"model": "multimodal", "tools": ["image_analysis", "visual_reasoning"]}, "task_planning": {"model": "fast_response", "tools": ["workflow_engine", "progress_monitor"]}, "autonomous_execution": {"model": "fast_response", "tools": ["all_capabilities"]}, "advanced_development": {"model": "fast_response", "advanced_model": "advanced_code", "service": "openhands", "tools": ["code_analysis", "file_operations", "development_assistance"]}, "advanced_voice": {"model": "fast_response", "service": "openvoice", "tools": ["voice_cloning", "multilingual_tts", "voice_analysis"]}, "general_chat": {"model": "fast_chat", "fallback_model": "fast_response", "research_model": "research_specialist", "tools": ["conversation", "general_assistance", "research_support"]}, "quick_tasks": {"model": "lightweight", "tools": ["simple_responses", "basic_assistance"]}, "balanced_tasks": {"model": "balanced_performance", "tools": ["general_coding", "moderate_reasoning", "balanced_responses"]}}, "local_services": {"ollama": {"url": "http://localhost:11434", "health_endpoint": "/api/tags", "required": true}, "whisper": {"url": "http://localhost:8004", "health_endpoint": "/health", "model": "whisper-base", "required_for": ["voice_processing"]}, "tts": {"url": "http://localhost:8005", "health_endpoint": "/health", "model": "local_tts", "required_for": ["voice_processing"]}, "vector_db": {"url": "http://localhost:8006", "health_endpoint": "/health", "storage": "local_chromadb", "required_for": ["web_search", "document_search"]}}, "unified_interface": {"port": 9001, "endpoints": {"chat": "/chat", "voice": "/voice", "upload": "/upload", "execute": "/execute", "search": "/search", "health": "/health"}, "supported_inputs": ["text", "voice", "images", "documents", "files"], "supported_outputs": ["text", "voice", "files", "actions", "code"]}, "security": {"local_only": true, "no_external_apis": true, "file_access_restricted": true, "safe_execution": true}, "services": {"openhands": {"enabled": true, "path": "OpenHands-main", "capabilities": ["advanced_development", "code_analysis", "file_operations"]}, "openvoice": {"enabled": true, "path": "OpenVoice-main", "capabilities": ["advanced_voice", "voice_cloning", "multilingual_tts"]}}}