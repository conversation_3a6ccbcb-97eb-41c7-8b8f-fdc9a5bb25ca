#!/usr/bin/env python3
"""
Local Web Search Processor - Web scraping with local embeddings
"""

import asyncio
import json
import logging
import os
import requests
import time
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from urllib.parse import urljoin, urlparse
import re

# Web scraping imports
try:
    from bs4 import BeautifulSoup
    BEAUTIFULSOUP_AVAILABLE = True
except ImportError:
    BEAUTIFULSOUP_AVAILABLE = False

try:
    import numpy as np
    NUMPY_AVAILABLE = True
except ImportError:
    NUMPY_AVAILABLE = False

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("local-web-search")

@dataclass
class SearchResult:
    """Web search result with embeddings"""
    url: str
    title: str
    content: str
    snippet: str
    relevance_score: float = 0.0
    embedding: Optional[List[float]] = None

class LocalWebSearchProcessor:
    """Local web search with embeddings and scraping"""
    
    def __init__(self):
        self.initialized = False
        self.embedding_model = "nomic-embed-text:latest"
        self.research_model = "llama3.2:latest"  # Fast and reliable model for research synthesis
        self.ollama_url = "http://localhost:11434"
        self.max_results = 8  # Good balance for comprehensive research
        self.max_content_length = 6000  # Enough for detailed content
        self.similarity_threshold = 0.3  # Lower threshold for more sources
        self.session = requests.Session()
        
        # Configure session headers
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
    
    async def initialize(self):
        """Initialize the web search processor"""
        try:
            logger.info("🔄 Initializing Local Web Search Processor...")
            
            # Check dependencies
            if not BEAUTIFULSOUP_AVAILABLE:
                logger.error("❌ BeautifulSoup4 not available. Install with: pip install beautifulsoup4")
                return False
            
            if not NUMPY_AVAILABLE:
                logger.error("❌ NumPy not available. Install with: pip install numpy")
                return False
            
            # Test Ollama connection
            if not await self._test_ollama_connection():
                logger.error("❌ Ollama connection failed")
                return False
            
            # Test embedding model
            if not await self._test_embedding_model():
                logger.error("❌ Embedding model test failed")
                return False
            
            self.initialized = True
            logger.info("✅ Local Web Search Processor initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize web search processor: {e}")
            return False
    
    async def _test_ollama_connection(self) -> bool:
        """Test connection to Ollama server"""
        try:
            response = requests.get(f"{self.ollama_url}/api/tags", timeout=5)
            return response.status_code == 200
        except Exception as e:
            logger.error(f"❌ Ollama connection test failed: {e}")
            return False
    
    async def _test_embedding_model(self) -> bool:
        """Test the embedding model"""
        try:
            test_text = "This is a test for embedding generation"
            embedding = await self._generate_embedding(test_text)
            return embedding is not None and len(embedding) > 0
        except Exception as e:
            logger.error(f"❌ Embedding model test failed: {e}")
            return False
    
    async def _generate_embedding(self, text: str) -> Optional[List[float]]:
        """Generate embedding for text using local model"""
        try:
            payload = {
                "model": self.embedding_model,
                "prompt": text
            }
            
            response = requests.post(
                f"{self.ollama_url}/api/embeddings",
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                return result.get("embedding", [])
            else:
                logger.error(f"❌ Embedding generation failed: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"❌ Embedding generation error: {e}")
            return None
    
    def _calculate_similarity(self, embedding1: List[float], embedding2: List[float]) -> float:
        """Calculate cosine similarity between embeddings"""
        try:
            if not NUMPY_AVAILABLE:
                return 0.0
            
            vec1 = np.array(embedding1)
            vec2 = np.array(embedding2)
            
            # Cosine similarity
            dot_product = np.dot(vec1, vec2)
            norm1 = np.linalg.norm(vec1)
            norm2 = np.linalg.norm(vec2)
            
            if norm1 == 0 or norm2 == 0:
                return 0.0
            
            return dot_product / (norm1 * norm2)
            
        except Exception as e:
            logger.error(f"❌ Similarity calculation error: {e}")
            return 0.0
    
    async def _scrape_url(self, url: str) -> Optional[Dict[str, str]]:
        """Scrape content from a URL"""
        try:
            logger.info(f"🌐 Scraping: {url}")
            
            response = self.session.get(url, timeout=8)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Extract title
            title_tag = soup.find('title')
            title = title_tag.get_text().strip() if title_tag else "No Title"
            
            # Remove script and style elements
            for script in soup(["script", "style", "nav", "footer", "header"]):
                script.decompose()
            
            # Extract main content
            content_selectors = [
                'main', 'article', '.content', '#content', 
                '.post', '.entry', '.article-body'
            ]
            
            content = ""
            for selector in content_selectors:
                content_elem = soup.select_one(selector)
                if content_elem:
                    content = content_elem.get_text()
                    break
            
            # Fallback to body if no main content found
            if not content:
                body = soup.find('body')
                content = body.get_text() if body else soup.get_text()
            
            # Clean up content
            content = re.sub(r'\s+', ' ', content).strip()
            content = content[:self.max_content_length]
            
            # Create snippet (first 200 chars)
            snippet = content[:200] + "..." if len(content) > 200 else content
            
            return {
                "title": title,
                "content": content,
                "snippet": snippet
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to scrape {url}: {e}")
            return None
    
    async def search_web(self, query: str, urls: Optional[List[str]] = None) -> List[SearchResult]:
        """Perform web search with local processing"""
        if not self.initialized:
            logger.error("❌ Web search processor not initialized")
            return []
        
        try:
            logger.info(f"🔍 Searching for: {query}")
            
            # Generate query embedding
            query_embedding = await self._generate_embedding(query)
            if not query_embedding:
                logger.error("❌ Failed to generate query embedding")
                return []
            
            # Use provided URLs or default search URLs
            if not urls:
                urls = await self._get_search_urls(query)
            
            results = []
            
            for url in urls[:self.max_results]:
                scraped_data = await self._scrape_url(url)
                if not scraped_data:
                    continue
                
                # Generate content embedding
                content_embedding = await self._generate_embedding(scraped_data["content"])
                if not content_embedding:
                    continue
                
                # Calculate relevance score
                relevance_score = self._calculate_similarity(query_embedding, content_embedding)
                
                result = SearchResult(
                    url=url,
                    title=scraped_data["title"],
                    content=scraped_data["content"],
                    snippet=scraped_data["snippet"],
                    relevance_score=relevance_score,
                    embedding=content_embedding
                )
                
                results.append(result)
                logger.info(f"✅ Processed: {url} (relevance: {relevance_score:.3f})")
            
            # Sort by relevance score
            results.sort(key=lambda x: x.relevance_score, reverse=True)
            
            logger.info(f"🎯 Found {len(results)} relevant results")

            # Progressive response delivery - always provide immediate results
            logger.info(f"📊 Found {len(results)} search results, preparing response...")

            # Always attempt synthesis if we have results, but don't block on it
            if len(results) >= 3:
                logger.info(f"🧠 Attempting AI synthesis for {len(results)} results...")

                # Create immediate response with search results
                immediate_summary = self._create_immediate_summary(query, results)
                immediate_result = SearchResult(
                    url="immediate_summary",
                    title=f"Quick Summary: {query}",
                    content=immediate_summary,
                    snippet=immediate_summary[:200] + "..." if len(immediate_summary) > 200 else immediate_summary,
                    relevance_score=0.95
                )
                results.insert(0, immediate_result)

                # Try REAL AI synthesis with aggressive timeout protection
                logger.info("🧠 Attempting REAL AI synthesis for comprehensive research...")
                try:
                    synthesis = await asyncio.wait_for(
                        self._synthesize_research(query, results[1:]),
                        timeout=12.0  # 12 second max for synthesis (reduced)
                    )
                except asyncio.TimeoutError:
                    logger.warning("⚠️ AI synthesis timed out - using fallback")
                    synthesis = None

                if synthesis:
                    # Replace immediate summary with full synthesis
                    synthesis_result = SearchResult(
                        url="ai_research_synthesis",
                        title=f"🤖 AI Research Analysis: {query}",
                        content=synthesis,
                        snippet=synthesis[:300] + "..." if len(synthesis) > 300 else synthesis,
                        relevance_score=1.0
                    )
                    results[0] = synthesis_result  # Replace immediate summary
                    logger.info("✅ AI research synthesis completed and added")
                else:
                    logger.info("ℹ️ Using immediate summary (AI synthesis unavailable)")
            else:
                logger.info("ℹ️ Limited results - providing direct search results")

            return results

        except Exception as e:
            logger.error(f"❌ Web search failed: {e}")
            return []
    
    async def _get_search_urls(self, query: str) -> List[str]:
        """Get URLs to search with comprehensive sources"""
        encoded_query = requests.utils.quote(query)
        wiki_query = query.replace(' ', '_')

        # Comprehensive search sources for thorough research
        knowledge_sources = [
            f"https://en.wikipedia.org/wiki/{wiki_query}",
            f"https://www.britannica.com/search?query={encoded_query}",
            f"https://scholar.google.com/scholar?q={encoded_query}",
            f"https://www.nature.com/search?q={encoded_query}",
            f"https://arxiv.org/search/?query={encoded_query}",
        ]

        tech_sources = [
            f"https://stackoverflow.com/search?q={encoded_query}",
            f"https://github.com/search?q={encoded_query}",
            f"https://news.ycombinator.com/item?id=search&q={encoded_query}",
            f"https://www.reddit.com/r/programming/search/?q={encoded_query}",
            f"https://dev.to/search?q={encoded_query}",
        ]

        news_sources = [
            f"https://www.reddit.com/search/?q={encoded_query}",
            f"https://www.bbc.com/search?q={encoded_query}",
            f"https://www.reuters.com/site-search/?query={encoded_query}",
            f"https://techcrunch.com/search/{encoded_query}",
            f"https://arstechnica.com/search/?query={encoded_query}",
        ]

        # Combine all sources for comprehensive coverage
        all_sources = knowledge_sources + tech_sources + news_sources

        # Add topic-specific sources based on query content
        query_lower = query.lower()

        if any(tech_term in query_lower for tech_term in ['python', 'javascript', 'programming', 'code', 'software']):
            all_sources.extend([
                f"https://docs.python.org/3/search.html?q={encoded_query}",
                f"https://developer.mozilla.org/en-US/search?q={encoded_query}",
            ])

        if any(ai_term in query_lower for ai_term in ['ai', 'artificial intelligence', 'machine learning', 'deep learning']):
            all_sources.extend([
                f"https://arxiv.org/search/?query={encoded_query}&searchtype=all",
                f"https://paperswithcode.com/search?q={encoded_query}",
            ])

        if any(news_term in query_lower for news_term in ['news', 'latest', 'recent', 'current']):
            all_sources.extend([
                f"https://www.reuters.com/search/news?blob={encoded_query}",
                f"https://www.bbc.com/search?q={encoded_query}",
            ])

        return all_sources

    async def _synthesize_research(self, query: str, results: List[SearchResult]) -> Optional[str]:
        """Synthesize research findings using local LLM with robust timeout handling"""
        try:
            # Prepare optimized research content (use fewer results for speed)
            research_content = f"Research Query: {query}\n\n"
            research_content += "Key Sources:\n"

            # Use only top 4 results for faster processing
            for i, result in enumerate(results[:4], 1):
                research_content += f"\n{i}. {result.title}\n"
                research_content += f"Content: {result.content[:600]}...\n"  # Reduced content
                research_content += f"Relevance: {result.relevance_score:.2f}\n"

            # Shorter, more focused synthesis prompt
            synthesis_prompt = f"""Provide a concise research synthesis for: "{query}"

{research_content}

Deliver:
1. Key findings summary (2-3 sentences)
2. Main insights (2-3 points)
3. Important conclusions (1-2 sentences)

Keep response under 300 words and focus on the most important information."""

            # Call research model with aggressive timeout
            response = requests.post(
                f"{self.ollama_url}/api/generate",
                json={
                    "model": self.research_model,
                    "prompt": synthesis_prompt,
                    "stream": False,
                    "options": {
                        "temperature": 0.1,  # Lower for faster, focused responses
                        "top_p": 0.8,
                        "num_ctx": 8192,    # Reduced context window
                        "num_predict": 300  # Limit response length
                    }
                },
                timeout=8  # Very fast timeout for quick synthesis
            )

            if response.status_code == 200:
                synthesis = response.json().get("response", "").strip()
                if synthesis:
                    logger.info(f"✅ Generated research synthesis ({len(synthesis)} chars)")
                    return synthesis
                else:
                    logger.warning("⚠️ Empty synthesis response - using fallback")
                    return self._create_fallback_synthesis(query, results)
            else:
                logger.warning(f"⚠️ Synthesis API error: {response.status_code} - using fallback")
                return self._create_fallback_synthesis(query, results)

        except requests.exceptions.Timeout:
            logger.warning("⚠️ Research synthesis timed out - using fallback")
            return self._create_fallback_synthesis(query, results)
        except Exception as e:
            logger.warning(f"⚠️ Research synthesis failed: {e} - using fallback")
            return self._create_fallback_synthesis(query, results)

    def _create_immediate_summary(self, query: str, results: List[SearchResult]) -> str:
        """Create an immediate summary for progressive response delivery"""
        try:
            summary = f"# Research Summary: {query}\n\n"
            summary += f"**Quick Overview:** Found {len(results)} relevant sources providing comprehensive information about your query.\n\n"

            # Categorize sources
            academic_sources = [r for r in results if any(domain in r.url for domain in ['scholar.google', 'arxiv', 'nature.com', 'pubmed'])]
            technical_sources = [r for r in results if any(domain in r.url for domain in ['stackoverflow', 'github', 'dev.to'])]
            news_sources = [r for r in results if any(domain in r.url for domain in ['bbc', 'techcrunch', 'reuters'])]

            if academic_sources:
                summary += f"**📚 Academic Sources ({len(academic_sources)}):** Research papers and scholarly articles\n"
            if technical_sources:
                summary += f"**💻 Technical Sources ({len(technical_sources)}):** Developer resources and technical documentation\n"
            if news_sources:
                summary += f"**📰 News Sources ({len(news_sources)}):** Current developments and industry news\n"

            summary += "\n**Top Sources:**\n"
            for i, result in enumerate(results[:4], 1):
                summary += f"{i}. **{result.title}**\n"
                summary += f"   {result.snippet[:120]}...\n"
                summary += f"   *Relevance: {result.relevance_score:.2f}*\n\n"

            summary += "*🤖 AI analysis in progress... This summary will be enhanced with detailed insights.*"
            return summary

        except Exception as e:
            logger.error(f"❌ Immediate summary failed: {str(e)}")
            return f"Research in progress for '{query}' - found {len(results)} sources."

    def _create_fallback_synthesis(self, query: str, results: List[SearchResult]) -> str:
        """Create a simple fallback synthesis when AI synthesis fails"""
        try:
            synthesis = f"# Research Results: {query}\n\n"
            synthesis += f"Found {len(results)} relevant sources with comprehensive information:\n\n"

            for i, result in enumerate(results[:5], 1):
                synthesis += f"**{i}. {result.title}**\n"
                synthesis += f"Source: {result.url}\n"
                synthesis += f"Summary: {result.snippet[:150]}...\n"
                synthesis += f"Relevance Score: {result.relevance_score:.2f}\n\n"

            synthesis += "\n*This is a structured summary of research findings. The sources above contain detailed information about your query.*"
            return synthesis

        except Exception as e:
            logger.error(f"❌ Fallback synthesis failed: {str(e)}")
            return f"Research completed for '{query}' - found {len(results)} relevant sources."

    def get_status(self) -> Dict[str, Any]:
        """Get processor status"""
        return {
            "initialized": self.initialized,
            "embedding_model": self.embedding_model,
            "beautifulsoup_available": BEAUTIFULSOUP_AVAILABLE,
            "numpy_available": NUMPY_AVAILABLE,
            "capabilities": [
                "web_scraping",
                "content_extraction", 
                "embedding_generation",
                "relevance_scoring"
            ] if self.initialized else []
        }

# Global instance
_web_search_processor = None

async def get_web_search_processor() -> LocalWebSearchProcessor:
    """Get or create web search processor instance"""
    global _web_search_processor
    
    if _web_search_processor is None:
        _web_search_processor = LocalWebSearchProcessor()
        await _web_search_processor.initialize()
    
    return _web_search_processor

if __name__ == "__main__":
    async def test_web_search():
        processor = await get_web_search_processor()
        
        if processor.initialized:
            results = await processor.search_web("artificial intelligence")
            
            print(f"Found {len(results)} results:")
            for i, result in enumerate(results, 1):
                print(f"{i}. {result.title} (Score: {result.relevance_score:.3f})")
                print(f"   URL: {result.url}")
                print(f"   Snippet: {result.snippet[:100]}...")
                print()
        else:
            print("Failed to initialize web search processor")
    
    asyncio.run(test_web_search())
