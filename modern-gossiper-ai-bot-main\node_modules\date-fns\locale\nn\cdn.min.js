var A=function(U){return A=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(J){return typeof J}:function(J){return J&&typeof Symbol=="function"&&J.constructor===Symbol&&J!==Symbol.prototype?"symbol":typeof J},A(U)},N=function(U,J){var X=Object.keys(U);if(Object.getOwnPropertySymbols){var I=Object.getOwnPropertySymbols(U);J&&(I=I.filter(function(K){return Object.getOwnPropertyDescriptor(U,K).enumerable})),X.push.apply(X,I)}return X},z=function(U){for(var J=1;J<arguments.length;J++){var X=arguments[J]!=null?arguments[J]:{};J%2?N(Object(X),!0).forEach(function(I){B5(U,I,X[I])}):Object.getOwnPropertyDescriptors?Object.defineProperties(U,Object.getOwnPropertyDescriptors(X)):N(Object(X)).forEach(function(I){Object.defineProperty(U,I,Object.getOwnPropertyDescriptor(X,I))})}return U},B5=function(U,J,X){if(J=C5(J),J in U)Object.defineProperty(U,J,{value:X,enumerable:!0,configurable:!0,writable:!0});else U[J]=X;return U},C5=function(U){var J=G5(U,"string");return A(J)=="symbol"?J:String(J)},G5=function(U,J){if(A(U)!="object"||!U)return U;var X=U[Symbol.toPrimitive];if(X!==void 0){var I=X.call(U,J||"default");if(A(I)!="object")return I;throw new TypeError("@@toPrimitive must return a primitive value.")}return(J==="string"?String:Number)(U)};(function(U){var J=Object.defineProperty,X=function B(G,C){for(var H in C)J(G,H,{get:C[H],enumerable:!0,configurable:!0,set:function Y(Z){return C[H]=function(){return Z}}})},I={lessThanXSeconds:{one:"mindre enn eitt sekund",other:"mindre enn {{count}} sekund"},xSeconds:{one:"eitt sekund",other:"{{count}} sekund"},halfAMinute:"eit halvt minutt",lessThanXMinutes:{one:"mindre enn eitt minutt",other:"mindre enn {{count}} minutt"},xMinutes:{one:"eitt minutt",other:"{{count}} minutt"},aboutXHours:{one:"omtrent ein time",other:"omtrent {{count}} timar"},xHours:{one:"ein time",other:"{{count}} timar"},xDays:{one:"ein dag",other:"{{count}} dagar"},aboutXWeeks:{one:"omtrent ei veke",other:"omtrent {{count}} veker"},xWeeks:{one:"ei veke",other:"{{count}} veker"},aboutXMonths:{one:"omtrent ein m\xE5nad",other:"omtrent {{count}} m\xE5nader"},xMonths:{one:"ein m\xE5nad",other:"{{count}} m\xE5nader"},aboutXYears:{one:"omtrent eitt \xE5r",other:"omtrent {{count}} \xE5r"},xYears:{one:"eitt \xE5r",other:"{{count}} \xE5r"},overXYears:{one:"over eitt \xE5r",other:"over {{count}} \xE5r"},almostXYears:{one:"nesten eitt \xE5r",other:"nesten {{count}} \xE5r"}},K=["null","ein","to","tre","fire","fem","seks","sju","\xE5tte","ni","ti","elleve","tolv"],S=function B(G,C,H){var Y,Z=I[G];if(typeof Z==="string")Y=Z;else if(C===1)Y=Z.one;else Y=Z.other.replace("{{count}}",C<13?K[C]:String(C));if(H!==null&&H!==void 0&&H.addSuffix)if(H.comparison&&H.comparison>0)return"om "+Y;else return Y+" sidan";return Y};function x(B){return function(){var G=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},C=G.width?String(G.width):B.defaultWidth,H=B.formats[C]||B.formats[B.defaultWidth];return H}}var $={full:"EEEE d. MMMM y",long:"d. MMMM y",medium:"d. MMM y",short:"dd.MM.y"},M={full:"'kl'. HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},R={full:"{{date}} 'kl.' {{time}}",long:"{{date}} 'kl.' {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},V={date:x({formats:$,defaultWidth:"full"}),time:x({formats:M,defaultWidth:"full"}),dateTime:x({formats:R,defaultWidth:"full"})},L={lastWeek:"'f\xF8rre' eeee 'kl.' p",yesterday:"'i g\xE5r kl.' p",today:"'i dag kl.' p",tomorrow:"'i morgon kl.' p",nextWeek:"EEEE 'kl.' p",other:"P"},f=function B(G,C,H,Y){return L[G]};function Q(B){return function(G,C){var H=C!==null&&C!==void 0&&C.context?String(C.context):"standalone",Y;if(H==="formatting"&&B.formattingValues){var Z=B.defaultFormattingWidth||B.defaultWidth,O=C!==null&&C!==void 0&&C.width?String(C.width):Z;Y=B.formattingValues[O]||B.formattingValues[Z]}else{var T=B.defaultWidth,W=C!==null&&C!==void 0&&C.width?String(C.width):B.defaultWidth;Y=B.values[W]||B.values[T]}var E=B.argumentCallback?B.argumentCallback(G):G;return Y[E]}}var j={narrow:["f.Kr.","e.Kr."],abbreviated:["f.Kr.","e.Kr."],wide:["f\xF8r Kristus","etter Kristus"]},w={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1. kvartal","2. kvartal","3. kvartal","4. kvartal"]},v={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["jan.","feb.","mars","apr.","mai","juni","juli","aug.","sep.","okt.","nov.","des."],wide:["januar","februar","mars","april","mai","juni","juli","august","september","oktober","november","desember"]},P={narrow:["S","M","T","O","T","F","L"],short:["su","m\xE5","ty","on","to","fr","lau"],abbreviated:["sun","m\xE5n","tys","ons","tor","fre","laur"],wide:["sundag","m\xE5ndag","tysdag","onsdag","torsdag","fredag","laurdag"]},_={narrow:{am:"a",pm:"p",midnight:"midnatt",noon:"middag",morning:"p\xE5 morg.",afternoon:"p\xE5 etterm.",evening:"p\xE5 kvelden",night:"p\xE5 natta"},abbreviated:{am:"a.m.",pm:"p.m.",midnight:"midnatt",noon:"middag",morning:"p\xE5 morg.",afternoon:"p\xE5 etterm.",evening:"p\xE5 kvelden",night:"p\xE5 natta"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnatt",noon:"middag",morning:"p\xE5 morgonen",afternoon:"p\xE5 ettermiddagen",evening:"p\xE5 kvelden",night:"p\xE5 natta"}},F=function B(G,C){var H=Number(G);return H+"."},k={ordinalNumber:F,era:Q({values:j,defaultWidth:"wide"}),quarter:Q({values:w,defaultWidth:"wide",argumentCallback:function B(G){return G-1}}),month:Q({values:v,defaultWidth:"wide"}),day:Q({values:P,defaultWidth:"wide"}),dayPeriod:Q({values:_,defaultWidth:"wide"})};function q(B){return function(G){var C=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},H=C.width,Y=H&&B.matchPatterns[H]||B.matchPatterns[B.defaultMatchWidth],Z=G.match(Y);if(!Z)return null;var O=Z[0],T=H&&B.parsePatterns[H]||B.parsePatterns[B.defaultParseWidth],W=Array.isArray(T)?b(T,function(D){return D.test(O)}):m(T,function(D){return D.test(O)}),E;E=B.valueCallback?B.valueCallback(W):W,E=C.valueCallback?C.valueCallback(E):E;var t=G.slice(O.length);return{value:E,rest:t}}}var m=function B(G,C){for(var H in G)if(Object.prototype.hasOwnProperty.call(G,H)&&C(G[H]))return H;return},b=function B(G,C){for(var H=0;H<G.length;H++)if(C(G[H]))return H;return};function h(B){return function(G){var C=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},H=G.match(B.matchPattern);if(!H)return null;var Y=H[0],Z=G.match(B.parsePattern);if(!Z)return null;var O=B.valueCallback?B.valueCallback(Z[0]):Z[0];O=C.valueCallback?C.valueCallback(O):O;var T=G.slice(Y.length);return{value:O,rest:T}}}var y=/^(\d+)\.?/i,c=/\d+/i,p={narrow:/^(f\.? ?Kr\.?|fvt\.?|e\.? ?Kr\.?|evt\.?)/i,abbreviated:/^(f\.? ?Kr\.?|fvt\.?|e\.? ?Kr\.?|evt\.?)/i,wide:/^(før Kristus|før vår tid|etter Kristus|vår tid)/i},u={any:[/^f/i,/^e/i]},g={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](\.)? kvartal/i},d={any:[/1/i,/2/i,/3/i,/4/i]},l={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mars?|apr|mai|juni?|juli?|aug|sep|okt|nov|des)\.?/i,wide:/^(januar|februar|mars|april|mai|juni|juli|august|september|oktober|november|desember)/i},i={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^mai/i,/^jun/i,/^jul/i,/^aug/i,/^s/i,/^o/i,/^n/i,/^d/i]},n={narrow:/^[smtofl]/i,short:/^(su|må|ty|on|to|fr|la)/i,abbreviated:/^(sun|mån|tys|ons|tor|fre|laur)/i,wide:/^(sundag|måndag|tysdag|onsdag|torsdag|fredag|laurdag)/i},s={any:[/^s/i,/^m/i,/^ty/i,/^o/i,/^to/i,/^f/i,/^l/i]},o={narrow:/^(midnatt|middag|(på) (morgonen|ettermiddagen|kvelden|natta)|[ap])/i,any:/^([ap]\.?\s?m\.?|midnatt|middag|(på) (morgonen|ettermiddagen|kvelden|natta))/i},r={any:{am:/^a(\.?\s?m\.?)?$/i,pm:/^p(\.?\s?m\.?)?$/i,midnight:/^midn/i,noon:/^midd/i,morning:/morgon/i,afternoon:/ettermiddag/i,evening:/kveld/i,night:/natt/i}},e={ordinalNumber:h({matchPattern:y,parsePattern:c,valueCallback:function B(G){return parseInt(G,10)}}),era:q({matchPatterns:p,defaultMatchWidth:"wide",parsePatterns:u,defaultParseWidth:"any"}),quarter:q({matchPatterns:g,defaultMatchWidth:"wide",parsePatterns:d,defaultParseWidth:"any",valueCallback:function B(G){return G+1}}),month:q({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any"}),day:q({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),dayPeriod:q({matchPatterns:o,defaultMatchWidth:"any",parsePatterns:r,defaultParseWidth:"any"})},a={code:"nn",formatDistance:S,formatLong:V,formatRelative:f,localize:k,match:e,options:{weekStartsOn:1,firstWeekContainsDate:4}};window.dateFns=z(z({},window.dateFns),{},{locale:z(z({},(U=window.dateFns)===null||U===void 0?void 0:U.locale),{},{nn:a})})})();

//# debugId=71E718FAC9CCF73264756e2164756e21
