<p align="center">
  <img src="https://raw.githubusercontent.com/reworkd/AgentGPT/main/public/banner.png?token=GHSAT0AAAAAAB7JND3U3VGGF3UYYHGYO4RAZBSDJAQ" height="300"/>
</p>
<p align="center">
  <em>🤖 <PERSON><PERSON><PERSON>, konfigurá<PERSON><PERSON> és telepítse az autonóm AI-ügynököket a böngészőjében. 🤖 </em>
</p>
<p align="center">
    <img alt="Node version" src="https://img.shields.io/static/v1?label=node&message=%20%3E=16.0.0&logo=node.js&color=2334D058" />
      <a href="https://github.com/reworkd/AgentGPT/blob/master/README.md"><img src="https://img.shields.io/badge/lang-English-blue.svg" alt="English"></a>
  <a href="https://github.com/reworkd/AgentGPT/blob/master/docs/README.zh-HANS.md"><img src="https://img.shields.io/badge/lang-简体中文-red.svg" alt="简体中文"></a>
  <a href="https://github.com/reworkd/AgentGPT/blob/master/docs/README.hu-Cs4K1Sr4C.md"><img src="https://img.shields.io/badge/lang-Hungarian-red.svg" alt="Hungarian"></a>
</p>

<p align="center">
<a href="https://agentgpt.reworkd.ai">🔗 Weboldal</a>
<span>&nbsp;&nbsp;•&nbsp;&nbsp;</span>
<a href="#-getting-started">🤝 Hozzájárulás</a>
<span>&nbsp;&nbsp;•&nbsp;&nbsp;</span>
<a href="https://twitter.com/asimdotshrestha/status/1644883727707959296">🐦 Twitter</a>
<span>&nbsp;&nbsp;•&nbsp;&nbsp;</span>
<a href="https://discord.gg/gcmNyAAFfV">📢 Discord</a>
</p>

---

<h2 align="center">
💝 Támogassa az AgentGPT fejlesztését!! 💝
</h2>

<p align="center">
Csatlakozzon hozzánk, az AgentGPT fejlesztéséhez, egy nyílt forráskódú projekthez, amely az AI automatizálás határait feszegeti! Kihívásokkal nézünk szembe a működési költségek fedezése 💸, beleértve a házon belüli API-t és egyéb infrastrukturális költségeket, amelyek az előrejelzések szerint körülbelül napi 150 USD-ra nőnek. 💳🤕 Az Ön szponzorálása elősegítené a fejlődést azáltal, hogy segít nekünk az erőforrások bővítésében, a funkciók és a funkcionalitás bővítésében, valamint az izgalmas projekt folytatásában! 🚀
</p>

<p align="center">
Ennek az ingyenes, nyílt forráskódú projektnek a szponzorálásával nem csak az avatarod/logódat láthatod alább, hanem exkluzív lehetőséget kapsz az alapítókkal való beszélgetésre is!🗣️
</p>

<p align="center">
<a href="https://github.com/sponsors/reworkd-admin">👉 Kattint ide</a> ha szeretnéd támogatni a projektet
</p>

<h3 align="center">
🙌🏻 Szponzoraink 🙌🏻
</h3>

<div align="center" dir="auto">
  <!-- sponsors --><a href="https://github.com/arthurbnhm"><img src="https://github.com/arthurbnhm.png" width="60px" alt="Arthur" /></a><a href="https://github.com/mrayonnaise"><img src="https://github.com/mrayonnaise.png" width="60px" alt="Matt Ray" /></a><a href="https://github.com/iv-ann"><img src="https://github.com/iv-ann.png" width="60px" alt="Ivan" /></a><a href="https://github.com/alex-shortt"><img src="https://github.com/alex-shortt.png" width="60px" alt="Alex Shortt" /></a><a href="https://github.com/echallenge"><img src="https://github.com/echallenge.png" width="60px" alt="eDad" /></a><a href="https://github.com/jd3655"><img src="https://github.com/jd3655.png" width="60px" alt="Vector Ventures" /></a><a href="https://github.com/PersonMan65"><img src="https://github.com/PersonMan65.png" width="60px" alt="" /></a><a href="https://github.com/peth-yursick"><img src="https://github.com/peth-yursick.png" width="60px" alt="" /></a><a href="https://github.com/durairajasivam"><img src="https://github.com/durairajasivam.png" width="60px" alt="" /></a><a href="https://github.com/floriank"><img src="https://github.com/floriank.png" width="60px" alt="Florian Kraft" /></a><a href="https://github.com/SicilianaTrevino"><img src="https://github.com/SicilianaTrevino.png" width="60px" alt="" /></a><a href="https://github.com/andrewpeluso"><img src="https://github.com/andrewpeluso.png" width="60px" alt="" /></a><a href="https://github.com/localecho"><img src="https://github.com/localecho.png" width="60px" alt="" /></a><a href="https://github.com/fireheat135"><img src="https://github.com/fireheat135.png" width="60px" alt="" /></a><a href="https://github.com/ashventure"><img src="https://github.com/ashventure.png" width="60px" alt="" /></a><a href="https://github.com/biolippi"><img src="https://github.com/biolippi.png" width="60px" alt="" /></a><a href="https://github.com/orkunisitmak"><img src="https://github.com/orkunisitmak.png" width="60px" alt="" /></a><a href="https://github.com/zoelidity"><img src="https://github.com/zoelidity.png" width="60px" alt="Zoe" /></a><a href="https://github.com/busseyl"><img src="https://github.com/busseyl.png" width="60px" alt="Lucas Bussey" /></a><a href="https://github.com/DuanChaori"><img src="https://github.com/DuanChaori.png" width="60px" alt="" /></a><a href="https://github.com/Faitltd"><img src="https://github.com/Faitltd.png" width="60px" alt="fAIt" /></a><a href="https://github.com/jukwaphil1"><img src="https://github.com/jukwaphil1.png" width="60px" alt="" /></a><a href="https://github.com/lisa-ee"><img src="https://github.com/lisa-ee.png" width="60px" alt="Lisa" /></a><a href="https://github.com/VulcanT"><img src="https://github.com/VulcanT.png" width="60px" alt="" /></a><a href="https://github.com/kman62"><img src="https://github.com/kman62.png" width="60px" alt="kmotte" /></a><a href="https://github.com/Haithamhaj"><img src="https://github.com/Haithamhaj.png" width="60px" alt="" /></a><a href="https://github.com/SwftCoins"><img src="https://github.com/SwftCoins.png" width="60px" alt="SWFT Blockchain" /></a><a href="https://github.com/ChevalierzA"><img src="https://github.com/ChevalierzA.png" width="60px" alt="" /></a><a href="https://github.com/research-developer"><img src="https://github.com/research-developer.png" width="60px" alt="" /></a><a href="https://github.com/Wetradetogether"><img src="https://github.com/Wetradetogether.png" width="60px" alt="Wetradetogether Corporation" /></a><a href="https://github.com/keanuhsieh"><img src="https://github.com/keanuhsieh.png" width="60px" alt="" /></a><a href="https://github.com/sorrynothing"><img src="https://github.com/sorrynothing.png" width="60px" alt="" /></a><a href="https://github.com/FelixAI2023"><img src="https://github.com/FelixAI2023.png" width="60px" alt="" /></a><a href="https://github.com/Mitchell-Coder-New"><img src="https://github.com/Mitchell-Coder-New.png" width="60px" alt="" /></a><a href="https://github.com/Xiaofeixiao0309"><img src="https://github.com/Xiaofeixiao0309.png" width="60px" alt="XIAO FEI" /></a><a href="https://github.com/jondwillis"><img src="https://github.com/jondwillis.png" width="60px" alt="jon" /></a><a href="https://github.com/mojosolo"><img src="https://github.com/mojosolo.png" width="60px" alt="MojoSolo" /></a><a href="https://github.com/Trecares"><img src="https://github.com/Trecares.png" width="60px" alt="" /></a><a href="https://github.com/nnkostov"><img src="https://github.com/nnkostov.png" width="60px" alt="Nikolay Kostov" /></a><a href="https://github.com/mfcolbrie"><img src="https://github.com/mfcolbrie.png" width="60px" alt="" /></a><a href="https://github.com/oryanmoshe"><img src="https://github.com/oryanmoshe.png" width="60px" alt="Oryan Moshe" /></a><a href="https://github.com/newjxmaster"><img src="https://github.com/newjxmaster.png" width="60px" alt="Napole" /></a><a href="https://github.com/jofe03"><img src="https://github.com/jofe03.png" width="60px" alt="" /></a><a href="https://github.com/DanielRobertKelly"><img src="https://github.com/DanielRobertKelly.png" width="60px" alt="" /></a><a href="https://github.com/genetrader"><img src="https://github.com/genetrader.png" width="60px" alt="Gene Waxman" /></a><a href="https://github.com/NerdDeWallStreet"><img src="https://github.com/NerdDeWallStreet.png" width="60px" alt="Nerd de Wall Street" /></a><a href="https://github.com/qwe777897"><img src="https://github.com/qwe777897.png" width="60px" alt="" /></a><a href="https://github.com/tudorw"><img src="https://github.com/tudorw.png" width="60px" alt="Tudor Watson" /></a><a href="https://github.com/ClayNelson"><img src="https://github.com/ClayNelson.png" width="60px" alt="Clay Nelson" /></a><a href="https://github.com/johnopaluwa"><img src="https://github.com/johnopaluwa.png" width="60px" alt="Opaluwa John" /></a><a href="https://github.com/Mamadouff"><img src="https://github.com/Mamadouff.png" width="60px" alt="" /></a><a href="https://github.com/xuxizhen"><img src="https://github.com/xuxizhen.png" width="60px" alt="xizhen" /></a><a href="https://github.com/ShamrockYuma"><img src="https://github.com/ShamrockYuma.png" width="60px" alt="Elizabeth" /></a><a href="https://github.com/thecatfix"><img src="https://github.com/thecatfix.png" width="60px" alt="John Shelburne" /></a><a href="https://github.com/18900775855"><img src="https://github.com/18900775855.png" width="60px" alt="bin" /></a><a href="https://github.com/destro225"><img src="https://github.com/destro225.png" width="60px" alt="" /></a><a href="https://github.com/0xmatchmaker"><img src="https://github.com/0xmatchmaker.png" width="60px" alt="0xmatchmaker" /></a><a href="https://github.com/dray3310"><img src="https://github.com/dray3310.png" width="60px" alt="" /></a><a href="https://github.com/mgesteban"><img src="https://github.com/mgesteban.png" width="60px" alt="Grace Esteban" /></a><a href="https://github.com/fegodi"><img src="https://github.com/fegodi.png" width="60px" alt="" /></a><a href="https://github.com/kylburns89"><img src="https://github.com/kylburns89.png" width="60px" alt="" /></a><a href="https://github.com/Ademrobert"><img src="https://github.com/Ademrobert.png" width="60px" alt="Adem Ottoman" /></a><a href="https://github.com/mouhaxp"><img src="https://github.com/mouhaxp.png" width="60px" alt="" /></a><a href="https://github.com/carlosbartolomeu"><img src="https://github.com/carlosbartolomeu.png" width="60px" alt="" /></a><a href="https://github.com/Zoffra1984"><img src="https://github.com/Zoffra1984.png" width="60px" alt="" /></a><a href="https://github.com/Agronobeetles"><img src="https://github.com/Agronobeetles.png" width="60px" alt="" /></a><a href="https://github.com/CloudyGuyThompson"><img src="https://github.com/CloudyGuyThompson.png" width="60px" alt="Guy Thompson" /></a><a href="https://github.com/rocogirl-jp"><img src="https://github.com/rocogirl-jp.png" width="60px" alt="" /></a><a href="https://github.com/Jhonvolt17"><img src="https://github.com/Jhonvolt17.png" width="60px" alt="" /></a><a href="https://github.com/koltziver"><img src="https://github.com/koltziver.png" width="60px" alt="" /></a><a href="https://github.com/meeuwsstefaan"><img src="https://github.com/meeuwsstefaan.png" width="60px" alt="Stefaan Meeuws" /></a><a href="https://github.com/sirswali"><img src="https://github.com/sirswali.png" width="60px" alt="Vusi Dube" /></a><a href="https://github.com/Tweezamiza"><img src="https://github.com/Tweezamiza.png" width="60px" alt="" /></a><a href="https://github.com/Kenono2000"><img src="https://github.com/Kenono2000.png" width="60px" alt="Ken Wong" /></a><a href="https://github.com/QuickBenx"><img src="https://github.com/QuickBenx.png" width="60px" alt="" /></a><a href="https://github.com/ouguajian"><img src="https://github.com/ouguajian.png" width="60px" alt="" /></a><a href="https://github.com/DixonFyre"><img src="https://github.com/DixonFyre.png" width="60px" alt="" /></a><a href="https://github.com/ospl99"><img src="https://github.com/ospl99.png" width="60px" alt="" /></a><a href="https://github.com/bbookser"><img src="https://github.com/bbookser.png" width="60px" alt="Bradford Bookser" /></a><a href="https://github.com/abhirichster"><img src="https://github.com/abhirichster.png" width="60px" alt="" /></a><a href="https://github.com/SwingPoint"><img src="https://github.com/SwingPoint.png" width="60px" alt="" /></a><a href="https://github.com/jenius-eagle"><img src="https://github.com/jenius-eagle.png" width="60px" alt="" /></a><a href="https://github.com/CubanCongaMan"><img src="https://github.com/CubanCongaMan.png" width="60px" alt="Roberto Luis Sanchez, P.E., P.G.; D,GE; F.ASCE" /></a><a href="https://github.com/cskrobec"><img src="https://github.com/cskrobec.png" width="60px" alt="" /></a><a href="https://github.com/Jahmazon"><img src="https://github.com/Jahmazon.png" width="60px" alt="" /></a><a href="https://github.com/jackPan010203"><img src="https://github.com/jackPan010203.png" width="60px" alt="" /></a><a href="https://github.com/pmespresso"><img src="https://github.com/pmespresso.png" width="60px" alt="πisk0mate" /></a><a href="https://github.com/itbc2004"><img src="https://github.com/itbc2004.png" width="60px" alt="" /></a><a href="https://github.com/ISDAworld"><img src="https://github.com/ISDAworld.png" width="60px" alt="David Gammond" /></a><a href="https://github.com/mattlwaters"><img src="https://github.com/mattlwaters.png" width="60px" alt="" /></a><a href="https://github.com/Stargard"><img src="https://github.com/Stargard.png" width="60px" alt="" /></a><a href="https://github.com/lazzacapital"><img src="https://github.com/lazzacapital.png" width="60px" alt="Lazza Capital" /></a><a href="https://github.com/MichaelRDionne"><img src="https://github.com/MichaelRDionne.png" width="60px" alt="Michael R Dionne" /></a><a href="https://github.com/nilotpalc"><img src="https://github.com/nilotpalc.png" width="60px" alt="Nilotpal Choudhury" /></a><a href="https://github.com/FrankyPete"><img src="https://github.com/FrankyPete.png" width="60px" alt="" /></a><a href="https://github.com/TylerrDurden1979"><img src="https://github.com/TylerrDurden1979.png" width="60px" alt="" /></a><a href="https://github.com/OptionalJoystick"><img src="https://github.com/OptionalJoystick.png" width="60px" alt="" /></a><a href="https://github.com/PaulV27"><img src="https://github.com/PaulV27.png" width="60px" alt="" /></a><a href="https://github.com/rodolfoguzzi"><img src="https://github.com/rodolfoguzzi.png" width="60px" alt="" /></a><a href="https://github.com/bluecat2210"><img src="https://github.com/bluecat2210.png" width="60px" alt="" /></a><!-- sponsors -->

</div>

---

Az AgentGPT lehetővé teszi az automatizált AI-ügynökök konfigurálását és üzembe helyezését.
Nevezze el saját egyéni mesterséges intelligenciáját, és tegye lehetővé, hogy bármilyen célt elérjen.
Megkísérli elérni a célt az elvégzendő feladatok átgondolásával, végrehajtásával és az eredményekből való tanulással 🚀.

## 🎉 Útiterv

Ez a platform jelenleg béta állapotban van és a következőkön dolgozunk:

- Hosszú távú memória vektoros DB-n keresztül 🧠
- Webböngészési lehetőségek a LangChain-en keresztül 🌐
- Interakció webhelyekkel és emberekkel 👨‍👩‍👦
- Írási lehetőségek egy dokumentum API-n keresztül 📄
- Az AI-ügynökök mentése 💾
- Felhasználók és hitelesítés 🔐
- Stripe integráció egy alsó limites fizetős verzióhoz (hogy ne aggódjunk az infra költségek miatt) 💵

Hamarosan még több jön...

## 🚀 Tech Stack

- ✅ **Bootstrapping**: [create-t3-app](https://create.t3.gg).
- ✅ **Framework**: [Nextjs 13 + Typescript](https://nextjs.org/).
- ✅ **Auth**: [Next-Auth.js](https://next-auth.js.org)
- ✅ **ORM**: [Prisma](https://prisma.io).
- ✅ **Database**: [Supabase](https://supabase.com/).
- ✅ **Styling**: [TailwindCSS + HeadlessUI](https://tailwindcss.com).
- ✅ **Typescript Schema Validation**: [Zod](https://github.com/colinhacks/zod).
- ✅ **End-to-end typesafe API**: [tRPC](https://trpc.io/).

## 👨‍🚀 Első lépések

### 🐳 Docker beállítása

Az AgentGPT helyi futtatásának legegyszerűbb módja a Docker használata.
Egy kényelmes beállítási szkriptet biztosítunk az induláshoz.

```bash
./setup.sh --docker
```

### 👷 Helyi fejlesztési beállítások

Ha helyben szeretné fejleszteni az Agent GPT-t, a legegyszerűbb módja a mellékelt telepítőszkript használata.

```bash
./setup.sh --local
```

### 🛠️ Manuális beállítás

> 🚧 Szüksége lesz a [Nodejs +18 (LTS recommended)](https://nodejs.org/en/) telepítésre.

1. Elágaztatni a tárat

- [Elágaztatás](https://github.com/reworkd/AgentGPT/fork).

2. Klónozni a tárolót:

```bash
<NAME_EMAIL>:YOU_USER/AgentGPT.git
```

3. Függőségek telepítése:

```bash
cd AgentGPT
npm install
```

4. Hozzon létre egy **.env** fájlt a következő tartalommal:

> 🚧 A környezeti változóknak meg kell egyeznie a következő [sémával](https://github.com/reworkd/AgentGPT/blob/main/src/env/schema.mjs).

```bash
# Telepítési környezet:
NODE_ENV=development

# Következő hitelesítési konfiguráció:
# Hozzon létre egy titkos kulcsot az `openssl rand -base64 32` paranccsal
NEXTAUTH_SECRET=VÁLTOZTASS_MEG
NEXTAUTH_URL=http://localhost:3000
DATABASE_URL=file:./db.sqlite

# OpenAI API kulcs
OPENAI_API_KEY=VÁLTOZTASS_MEG
```

5. Módosítsa a prisma sémát az sqlite használatához:

```bash
./prisma/useSqlite.sh
```

**Megjegyzés:** Ezt csak akkor kell megtenni, ha sqlite-ot szeretne használni.

6. Kész 🥳, következő a futtatás:

```bash
# Adatbázis-migrálások létrehozása
npx prisma db push
npm run dev
```

### 🚀 GitHub Codespaces

Állítsa be azonnal az AgentGPT-t a felhőben a [GitHub Codespaces](https://github.com/features/codespaces) használatával.

1. A GitHub-tárhelyen kattintson a zöld "Kód" gombra, és válassza a "Kódterek" lehetőséget.
2. Hozzon létre egy új kódteret, vagy válasszon egy előzőt, amelyet már létrehozott.
3. A kódtér külön lapon nyílik meg a böngészőben.
4. A terminálban futtassa a `bash ./setup.sh --local` parancsot
5. Amikor a terminál kéri, adja hozzá OpenAI API-kulcsát.
6. Kattintson a "Megnyitás böngészőben" gombra, amikor az összeállítási folyamat befejeződött.

- Az AgentGPT leállításához írja be a Ctrl+C billentyűkombinációt a terminálba.
- Az AgentGPT újraindításához futtassa az "npm run dev" parancsot a terminálban.

Futtassa a projektet 🥳

```
npm run dev
```

---

<h3 align="center">
🙌🏻 További szponzoraink 🙌🏻
</h3>

<div align="center" dir="auto">
  <a href="https://github.com/Trecares" style="display: inline-block; margin-right: 20px;">
    <img src="https://github.com/Trecares.png" width="50px" alt="Trecares" style="max-width:100%;">
  </a>
  <a href="https://github.com/oryanmoshe" style="display: inline-block; margin-right: 20px;">
    <img src="https://github.com/oryanmoshe.png" width="50px" alt="oryanmoshe" style="max-width:100%;">
  </a>
  <a href="https://github.com/rekimcn" style="display: inline-block; margin-right: 20px;">
    <img src="https://github.com/rekimcn.png" width="50px" alt="rekimcn" style="max-width:100%;">
  </a>
  <a href="https://github.com/qwe777897" style="display: inline-block; margin-right: 20px;">
    <img src="https://github.com/qwe777897.png" width="50px" alt="qwe777897" style="max-width:100%;">
  </a>
  <a href="https://github.com/ClayNelson" style="display: inline-block; margin-right: 20px;">
    <img src="https://github.com/ClayNelson.png" width="50px" alt="ClayNelson" style="max-width:100%;">
  </a>
  <a href="https://github.com/xuxizhen" style="display: inline-block; margin-right: 20px;">
    <img src="https://github.com/xuxizhen.png" width="50px" alt="xuxizhen" style="max-width:100%;">
  </a>
  <a href="https://github.com/destro225" style="display: inline-block; margin-right: 20px;">
    <img src="https://github.com/destro225.png" width="50px" alt="destro225" style="max-width:100%;">
  </a>
  <a href="https://github.com/mouhaxp" style="display: inline-block; margin-right: 20px;">
    <img src="https://github.com/mouhaxp.png" width="50px" alt="mouhaxp" style="max-width:100%;">
  </a>
  <a href="https://github.com/carlosbartolomeu" style="display: inline-block; margin-right: 20px;">
    <img src="https://github.com/carlosbartolomeu.png" width="50px" alt="carlosbartolomeu" style="max-width:100%;">
  </a>
  <a href="https://github.com/Agronobeetles" style="display: inline-block; margin-right: 20px;">
    <img src="https://github.com/Agronobeetles.png" width="50px" alt="Agronobeetles " style="max-width:100%;">
  </a>
  <a href="https://github.com/CloudyGuyThompson" style="display: inline-block; margin-right: 20px;">
    <img src="https://github.com/CloudyGuyThompson.png" width="50px" alt="CloudyGuyThompson" style="max-width:100%;">
  </a>
  <a href="https://github.com/xinghz" style="display: inline-block; margin-right: 20px;">
    <img src="https://github.com/xinghz.png" width="50px" alt="xinghz" style="max-width:100%;">
  </a>
  <a href="https://github.com/Jaimbart" style="display: inline-block; margin-right: 20px;">
    <img src="https://github.com/Jaimbart.png" width="50px" alt="Jaimbart" style="max-width:100%;">
  </a>
  <a href="https://github.com/Jhonvolt17" style="display: inline-block; margin-right: 20px;">
    <img src="https://github.com/Jhonvolt17.png" width="50px" alt="Jhonvolt17" style="max-width:100%;">
  </a>
  <a href="https://github.com/koltziver" style="display: inline-block; margin-right: 20px;">
    <img src="https://github.com/koltziver.png" width="50px" alt="koltziver" style="max-width:100%;">
  </a>
  <a href="https://github.com/sirswali" style="display: inline-block; margin-right: 20px;">
    <img src="https://github.com/sirswali.png" width="50px" alt="sirswali" style="max-width:100%;">
  </a>
  <a href="https://github.com/DixonFyre" style="display: inline-block; margin-right: 20px;">
    <img src="https://github.com/DixonFyre.png" width="50px" alt="DixonFyre" style="max-width:100%;">
  </a>

</div>
