#!/usr/bin/env python3
"""
Local Multimodal Processor - Image analysis with local vision models
"""

import asyncio
import base64
import json
import logging
import os
import requests
import tempfile
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from pathlib import Path
import io

# Image processing imports
try:
    from PIL import Image
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("local-multimodal")

@dataclass
class ImageAnalysisResult:
    """Result of image analysis"""
    description: str
    objects_detected: List[str]
    text_extracted: str
    analysis_confidence: float
    model_used: str
    processing_time: float

class LocalMultimodalProcessor:
    """Local multimodal processing with vision models"""
    
    def __init__(self):
        self.initialized = False
        self.vision_model = "qwen2.5vl:32b"
        self.ollama_url = "http://localhost:11434"
        self.supported_formats = ['.jpg', '.jpeg', '.png', '.bmp', '.gif', '.webp']
        self.max_image_size = (1024, 1024)  # Max dimensions for processing
        
    async def initialize(self):
        """Initialize the multimodal processor"""
        try:
            logger.info("🔄 Initializing Local Multimodal Processor...")
            
            # Check dependencies
            if not PIL_AVAILABLE:
                logger.error("❌ PIL (Pillow) not available. Install with: pip install Pillow")
                return False
            
            # Test Ollama connection
            if not await self._test_ollama_connection():
                logger.error("❌ Ollama connection failed")
                return False
            
            # Test vision model
            if not await self._test_vision_model():
                logger.error("❌ Vision model test failed")
                return False
            
            self.initialized = True
            logger.info("✅ Local Multimodal Processor initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize multimodal processor: {e}")
            return False
    
    async def _test_ollama_connection(self) -> bool:
        """Test connection to Ollama server"""
        try:
            response = requests.get(f"{self.ollama_url}/api/tags", timeout=5)
            return response.status_code == 200
        except Exception as e:
            logger.error(f"❌ Ollama connection test failed: {e}")
            return False
    
    async def _test_vision_model(self) -> bool:
        """Test the vision model availability"""
        try:
            response = requests.get(f"{self.ollama_url}/api/tags", timeout=10)
            if response.status_code == 200:
                models = response.json()
                model_names = [model['name'] for model in models.get('models', [])]
                return any(self.vision_model in name for name in model_names)
            return False
        except Exception as e:
            logger.error(f"❌ Vision model test failed: {e}")
            return False
    
    def _prepare_image(self, image_path: str) -> Optional[str]:
        """Prepare image for processing - resize and encode to base64"""
        try:
            with Image.open(image_path) as img:
                # Convert to RGB if necessary
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # Resize if too large
                if img.size[0] > self.max_image_size[0] or img.size[1] > self.max_image_size[1]:
                    img.thumbnail(self.max_image_size, Image.Resampling.LANCZOS)
                    logger.info(f"🔄 Resized image to {img.size}")
                
                # Convert to base64
                buffer = io.BytesIO()
                img.save(buffer, format='JPEG', quality=85)
                img_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
                
                return img_base64
                
        except Exception as e:
            logger.error(f"❌ Image preparation failed: {e}")
            return None
    
    async def _analyze_with_vision_model(self, image_base64: str, prompt: str) -> Optional[str]:
        """Analyze image using the vision model"""
        try:
            payload = {
                "model": self.vision_model,
                "prompt": prompt,
                "images": [image_base64],
                "stream": False
            }
            
            logger.info(f"🔍 Analyzing image with {self.vision_model}...")
            
            response = requests.post(
                f"{self.ollama_url}/api/generate",
                json=payload,
                timeout=120  # Vision models can be slow
            )
            
            if response.status_code == 200:
                result = response.json()
                return result.get("response", "")
            else:
                logger.error(f"❌ Vision model request failed: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"❌ Vision model analysis failed: {e}")
            return None
    
    async def analyze_image(self, image_path: str, analysis_type: str = "general") -> Optional[ImageAnalysisResult]:
        """Analyze an image with the specified analysis type"""
        if not self.initialized:
            logger.error("❌ Multimodal processor not initialized")
            return None
        
        try:
            import time
            start_time = time.time()
            
            logger.info(f"🖼️ Analyzing image: {image_path}")
            
            # Check if file exists and is supported
            if not os.path.exists(image_path):
                logger.error(f"❌ Image file not found: {image_path}")
                return None
            
            file_ext = Path(image_path).suffix.lower()
            if file_ext not in self.supported_formats:
                logger.error(f"❌ Unsupported image format: {file_ext}")
                return None
            
            # Prepare image
            image_base64 = self._prepare_image(image_path)
            if not image_base64:
                return None
            
            # Create analysis prompt based on type
            prompts = {
                "general": "Describe this image in detail. What do you see? Include objects, people, activities, colors, and overall scene.",
                "objects": "List all the objects you can identify in this image. Be specific and comprehensive.",
                "text": "Extract and transcribe any text visible in this image. If no text is present, say 'No text detected'.",
                "detailed": "Provide a comprehensive analysis of this image including: 1) Overall description, 2) Objects and people present, 3) Activities or actions, 4) Colors and lighting, 5) Text if any, 6) Mood or atmosphere.",
                "technical": "Analyze this image from a technical perspective: composition, lighting, quality, resolution, and any technical aspects you can observe."
            }
            
            prompt = prompts.get(analysis_type, prompts["general"])
            
            # Analyze with vision model
            analysis_text = await self._analyze_with_vision_model(image_base64, prompt)
            if not analysis_text:
                return None
            
            # Extract objects (simple keyword extraction)
            objects_detected = self._extract_objects_from_description(analysis_text)
            
            # Extract text (if mentioned in analysis)
            text_extracted = self._extract_text_from_description(analysis_text)
            
            # Calculate confidence (placeholder - could be enhanced)
            confidence = 0.85 if len(analysis_text) > 50 else 0.6
            
            processing_time = time.time() - start_time
            
            result = ImageAnalysisResult(
                description=analysis_text,
                objects_detected=objects_detected,
                text_extracted=text_extracted,
                analysis_confidence=confidence,
                model_used=self.vision_model,
                processing_time=processing_time
            )
            
            logger.info(f"✅ Image analysis completed in {processing_time:.2f}s")
            return result
            
        except Exception as e:
            logger.error(f"❌ Image analysis failed: {e}")
            return None
    
    def _extract_objects_from_description(self, description: str) -> List[str]:
        """Extract object names from description text"""
        # Simple keyword extraction - could be enhanced with NLP
        common_objects = [
            'person', 'people', 'man', 'woman', 'child', 'car', 'truck', 'bus', 'bicycle',
            'dog', 'cat', 'bird', 'tree', 'building', 'house', 'road', 'sky', 'cloud',
            'table', 'chair', 'computer', 'phone', 'book', 'cup', 'bottle', 'food',
            'window', 'door', 'wall', 'floor', 'ceiling', 'light', 'sign', 'text'
        ]
        
        found_objects = []
        description_lower = description.lower()
        
        for obj in common_objects:
            if obj in description_lower:
                found_objects.append(obj)
        
        return found_objects
    
    def _extract_text_from_description(self, description: str) -> str:
        """Extract mentioned text from description"""
        # Look for text mentions in the description
        text_indicators = ['text', 'sign', 'writing', 'words', 'letters', 'says', 'reads']
        
        for indicator in text_indicators:
            if indicator in description.lower():
                # Try to extract quoted text or text after indicators
                lines = description.split('\n')
                for line in lines:
                    if indicator in line.lower():
                        return line.strip()
        
        return "No text detected"
    
    async def analyze_image_from_bytes(self, image_bytes: bytes, filename: str, analysis_type: str = "general") -> Optional[ImageAnalysisResult]:
        """Analyze image from bytes data"""
        try:
            # Save bytes to temporary file
            with tempfile.NamedTemporaryFile(delete=False, suffix=Path(filename).suffix) as temp_file:
                temp_file.write(image_bytes)
                temp_path = temp_file.name
            
            # Analyze the temporary file
            result = await self.analyze_image(temp_path, analysis_type)
            
            # Clean up temporary file
            os.unlink(temp_path)
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Image analysis from bytes failed: {e}")
            return None
    
    def get_status(self) -> Dict[str, Any]:
        """Get processor status"""
        return {
            "initialized": self.initialized,
            "vision_model": self.vision_model,
            "pil_available": PIL_AVAILABLE,
            "supported_formats": self.supported_formats,
            "max_image_size": self.max_image_size,
            "capabilities": [
                "image_analysis",
                "object_detection",
                "text_extraction",
                "scene_description"
            ] if self.initialized else []
        }

# Global instance
_multimodal_processor = None

async def get_multimodal_processor() -> LocalMultimodalProcessor:
    """Get or create multimodal processor instance"""
    global _multimodal_processor
    
    if _multimodal_processor is None:
        _multimodal_processor = LocalMultimodalProcessor()
        await _multimodal_processor.initialize()
    
    return _multimodal_processor

if __name__ == "__main__":
    async def test_multimodal():
        processor = await get_multimodal_processor()
        
        if processor.initialized:
            print("✅ Multimodal processor initialized")
            print(f"Status: {json.dumps(processor.get_status(), indent=2)}")
        else:
            print("❌ Failed to initialize multimodal processor")
    
    asyncio.run(test_multimodal())
