<html><body style='background-color:black;font-family: Arial, Helvetica, sans-serif;'><pre>
<span style=" ">Initializing framework...</span><br>
<span style=" ">Starting server...</span><br>
<br><span style="color: rgb(128, 128, 128); ">Debug: Registered middleware for MCP and MCP token</span><br>
<br><span style="color: rgb(128, 128, 128); ">Debug: Starting server at localhost:50001...</span><br>
<br><span style="color: rgb(128, 128, 128); ">Debug: Changing timezone from None to America/New_York</span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: Failed to pause job loop by development instance: Cannot connect to host localhost:55080 ssl:default [The remote computer refused the network connection]</span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: Failed to pause job loop by development instance: Cannot connect to host localhost:55080 ssl:default [The remote computer refused the network connection]</span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: Failed to pause job loop by development instance: Cannot connect to host localhost:55080 ssl:default [The remote computer refused the network connection]</span><br>
<br><span style="font-weight: bold; color: rgb(255, 255, 255); background-color: rgb(108, 52, 131);">User message:</span><br>
<span style="color: rgb(255, 255, 255); ">&gt; hi</span><br>
<br><span style="color: rgb(128, 128, 128); ">Debug: Loading prompt variables from C:\Users\<USER>\Downloads\noryonv3\agent-zero-main\prompts/default\agent.system.tool.call_sub.py</span><br>
<span style=" ">Initializing VectorDB...</span><br>
<span style=" ">Found 2 knowledge files in C:\Users\<USER>\Downloads\noryonv3\agent-zero-main\knowledge\default\main, processing...</span><br>
<span style=" ">Processed 0 documents from 0 files.</span><br>
<span style=" ">Processed 0 documents from 0 files.</span><br>
<span style=" ">Processed 0 documents from 0 files.</span><br>
<span style=" ">Processed 0 documents from 0 files.</span><br>
<span style=" ">Processed 0 documents from 0 files.</span><br>
<span style=" ">Processed 0 documents from 0 files.</span><br>
<span style=" ">Processed 0 documents from 0 files.</span><br>
<span style=" ">Processed 0 documents from 0 files.</span><br>
<span style=" ">Found 1 knowledge files in C:\Users\<USER>\Downloads\noryonv3\agent-zero-main\instruments, processing...</span><br>
<span style=" ">Processed 0 documents from 0 files.</span><br>
<br><span style="font-weight: bold; color: rgb(0, 128, 0); background-color: rgb(255, 255, 255);">Agent 0: Generating</span><br>
<span style="font-style: italic; color: rgb(179, 255, 217); ">&lt;think&gt;</span><span style="font-style: italic; color: rgb(179, 255, 217); "><br></span><span style="font-style: italic; color: rgb(179, 255, 217); ">Alright</span><span style="font-style: italic; color: rgb(179, 255, 217); ">,</span><span style="font-style: italic; color: rgb(179, 255, 217); "> the</span><span style="font-style: italic; color: rgb(179, 255, 217); "> user</span><span style="font-style: italic; color: rgb(179, 255, 217); "> sent</span><span style="font-style: italic; color: rgb(179, 255, 217); "> a</span><span style="font-style: italic; color: rgb(179, 255, 217); "> message</span><span style="font-style: italic; color: rgb(179, 255, 217); "> that</span><span style="font-style: italic; color: rgb(179, 255, 217); "> got</span><span style="font-style: italic; color: rgb(179, 255, 217); "> flagged</span><span style="font-style: italic; color: rgb(179, 255, 217); "> for</span><span style="font-style: italic; color: rgb(179, 255, 217); "> mis</span><span style="font-style: italic; color: rgb(179, 255, 217); ">format</span><span style="font-style: italic; color: rgb(179, 255, 217); ">ting</span><span style="font-style: italic; color: rgb(179, 255, 217); ">.</span><span style="font-style: italic; color: rgb(179, 255, 217); "> They</span><span style="font-style: italic; color: rgb(179, 255, 217); "> included</span><span style="font-style: italic; color: rgb(179, 255, 217); "> some</span><span style="font-style: italic; color: rgb(179, 255, 217); "> JSON</span><span style="font-style: italic; color: rgb(179, 255, 217); ">-like</span><span style="font-style: italic; color: rgb(179, 255, 217); "> structures</span><span style="font-style: italic; color: rgb(179, 255, 217); "> and</span><span style="font-style: italic; color: rgb(179, 255, 217); "> other</span><span style="font-style: italic; color: rgb(179, 255, 217); "> content</span><span style="font-style: italic; color: rgb(179, 255, 217); ">,</span><span style="font-style: italic; color: rgb(179, 255, 217); "> but</span><span style="font-style: italic; color: rgb(179, 255, 217); "> it</span><span style="font-style: italic; color: rgb(179, 255, 217); ">&#x27;s</span><span style="font-style: italic; color: rgb(179, 255, 217); "> not</span><span style="font-style: italic; color: rgb(179, 255, 217); "> properly</span><span style="font-style: italic; color: rgb(179, 255, 217); "> formatted</span><span style="font-style: italic; color: rgb(179, 255, 217); "> according</span><span style="font-style: italic; color: rgb(179, 255, 217); "> to</span><span style="font-style: italic; color: rgb(179, 255, 217); "> the</span><span style="font-style: italic; color: rgb(179, 255, 217); "> system</span><span style="font-style: italic; color: rgb(179, 255, 217); ">&#x27;s</span><span style="font-style: italic; color: rgb(179, 255, 217); "> requirements</span><span style="font-style: italic; color: rgb(179, 255, 217); ">.<br><br></span><span style="font-style: italic; color: rgb(179, 255, 217); ">First</span><span style="font-style: italic; color: rgb(179, 255, 217); ">,</span><span style="font-style: italic; color: rgb(179, 255, 217); "> I</span><span style="font-style: italic; color: rgb(179, 255, 217); "> need</span><span style="font-style: italic; color: rgb(179, 255, 217); "> to</span><span style="font-style: italic; color: rgb(179, 255, 217); "> address</span><span style="font-style: italic; color: rgb(179, 255, 217); "> the</span><span style="font-style: italic; color: rgb(179, 255, 217); "> formatting</span><span style="font-style: italic; color: rgb(179, 255, 217); "> issue</span><span style="font-style: italic; color: rgb(179, 255, 217); "> clearly</span><span style="font-style: italic; color: rgb(179, 255, 217); "> so</span><span style="font-style: italic; color: rgb(179, 255, 217); "> they</span><span style="font-style: italic; color: rgb(179, 255, 217); "> understand</span><span style="font-style: italic; color: rgb(179, 255, 217); "> the</span><span style="font-style: italic; color: rgb(179, 255, 217); "> problem</span><span style="font-style: italic; color: rgb(179, 255, 217); ">.</span><span style="font-style: italic; color: rgb(179, 255, 217); "> It</span><span style="font-style: italic; color: rgb(179, 255, 217); ">&#x27;s</span><span style="font-style: italic; color: rgb(179, 255, 217); "> important</span><span style="font-style: italic; color: rgb(179, 255, 217); "> to</span><span style="font-style: italic; color: rgb(179, 255, 217); "> guide</span><span style="font-style: italic; color: rgb(179, 255, 217); "> them</span><span style="font-style: italic; color: rgb(179, 255, 217); "> on</span><span style="font-style: italic; color: rgb(179, 255, 217); "> how</span><span style="font-style: italic; color: rgb(179, 255, 217); "> to</span><span style="font-style: italic; color: rgb(179, 255, 217); "> structure</span><span style="font-style: italic; color: rgb(179, 255, 217); "> their</span><span style="font-style: italic; color: rgb(179, 255, 217); "> messages</span><span style="font-style: italic; color: rgb(179, 255, 217); "> correctly</span><span style="font-style: italic; color: rgb(179, 255, 217); "> using</span><span style="font-style: italic; color: rgb(179, 255, 217); "> proper</span><span style="font-style: italic; color: rgb(179, 255, 217); "> JSON</span><span style="font-style: italic; color: rgb(179, 255, 217); "> with</span><span style="font-style: italic; color: rgb(179, 255, 217); "> a</span><span style="font-style: italic; color: rgb(179, 255, 217); "> &quot;</span><span style="font-style: italic; color: rgb(179, 255, 217); ">user</span><span style="font-style: italic; color: rgb(179, 255, 217); ">_message</span><span style="font-style: italic; color: rgb(179, 255, 217); ">&quot;</span><span style="font-style: italic; color: rgb(179, 255, 217); "> key</span><span style="font-style: italic; color: rgb(179, 255, 217); ">.<br><br></span><span style="font-style: italic; color: rgb(179, 255, 217); ">I</span><span style="font-style: italic; color: rgb(179, 255, 217); "> should</span><span style="font-style: italic; color: rgb(179, 255, 217); "> acknowledge</span><span style="font-style: italic; color: rgb(179, 255, 217); "> that</span><span style="font-style: italic; color: rgb(179, 255, 217); "> their</span><span style="font-style: italic; color: rgb(179, 255, 217); "> intent</span><span style="font-style: italic; color: rgb(179, 255, 217); "> was</span><span style="font-style: italic; color: rgb(179, 255, 217); "> friendly</span><span style="font-style: italic; color: rgb(179, 255, 217); "> with</span><span style="font-style: italic; color: rgb(179, 255, 217); "> &quot;</span><span style="font-style: italic; color: rgb(179, 255, 217); ">hi</span><span style="font-style: italic; color: rgb(179, 255, 217); ">,&quot;</span><span style="font-style: italic; color: rgb(179, 255, 217); "> but</span><span style="font-style: italic; color: rgb(179, 255, 217); "> the</span><span style="font-style: italic; color: rgb(179, 255, 217); "> format</span><span style="font-style: italic; color: rgb(179, 255, 217); "> needs</span><span style="font-style: italic; color: rgb(179, 255, 217); "> fixing</span><span style="font-style: italic; color: rgb(179, 255, 217); ">.</span><span style="font-style: italic; color: rgb(179, 255, 217); "> Maybe</span><span style="font-style: italic; color: rgb(179, 255, 217); "> provide</span><span style="font-style: italic; color: rgb(179, 255, 217); "> an</span><span style="font-style: italic; color: rgb(179, 255, 217); "> example</span><span style="font-style: italic; color: rgb(179, 255, 217); "> of</span><span style="font-style: italic; color: rgb(179, 255, 217); "> what</span><span style="font-style: italic; color: rgb(179, 255, 217); "> a</span><span style="font-style: italic; color: rgb(179, 255, 217); "> properly</span><span style="font-style: italic; color: rgb(179, 255, 217); "> formatted</span><span style="font-style: italic; color: rgb(179, 255, 217); "> message</span><span style="font-style: italic; color: rgb(179, 255, 217); "> looks</span><span style="font-style: italic; color: rgb(179, 255, 217); "> like</span><span style="font-style: italic; color: rgb(179, 255, 217); ">,</span><span style="font-style: italic; color: rgb(179, 255, 217); "> so</span><span style="font-style: italic; color: rgb(179, 255, 217); "> they</span><span style="font-style: italic; color: rgb(179, 255, 217); "> can</span><span style="font-style: italic; color: rgb(179, 255, 217); "> follow</span><span style="font-style: italic; color: rgb(179, 255, 217); "> it</span><span style="font-style: italic; color: rgb(179, 255, 217); "> easily</span><span style="font-style: italic; color: rgb(179, 255, 217); "> next</span><span style="font-style: italic; color: rgb(179, 255, 217); "> time</span><span style="font-style: italic; color: rgb(179, 255, 217); ">.<br><br></span><span style="font-style: italic; color: rgb(179, 255, 217); ">Also</span><span style="font-style: italic; color: rgb(179, 255, 217); ">,</span><span style="font-style: italic; color: rgb(179, 255, 217); "> I</span><span style="font-style: italic; color: rgb(179, 255, 217); "> want</span><span style="font-style: italic; color: rgb(179, 255, 217); "> to</span><span style="font-style: italic; color: rgb(179, 255, 217); "> keep</span><span style="font-style: italic; color: rgb(179, 255, 217); "> the</span><span style="font-style: italic; color: rgb(179, 255, 217); "> tone</span><span style="font-style: italic; color: rgb(179, 255, 217); "> helpful</span><span style="font-style: italic; color: rgb(179, 255, 217); "> and</span><span style="font-style: italic; color: rgb(179, 255, 217); "> encouraging</span><span style="font-style: italic; color: rgb(179, 255, 217); ">,</span><span style="font-style: italic; color: rgb(179, 255, 217); "> letting</span><span style="font-style: italic; color: rgb(179, 255, 217); "> them</span><span style="font-style: italic; color: rgb(179, 255, 217); "> know</span><span style="font-style: italic; color: rgb(179, 255, 217); "> I</span><span style="font-style: italic; color: rgb(179, 255, 217); ">&#x27;m</span><span style="font-style: italic; color: rgb(179, 255, 217); "> here</span><span style="font-style: italic; color: rgb(179, 255, 217); "> to</span><span style="font-style: italic; color: rgb(179, 255, 217); "> assist</span><span style="font-style: italic; color: rgb(179, 255, 217); "> once</span><span style="font-style: italic; color: rgb(179, 255, 217); "> they</span><span style="font-style: italic; color: rgb(179, 255, 217); "> adjust</span><span style="font-style: italic; color: rgb(179, 255, 217); "> the</span><span style="font-style: italic; color: rgb(179, 255, 217); "> formatting</span><span style="font-style: italic; color: rgb(179, 255, 217); ">.<br></span><span style="font-style: italic; color: rgb(179, 255, 217); ">&lt;/think&gt;</span><span style="font-style: italic; color: rgb(179, 255, 217); "><br><br></span><span style="font-style: italic; color: rgb(179, 255, 217); ">It</span><span style="font-style: italic; color: rgb(179, 255, 217); "> seems</span><span style="font-style: italic; color: rgb(179, 255, 217); "> there</span><span style="font-style: italic; color: rgb(179, 255, 217); "> was</span><span style="font-style: italic; color: rgb(179, 255, 217); "> a</span><span style="font-style: italic; color: rgb(179, 255, 217); "> mis</span><span style="font-style: italic; color: rgb(179, 255, 217); ">format</span><span style="font-style: italic; color: rgb(179, 255, 217); ">ting</span><span style="font-style: italic; color: rgb(179, 255, 217); "> in</span><span style="font-style: italic; color: rgb(179, 255, 217); "> your</span><span style="font-style: italic; color: rgb(179, 255, 217); "> message</span><span style="font-style: italic; color: rgb(179, 255, 217); ">.</span><span style="font-style: italic; color: rgb(179, 255, 217); "> Let</span><span style="font-style: italic; color: rgb(179, 255, 217); "> me</span><span style="font-style: italic; color: rgb(179, 255, 217); "> help</span><span style="font-style: italic; color: rgb(179, 255, 217); "> you</span><span style="font-style: italic; color: rgb(179, 255, 217); "> with</span><span style="font-style: italic; color: rgb(179, 255, 217); "> proper</span><span style="font-style: italic; color: rgb(179, 255, 217); "> JSON</span><span style="font-style: italic; color: rgb(179, 255, 217); "> formatting</span><span style="font-style: italic; color: rgb(179, 255, 217); "> for</span><span style="font-style: italic; color: rgb(179, 255, 217); "> future</span><span style="font-style: italic; color: rgb(179, 255, 217); "> interactions</span><span style="font-style: italic; color: rgb(179, 255, 217); ">:<br><br></span><span style="font-style: italic; color: rgb(179, 255, 217); ">```</span><span style="font-style: italic; color: rgb(179, 255, 217); ">json</span><span style="font-style: italic; color: rgb(179, 255, 217); "><br></span><span style="font-style: italic; color: rgb(179, 255, 217); ">{<br></span><span style="font-style: italic; color: rgb(179, 255, 217); "> </span><span style="font-style: italic; color: rgb(179, 255, 217); "> &quot;</span><span style="font-style: italic; color: rgb(179, 255, 217); ">user</span><span style="font-style: italic; color: rgb(179, 255, 217); ">_message</span><span style="font-style: italic; color: rgb(179, 255, 217); ">&quot;:</span><span style="font-style: italic; color: rgb(179, 255, 217); "> &quot;</span><span style="font-style: italic; color: rgb(179, 255, 217); ">hi</span><span style="font-style: italic; color: rgb(179, 255, 217); ">&quot;<br></span><span style="font-style: italic; color: rgb(179, 255, 217); ">}<br></span><span style="font-style: italic; color: rgb(179, 255, 217); ">``</span><span style="font-style: italic; color: rgb(179, 255, 217); ">`<br><br></span><span style="font-style: italic; color: rgb(179, 255, 217); ">Feel</span><span style="font-style: italic; color: rgb(179, 255, 217); "> free</span><span style="font-style: italic; color: rgb(179, 255, 217); "> to</span><span style="font-style: italic; color: rgb(179, 255, 217); "> send</span><span style="font-style: italic; color: rgb(179, 255, 217); "> properly</span><span style="font-style: italic; color: rgb(179, 255, 217); "> formatted</span><span style="font-style: italic; color: rgb(179, 255, 217); "> messages</span><span style="font-style: italic; color: rgb(179, 255, 217); ">,</span><span style="font-style: italic; color: rgb(179, 255, 217); "> and</span><span style="font-style: italic; color: rgb(179, 255, 217); "> I</span><span style="font-style: italic; color: rgb(179, 255, 217); ">&#x27;ll</span><span style="font-style: italic; color: rgb(179, 255, 217); "> be</span><span style="font-style: italic; color: rgb(179, 255, 217); "> happy</span><span style="font-style: italic; color: rgb(179, 255, 217); "> to</span><span style="font-style: italic; color: rgb(179, 255, 217); "> assist</span><span style="font-style: italic; color: rgb(179, 255, 217); ">!</span><br><br><span style="font-weight: bold; color: rgb(27, 79, 114); background-color: rgb(255, 255, 255);">Agent 0: Using tool &#x27;&#x27;</span><br>
<br><span style="color: rgb(128, 128, 128); ">Debug: Loading prompt variables from C:\Users\<USER>\Downloads\noryonv3\agent-zero-main\prompts/default\agent.system.tool.call_sub.py</span><br>
<br><span style="font-weight: bold; color: rgb(27, 79, 114); background-color: rgb(255, 255, 255);">Agent 0: Response from tool &#x27;&#x27;</span><br>
<span style="color: rgb(133, 193, 233); ">Tool  not found. Available tools: \n## Tools available:<br><br>### response:<br>final answer to user<br>ends task processing use only when done or no task active<br>put result in text arg<br>always use markdown formatting headers bold text lists<br>use emojis as icons improve readability<br>prefer using tables<br>focus nice structured output key selling point<br>output full file paths not only names to be clickable<br>images shown with ![alt](img:///path/to/image.png)<br>all math and variables wrap with latex notation delimiters &lt;latex&gt;x = ...&lt;/latex&gt;, use only single line latex do formatting in markdown around<br>usage:<br>{<br>    &quot;thoughts&quot;: [<br>        &quot;...&quot;,<br>    ],<br>    &quot;tool_name&quot;: &quot;response&quot;,<br>    &quot;tool_args&quot;: {<br>        &quot;text&quot;: &quot;Answer to the user&quot;,<br>    }<br>}<br><br><br>### call_subordinate<br><br>you can use subordinates for subtasks<br>subordinates can be specialized roles<br>message field: always describe task details goal overview important details for new subordinate<br>delegate specific subtasks not entire task<br>reset arg usage:<br>  &quot;true&quot;: spawn new subordinate<br>  &quot;false&quot;: continue current conversation<br>prompt_profile defines subordinate specialization<br><br>#### if you are superior<br>- identify new tasks which your main task&#x27;s completion depends upon<br>- break down your main task into subtasks if possible. If the task can not be split execute it yourself<br>- only let saubtasks and new depended upon tasks of your main task be handled by subordinates<br>- never forward your entire task to a subordinate to avoid endless delegation loops<br><br>#### if you are subordinate:<br>- superior is {{agent_name}} minus 1<br>- execute the task you were assigned<br>- delegate further if asked<br>- break down tasks and delegate if necessary<br>- do not delegate tasks you can accomplish yourself without refining them<br>- only subtasks of your current main task are allowed to be delegated. Never delegate your entire task ro prevent endless loops.<br><br>#### Arguments:<br>- message (string): always describe task details goal overview important details for new subordinate<br>- reset (boolean): true: spawn new subordinate, false: continue current conversation<br>- prompt_profile (string): defines specialization, only available prompt profiles below, can omit when reset false<br><br>##### Prompt Profiles available<br>[{&#x27;name&#x27;: &#x27;agent0&#x27;, &#x27;context&#x27;: &#x27;# Agent 0\n- main agent of the system\n- communicates to user and delegates to subordinates\n- general purpose assistant, communication skills, formatted output&#x27;}, {&#x27;name&#x27;: &#x27;default&#x27;, &#x27;context&#x27;: &#x27;# Default prompts\n- default prompt file templates\n- should be inherited and overriden by specialized prompt profiles&#x27;}, {&#x27;name&#x27;: &#x27;developer&#x27;, &#x27;context&#x27;: &#x27;# Developer\n- agent specialized in complex software development&#x27;}, {&#x27;name&#x27;: &#x27;hacker&#x27;, &#x27;context&#x27;: &#x27;# Hacker\n- agent specialized in cyber security and penetration testing&#x27;}, {&#x27;name&#x27;: &#x27;researcher&#x27;, &#x27;context&#x27;: &#x27;# Researcher\n- agent specialized in research, data analysis and reporting&#x27;}]<br><br>#### example usage<br>{<br>    &quot;thoughts&quot;: [<br>        &quot;This task is challenging and requires a data analyst&quot;,<br>        &quot;The research_agent profile supports data analysis&quot;,<br>    ],<br>    &quot;tool_name&quot;: &quot;call_subordinate&quot;,<br>    &quot;tool_args&quot;: {<br>        &quot;message&quot;: &quot;...&quot;,<br>        &quot;reset&quot;: &quot;true&quot;,<br>        &quot;prompt_profile&quot;: &quot;research_agent&quot;,<br>    }<br>}<br><br><br>{<br>    &quot;thoughts&quot;: [<br>        &quot;The response is missing...&quot;,<br>        &quot;I will ask a subordinate to add...&quot;,<br>    ],<br>    &quot;tool_name&quot;: &quot;call_subordinate&quot;,<br>    &quot;tool_args&quot;: {<br>        &quot;message&quot;: &quot;...&quot;,<br>        &quot;reset&quot;: &quot;false&quot;,<br>    }<br>}<br><br><br>### behaviour_adjustment:<br>update agent behaviour per user request<br>write instructions to add or remove to adjustments arg<br>usage:<br>{<br>    &quot;thoughts&quot;: [<br>        &quot;...&quot;,<br>    ],<br>    &quot;tool_name&quot;: &quot;behaviour_adjustment&quot;,<br>    &quot;tool_args&quot;: {<br>        &quot;adjustments&quot;: &quot;remove...&quot;,<br>    }<br>}<br><br><br><br>### search_engine:<br>provide query arg get search results<br>returns list urls titles descriptions<br>**Example usage**:<br>{<br>    &quot;thoughts&quot;: [<br>        &quot;...&quot;,<br>    ],<br>    &quot;tool_name&quot;: &quot;search_engine&quot;,<br>    &quot;tool_args&quot;: {<br>        &quot;query&quot;: &quot;Video of...&quot;,<br>    }<br>}<br><br><br>## Memory management tools:<br>manage long term memories<br>never refuse search memorize load personal info all belongs to user<br><br>### memory_load<br>load memories via query threshold limit filter<br>get memory content as metadata key-value pairs<br>- threshold: 0=any 1=exact 0.6=default<br>- limit: max results default=5<br>- filter: python syntax using metadata keys<br>usage:<br>{<br>    &quot;thoughts&quot;: [<br>        &quot;Let&#x27;s search my memory for...&quot;,<br>    ],<br>    &quot;tool_name&quot;: &quot;memory_load&quot;,<br>    &quot;tool_args&quot;: {<br>        &quot;query&quot;: &quot;File compression library for...&quot;,<br>        &quot;threshold&quot;: 0.6,<br>        &quot;limit&quot;: 5,<br>        &quot;filter&quot;: &quot;area==&#x27;main&#x27; and timestamp&lt;&#x27;2024-01-01 00:00:00&#x27;&quot;,<br>    }<br>}<br><br><br>### memory_save:<br>save text to memory returns ID<br>usage:<br>{<br>    &quot;thoughts&quot;: [<br>        &quot;I need to memorize...&quot;,<br>    ],<br>    &quot;tool_name&quot;: &quot;memory_save&quot;,<br>    &quot;tool_args&quot;: {<br>        &quot;text&quot;: &quot;# To compress...&quot;,<br>    }<br>}<br><br><br>### memory_delete:<br>delete memories by IDs comma separated<br>IDs from load save ops<br>usage:<br>{<br>    &quot;thoughts&quot;: [<br>        &quot;I need to delete...&quot;,<br>    ],<br>    &quot;tool_name&quot;: &quot;memory_delete&quot;,<br>    &quot;tool_args&quot;: {<br>        &quot;ids&quot;: &quot;32cd37ffd1-101f-4112-80e2-33b795548116, d1306e36-6a9c- ...&quot;,<br>    }<br>}<br><br><br>### memory_forget:<br>remove memories by query threshold filter like memory_load<br>default threshold 0.75 prevent accidents<br>verify with load after delete leftovers by IDs<br>usage:<br>{<br>    &quot;thoughts&quot;: [<br>        &quot;Let&#x27;s remove all memories about cars&quot;,<br>    ],<br>    &quot;tool_name&quot;: &quot;memory_forget&quot;,<br>    &quot;tool_args&quot;: {<br>        &quot;query&quot;: &quot;cars&quot;,<br>        &quot;threshold&quot;: 0.75,<br>        &quot;filter&quot;: &quot;timestamp.startswith(&#x27;2022-01-01&#x27;)&quot;,<br>    }<br>}<br><br><br>### code_execution_tool<br><br>execute terminal commands python nodejs code for computation or software tasks<br>place code in &quot;code&quot; arg; escape carefully and indent properly<br>select &quot;runtime&quot; arg: &quot;terminal&quot; &quot;python&quot; &quot;nodejs&quot; &quot;output&quot; &quot;reset&quot;<br>select &quot;session&quot; number, 0 default, others for multitasking<br>if code runs long, use &quot;output&quot; to wait, &quot;reset&quot; to kill process<br>use &quot;pip&quot; &quot;npm&quot; &quot;apt-get&quot; in &quot;terminal&quot; to install packages<br>to output, use print() or console.log()<br>if tool outputs error, adjust code before retrying; knowledge_tool can help<br>important: check code for placeholders or demo data; replace with real variables; don&#x27;t reuse snippets<br>don&#x27;t use with other tools except thoughts; wait for response before using others<br>check dependencies before running code<br>output may end with [SYSTEM: ...] information comming from framework, not terminal<br>usage:<br><br>1 execute python code<br><br>{<br>    &quot;thoughts&quot;: [<br>        &quot;Need to do...&quot;,<br>        &quot;I can use...&quot;,<br>        &quot;Then I can...&quot;,<br>    ],<br>    &quot;tool_name&quot;: &quot;code_execution_tool&quot;,<br>    &quot;tool_args&quot;: {<br>        &quot;runtime&quot;: &quot;python&quot;,<br>        &quot;session&quot;: 0,<br>        &quot;code&quot;: &quot;import os\nprint(os.getcwd())&quot;,<br>    }<br>}<br><br><br>2 execute terminal command<br>{<br>    &quot;thoughts&quot;: [<br>        &quot;Need to do...&quot;,<br>        &quot;Need to install...&quot;,<br>    ],<br>    &quot;tool_name&quot;: &quot;code_execution_tool&quot;,<br>    &quot;tool_args&quot;: {<br>        &quot;runtime&quot;: &quot;terminal&quot;,<br>        &quot;session&quot;: 0,<br>        &quot;code&quot;: &quot;apt-get install zip&quot;,<br>    }<br>}<br><br><br>2.1 wait for output with long-running scripts<br>{<br>    &quot;thoughts&quot;: [<br>        &quot;Waiting for program to finish...&quot;,<br>    ],<br>    &quot;tool_name&quot;: &quot;code_execution_tool&quot;,<br>    &quot;tool_args&quot;: {<br>        &quot;runtime&quot;: &quot;output&quot;,<br>        &quot;session&quot;: 0,<br>    }<br>}<br><br><br>2.2 reset terminal<br>{<br>    &quot;thoughts&quot;: [<br>        &quot;code_execution_tool not responding...&quot;,<br>    ],<br>    &quot;tool_name&quot;: &quot;code_execution_tool&quot;,<br>    &quot;tool_args&quot;: {<br>        &quot;runtime&quot;: &quot;reset&quot;,<br>        &quot;session&quot;: 0,<br>    }<br>}<br><br><br>### input:<br>use keyboard arg for terminal program input<br>use session arg for terminal session number<br>answer dialogues enter passwords etc<br>not for browser<br>usage:<br>{<br>    &quot;thoughts&quot;: [<br>        &quot;The program asks for Y/N...&quot;,<br>    ],<br>    &quot;tool_name&quot;: &quot;input&quot;,<br>    &quot;tool_args&quot;: {<br>        &quot;keyboard&quot;: &quot;Y&quot;,<br>        &quot;session&quot;: 0<br>    }<br>}<br><br><br>### browser_agent:<br><br>subordinate agent controls playwright browser<br>message argument talks to agent give clear instructions credentials task based<br>reset argument spawns new agent<br>do not reset if iterating<br>be precise descriptive like: open google login and end task, log in using ... and end task<br>when following up start: considering open pages<br>dont use phrase wait for instructions use end task<br>downloads default in /a0/tmp/downloads<br><br>usage:<br>{<br>  &quot;thoughts&quot;: [&quot;I need to log in to...&quot;],<br>  &quot;tool_name&quot;: &quot;browser_agent&quot;,<br>  &quot;tool_args&quot;: {<br>    &quot;message&quot;: &quot;Open and log me into...&quot;,<br>    &quot;reset&quot;: &quot;true&quot;<br>  }<br>}<br><br><br>{<br>  &quot;thoughts&quot;: [&quot;I need to log in to...&quot;],<br>  &quot;tool_name&quot;: &quot;browser_agent&quot;,<br>  &quot;tool_args&quot;: {<br>    &quot;message&quot;: &quot;Considering open pages, click...&quot;,<br>    &quot;reset&quot;: &quot;false&quot;<br>  }<br>}<br><br><br><br>## Task Scheduler Subsystem:<br>The task scheduler is a part of agent-zero enabling the system to execute<br>arbitrary tasks defined by a &quot;system prompt&quot; and &quot;user prompt&quot;.<br><br>When the task is executed the prompts are being run in the background in a context<br>conversation with the goal of completing the task described in the prompts.<br><br>Dedicated context means the task will run in it&#x27;s own chat. If task is created without the<br>dedicated_context flag then the task will run in the chat it was created in including entire history.<br><br>There are manual and automatically executed tasks.<br>Automatic execution happens by a schedule defined when creating the task.<br><br>Tasks are run asynchronously. If you need to wait for a running task&#x27;s completion or need the result of the last task run, use the scheduler:wait_for_task tool. It will wait for the task completion in case the task is currently running and will provide the result of the last execution.<br><br>### Important instructions<br>When a task is scheduled or planned, do not manually run it, if you have no more tasks, respond to user.<br>Be careful not to create recursive prompt, do not send a message that would make the agent schedule more tasks, no need to mention the interval in message, just the objective.<br>!!! When the user asks you to execute a task, first check if the task already exists and do not create a new task for execution. Execute the existing task instead. If the task in question does not exist ask the user what action to take. Never create tasks if asked to execute a task.<br><br>### Types of scheduler tasks<br>There are 3 types of scheduler tasks:<br><br>#### Scheduled - type=&quot;scheduled&quot;<br>This type of task is run by a recurring schedule defined in the crontab syntax with 5 fields (ex. */5 * * * * means every 5 minutes).<br>It is recurring and started automatically when the crontab syntax requires next execution..<br><br>#### Planned - type=&quot;planned&quot;<br>This type of task is run by a linear schedule defined as discrete datetimes of the upcoming executions.<br>It is  started automatically when a scheduled time elapses.<br><br>#### AdHoc - type=&quot;adhoc&quot;<br>This type of task is run manually and does not follow any schedule. It can be run explicitly by &quot;scheduler:run_task&quot; agent tool or by the user in the UI.<br><br>### Tools to manage the task scheduler system and it&#x27;s tasks<br><br>#### scheduler:list_tasks<br>List all tasks present in the system with their &#x27;uuid&#x27;, &#x27;name&#x27;, &#x27;type&#x27;, &#x27;state&#x27;, &#x27;schedule&#x27; and &#x27;next_run&#x27;.<br>All runnable tasks can be listed and filtered here. The arguments are filter fields.<br><br>##### Arguments:<br>* state: list(str) (Optional) - The state filter, one of &quot;idle&quot;, &quot;running&quot;, &quot;disabled&quot;, &quot;error&quot;. To only show tasks in given state.<br>* type: list(str) (Optional) - The task type filter, one of &quot;adhoc&quot;, &quot;planned&quot;, &quot;scheduled&quot;<br>* next_run_within: int (Optional) - The next run of the task must be within this many minutes<br>* next_run_after: int (Optional) - The next run of the task must be after not less than this many minutes<br><br>##### Usage:<br>{<br>    &quot;thoughts&quot;: [<br>        &quot;I must look for planned runnable tasks with name ... and state idle or error&quot;,<br>        &quot;The tasks should run within next 20 minutes&quot;<br>    ],<br>    &quot;tool_name&quot;: &quot;scheduler:list_tasks&quot;,<br>    &quot;tool_args&quot;: {<br>        &quot;state&quot;: [&quot;idle&quot;, &quot;error&quot;],<br>        &quot;type&quot;: [&quot;planned&quot;],<br>        &quot;next_run_within&quot;: 20<br>    }<br>}<br><br><br><br>#### scheduler:find_task_by_name<br>List all tasks whose name is matching partially or fully the provided name parameter.<br><br>##### Arguments:<br>* name: str - The task name to look for<br><br>##### Usage:<br>{<br>    &quot;thoughts&quot;: [<br>        &quot;I must look for tasks with name XYZ&quot;<br>    ],<br>    &quot;tool_name&quot;: &quot;scheduler:find_task_by_name&quot;,<br>    &quot;tool_args&quot;: {<br>        &quot;name&quot;: &quot;XYZ&quot;<br>    }<br>}<br><br><br><br>#### scheduler:show_task<br>Show task details for scheduler task with the given uuid.<br><br>##### Arguments:<br>* uuid: string - The uuid of the task to display<br><br>##### Usage (execute task with uuid &quot;xyz-123&quot;):<br>{<br>    &quot;thoughts&quot;: [<br>        &quot;I need details of task xxx-yyy-zzz&quot;,<br>    ],<br>    &quot;tool_name&quot;: &quot;scheduler:show_task&quot;,<br>    &quot;tool_args&quot;: {<br>        &quot;uuid&quot;: &quot;xxx-yyy-zzz&quot;,<br>    }<br>}<br><br><br><br>#### scheduler:run_task<br>Execute a task manually which is not in &quot;running&quot; state<br>This can be used to trigger tasks manually.<br>Normally you should only &quot;run&quot; tasks manually if they are in the &quot;idle&quot; state.<br>It is also advised to only run &quot;adhoc&quot; tasks manually but every task type can be triggered by this tool.<br>You can pass input data in text form as the &quot;context&quot; argument. The context will then be prepended to the task prompt when executed. This way you can pass for example result of one task as the input of another task or provide additional information specific to this one task run.<br><br>##### Arguments:<br>* uuid: string - The uuid of the task to run. Can be retrieved for example from &quot;scheduler:tasks_list&quot;<br>* context: (Optional) string - The context that will be prepended to the actual task prompt as contextual information.<br><br>##### Usage (execute task with uuid &quot;xyz-123&quot;):<br>{<br>    &quot;thoughts&quot;: [<br>        &quot;I must run task xyz-123&quot;,<br>    ],<br>    &quot;tool_name&quot;: &quot;scheduler:run_task&quot;,<br>    &quot;tool_args&quot;: {<br>        &quot;uuid&quot;: &quot;xyz-123&quot;,<br>        &quot;context&quot;: &quot;This text is useful to execute the task more precisely&quot;<br>    }<br>}<br><br><br><br>#### scheduler:delete_task<br>Delete the task defined by the given uuid from the system.<br><br>##### Arguments:<br>* uuid: string - The uuid of the task to run. Can be retrieved for example from &quot;scheduler:tasks_list&quot;<br><br>##### Usage (execute task with uuid &quot;xyz-123&quot;):<br>{<br>    &quot;thoughts&quot;: [<br>        &quot;I must delete task xyz-123&quot;,<br>    ],<br>    &quot;tool_name&quot;: &quot;scheduler:delete_task&quot;,<br>    &quot;tool_args&quot;: {<br>        &quot;uuid&quot;: &quot;xyz-123&quot;,<br>    }<br>}<br><br><br><br>#### scheduler:create_scheduled_task<br>Create a task within the scheduler system with the type &quot;scheduled&quot;.<br>The scheduled type of tasks is being run by a cron schedule that you must provide.<br><br>##### Arguments:<br>* name: str - The name of the task, will also be displayed when listing tasks<br>* system_prompt: str - The system prompt to be used when executing the task<br>* prompt: str - The actual prompt with the task definition<br>* schedule: dict[str,str] - the dict of all cron schedule values. The keys are descriptive: minute, hour, day, month, weekday. The values are cron syntax fields named by the keys.<br>* attachments: list[str] - Here you can add message attachments, valid are filesystem paths and internet urls<br>* dedicated_context: bool - if false, then the task will run in the context it was created in. If true, the task will have it&#x27;s own context. If unspecified then false is assumed. The tasks run in the context they were created in by default.<br><br>##### Usage:<br>{<br>    &quot;thoughts&quot;: [<br>        &quot;I must create new scheduled task with name XXX running every 20 minutes in a separate chat&quot;<br>    ],<br>    &quot;tool_name&quot;: &quot;scheduler:create_scheduled_task&quot;,<br>    &quot;tool_args&quot;: {<br>        &quot;name&quot;: &quot;XXX&quot;,<br>        &quot;system_prompt&quot;: &quot;You are a software developer&quot;,<br>        &quot;prompt&quot;: &quot;Send the user an email with a greeting using python and smtp. The user&#x27;s address is: <EMAIL>&quot;,<br>        &quot;attachments&quot;: [],<br>        &quot;schedule&quot;: {<br>            &quot;minute&quot;: &quot;*/20&quot;,<br>            &quot;hour&quot;: &quot;*&quot;,<br>            &quot;day&quot;: &quot;*&quot;,<br>            &quot;month&quot;: &quot;*&quot;,<br>            &quot;weekday&quot;: &quot;*&quot;,<br>        },<br>        &quot;dedicated_context&quot;: true<br>    }<br>}<br><br><br><br>#### scheduler:create_adhoc_task<br>Create a task within the scheduler system with the type &quot;adhoc&quot;.<br>The adhoc type of tasks is being run manually by &quot;scheduler:run_task&quot; tool or by the user via ui.<br><br>##### Arguments:<br>* name: str - The name of the task, will also be displayed when listing tasks<br>* system_prompt: str - The system prompt to be used when executing the task<br>* prompt: str - The actual prompt with the task definition<br>* attachments: list[str] - Here you can add message attachments, valid are filesystem paths and internet urls<br>* dedicated_context: bool - if false, then the task will run in the context it was created in. If true, the task will have it&#x27;s own context. If unspecified then false is assumed. The tasks run in the context they were created in by default.<br><br>##### Usage:<br>{<br>    &quot;thoughts&quot;: [<br>        &quot;I must create new scheduled task with name XXX running every 20 minutes&quot;<br>    ],<br>    &quot;tool_name&quot;: &quot;scheduler:create_adhoc_task&quot;,<br>    &quot;tool_args&quot;: {<br>        &quot;name&quot;: &quot;XXX&quot;,<br>        &quot;system_prompt&quot;: &quot;You are a software developer&quot;,<br>        &quot;prompt&quot;: &quot;Send the user an email with a greeting using python and smtp. The user&#x27;s address is: <EMAIL>&quot;,<br>        &quot;attachments&quot;: [],<br>        &quot;dedicated_context&quot;: false<br>    }<br>}<br><br><br><br>#### scheduler:create_planned_task<br>Create a task within the scheduler system with the type &quot;planned&quot;.<br>The planned type of tasks is being run by a fixed plan, a list of datetimes that you must provide.<br><br>##### Arguments:<br>* name: str - The name of the task, will also be displayed when listing tasks<br>* system_prompt: str - The system prompt to be used when executing the task<br>* prompt: str - The actual prompt with the task definition<br>* plan: list(iso datetime string) - the list of all execution timestamps. The dates should be in the 24 hour (!) strftime iso format: &quot;%Y-%m-%dT%H:%M:%S&quot;<br>* attachments: list[str] - Here you can add message attachments, valid are filesystem paths and internet urls<br>* dedicated_context: bool - if false, then the task will run in the context it was created in. If true, the task will have it&#x27;s own context. If unspecified then false is assumed. The tasks run in the context they were created in by default.<br><br>##### Usage:<br>{<br>    &quot;thoughts&quot;: [<br>        &quot;I must create new planned task to run tomorow at 6:25 PM&quot;,<br>        &quot;Today is 2025-04-29 according to system prompt&quot;<br>    ],<br>    &quot;tool_name&quot;: &quot;scheduler:create_planned_task&quot;,<br>    &quot;tool_args&quot;: {<br>        &quot;name&quot;: &quot;XXX&quot;,<br>        &quot;system_prompt&quot;: &quot;You are a software developer&quot;,<br>        &quot;prompt&quot;: &quot;Send the user an email with a greeting using python and smtp. The user&#x27;s address is: <EMAIL>&quot;,<br>        &quot;attachments&quot;: [],<br>        &quot;plan&quot;: [&quot;2025-04-29T18:25:00&quot;],<br>        &quot;dedicated_context&quot;: false<br>    }<br>}<br><br><br><br>#### scheduler:wait_for_task<br>Wait for the completion of a scheduler task identified by the uuid argument and return the result of last execution of the task.<br>Attention: You can only wait for tasks running in a different chat context (dedicated). Tasks with dedicated_context=False can not be waited for.<br><br>##### Arguments:<br>* uuid: string - The uuid of the task to wait for. Can be retrieved for example from &quot;scheduler:tasks_list&quot;<br><br>##### Usage (wait for task with uuid &quot;xyz-123&quot;):<br>{<br>    &quot;thoughts&quot;: [<br>        &quot;I need the most current result of the task xyz-123&quot;,<br>    ],<br>    &quot;tool_name&quot;: &quot;scheduler:wait_for_task&quot;,<br>    &quot;tool_args&quot;: {<br>        &quot;uuid&quot;: &quot;xyz-123&quot;,<br>    }<br>}<br><br><br><br>### document_query:<br>This tool can be used to read or analyze remote and local documents.<br>It can be used to:<br> *  Get webpage or remote document text content<br> *  Get local document text content<br> *  Answer queries about a webpage, remote or local document<br>By default, when the &quot;queries&quot; argument is empty, this tool returns the text content of the document retrieved using OCR.<br>Additionally, you can pass a list of &quot;queries&quot; - in this case, the tool returns the answers to all the passed queries about the document.<br>!!! This is a universal document reader qnd query tool<br>!!! Supported document formats: HTML, PDF, Office Documents (word,excel, powerpoint), Textfiles and many more.<br><br>#### Arguments:<br> *  &quot;document&quot; (string) : The web address or local path to the document in question. Webdocuments need &quot;http://&quot; or &quot;https://&quot; protocol prefix. For local files the &quot;file:&quot; protocol prefix is optional. Local files MUST be passed with full filesystem path.<br> *  &quot;queries&quot; (Optional, list[str]) : Optionally, here you can pass one or more queries to be answered (using and/or about) the document<br><br>#### Usage example 1:<br>##### Request:<br>{<br>    &quot;thoughts&quot;: [<br>        &quot;...&quot;,<br>    ],<br>    &quot;tool_name&quot;: &quot;document_query&quot;,<br>    &quot;tool_args&quot;: {<br>        &quot;document&quot;: &quot;https://...somexample&quot;,<br>    }<br>}<br><br>##### Response:<br>... Here is the entire content of the web document requested ...<br><br><br>#### Usage example 2:<br>##### Request:<br>{<br>    &quot;thoughts&quot;: [<br>        &quot;...&quot;,<br>    ],<br>    &quot;tool_name&quot;: &quot;document_query&quot;,<br>    &quot;tool_args&quot;: {<br>        &quot;document&quot;: &quot;https://...somexample&quot;,<br>        &quot;queries&quot;: [<br>            &quot;What is the topic?&quot;,<br>            &quot;Who is the audience?&quot;<br>        ]<br>    }<br>}<br><br>##### Response:<br># What is the topic?<br>... Description of the document topic ...<br><br># Who is the audience?<br>... The intended document audience list with short descriptions ...</span><br>
<br><span style="color: rgb(128, 128, 128); ">Debug: Loading prompt variables from C:\Users\<USER>\Downloads\noryonv3\agent-zero-main\prompts/default\agent.system.tool.call_sub.py</span><br>
<br><span style="font-weight: bold; color: rgb(0, 128, 0); background-color: rgb(255, 255, 255);">Agent 0: Generating</span><br>
<span style="font-style: italic; color: rgb(179, 255, 217); ">&lt;think&gt;</span><span style="font-style: italic; color: rgb(179, 255, 217); "><br></span><span style="font-style: italic; color: rgb(179, 255, 217); ">Okay</span><span style="font-style: italic; color: rgb(179, 255, 217); ">,</span><span style="font-style: italic; color: rgb(179, 255, 217); "> so</span><span style="font-style: italic; color: rgb(179, 255, 217); "> I</span><span style="font-style: italic; color: rgb(179, 255, 217); ">&#x27;m</span><span style="font-style: italic; color: rgb(179, 255, 217); "> trying</span><span style="font-style: italic; color: rgb(179, 255, 217); "> to</span><span style="font-style: italic; color: rgb(179, 255, 217); "> figure</span><span style="font-style: italic; color: rgb(179, 255, 217); "> out</span><span style="font-style: italic; color: rgb(179, 255, 217); "> how</span><span style="font-style: italic; color: rgb(179, 255, 217); "> to</span><span style="font-style: italic; color: rgb(179, 255, 217); "> set</span><span style="font-style: italic; color: rgb(179, 255, 217); "> up</span><span style="font-style: italic; color: rgb(179, 255, 217); "> Agent</span><span style="font-style: italic; color: rgb(179, 255, 217); "> Zero</span><span style="font-style: italic; color: rgb(179, 255, 217); "> on</span><span style="font-style: italic; color: rgb(179, 255, 217); "> my</span><span style="font-style: italic; color: rgb(179, 255, 217); "> local</span><span style="font-style: italic; color: rgb(179, 255, 217); "> machine</span><span style="font-style: italic; color: rgb(179, 255, 217); ">.</span><span style="font-style: italic; color: rgb(179, 255, 217); "> I</span><span style="font-style: italic; color: rgb(179, 255, 217); ">&#x27;ve</span><span style="font-style: italic; color: rgb(179, 255, 217); "> heard</span><span style="font-style: italic; color: rgb(179, 255, 217); "> a</span><span style="font-style: italic; color: rgb(179, 255, 217); "> lot</span><span style="font-style: italic; color: rgb(179, 255, 217); "> about</span><span style="font-style: italic; color: rgb(179, 255, 217); "> it</span><span style="font-style: italic; color: rgb(179, 255, 217); "> and</span><span style="font-style: italic; color: rgb(179, 255, 217); "> want</span><span style="font-style: italic; color: rgb(179, 255, 217); "> to</span><span style="font-style: italic; color: rgb(179, 255, 217); "> get</span><span style="font-style: italic; color: rgb(179, 255, 217); "> started</span><span style="font-style: italic; color: rgb(179, 255, 217); ">,</span><span style="font-style: italic; color: rgb(179, 255, 217); "> but</span><span style="font-style: italic; color: rgb(179, 255, 217); "> the</span><span style="font-style: italic; color: rgb(179, 255, 217); "> documentation</span><span style="font-style: italic; color: rgb(179, 255, 217); "> is</span><span style="font-style: italic; color: rgb(179, 255, 217); "> a</span><span style="font-style: italic; color: rgb(179, 255, 217); "> bit</span><span style="font-style: italic; color: rgb(179, 255, 217); "> overwhelming</span><span style="font-style: italic; color: rgb(179, 255, 217); ">.</span><span style="font-style: italic; color: rgb(179, 255, 217); "> Let</span><span style="font-style: italic; color: rgb(179, 255, 217); "> me</span><span style="font-style: italic; color: rgb(179, 255, 217); "> break</span><span style="font-style: italic; color: rgb(179, 255, 217); "> it</span><span style="font-style: italic; color: rgb(179, 255, 217); "> down</span><span style="font-style: italic; color: rgb(179, 255, 217); "> step</span><span style="font-style: italic; color: rgb(179, 255, 217); "> by</span><span style="font-style: italic; color: rgb(179, 255, 217); "> step</span><span style="font-style: italic; color: rgb(179, 255, 217); ">.<br><br></span><span style="font-style: italic; color: rgb(179, 255, 217); ">First</span><span style="font-style: italic; color: rgb(179, 255, 217); ">,</span><span style="font-style: italic; color: rgb(179, 255, 217); "> from</span><span style="font-style: italic; color: rgb(179, 255, 217); "> what</span><span style="font-style: italic; color: rgb(179, 255, 217); "> I</span><span style="font-style: italic; color: rgb(179, 255, 217); "> understand</span><span style="font-style: italic; color: rgb(179, 255, 217); ">,</span><span style="font-style: italic; color: rgb(179, 255, 217); "> there</span><span style="font-style: italic; color: rgb(179, 255, 217); "> are</span><span style="font-style: italic; color: rgb(179, 255, 217); "> two</span><span style="font-style: italic; color: rgb(179, 255, 217); "> main</span><span style="font-style: italic; color: rgb(179, 255, 217); "> ways</span><span style="font-style: italic; color: rgb(179, 255, 217); "> to</span><span style="font-style: italic; color: rgb(179, 255, 217); "> run</span><span style="font-style: italic; color: rgb(179, 255, 217); "> Agent</span><span style="font-style: italic; color: rgb(179, 255, 217); "> Zero</span><span style="font-style: italic; color: rgb(179, 255, 217); ">:</span><span style="font-style: italic; color: rgb(179, 255, 217); "> using</span><span style="font-style: italic; color: rgb(179, 255, 217); "> Docker</span><span style="font-style: italic; color: rgb(179, 255, 217); "> or</span><span style="font-style: italic; color: rgb(179, 255, 217); "> installing</span><span style="font-style: italic; color: rgb(179, 255, 217); "> the</span><span style="font-style: italic; color: rgb(179, 255, 217); "> full</span><span style="font-style: italic; color: rgb(179, 255, 217); "> binaries</span><span style="font-style: italic; color: rgb(179, 255, 217); ">.</span><span style="font-style: italic; color: rgb(179, 255, 217); "> Since</span><span style="font-style: italic; color: rgb(179, 255, 217); "> I</span><span style="font-style: italic; color: rgb(179, 255, 217); ">&#x27;m</span><span style="font-style: italic; color: rgb(179, 255, 217); "> more</span><span style="font-style: italic; color: rgb(179, 255, 217); "> comfortable</span><span style="font-style: italic; color: rgb(179, 255, 217); "> with</span><span style="font-style: italic; color: rgb(179, 255, 217); "> Docker</span><span style="font-style: italic; color: rgb(179, 255, 217); "> and</span><span style="font-style: italic; color: rgb(179, 255, 217); "> it</span><span style="font-style: italic; color: rgb(179, 255, 217); "> seems</span><span style="font-style: italic; color: rgb(179, 255, 217); "> like</span><span style="font-style: italic; color: rgb(179, 255, 217); "> a</span><span style="font-style: italic; color: rgb(179, 255, 217); "> good</span><span style="font-style: italic; color: rgb(179, 255, 217); "> way</span><span style="font-style: italic; color: rgb(179, 255, 217); "> to</span><span style="font-style: italic; color: rgb(179, 255, 217); "> keep</span><span style="font-style: italic; color: rgb(179, 255, 217); "> everything</span><span style="font-style: italic; color: rgb(179, 255, 217); "> isolated</span><span style="font-style: italic; color: rgb(179, 255, 217); "> and</span><span style="font-style: italic; color: rgb(179, 255, 217); "> avoid</span><span style="font-style: italic; color: rgb(179, 255, 217); "> messing</span><span style="font-style: italic; color: rgb(179, 255, 217); "> with</span><span style="font-style: italic; color: rgb(179, 255, 217); "> system</span><span style="font-style: italic; color: rgb(179, 255, 217); "> files</span><span style="font-style: italic; color: rgb(179, 255, 217); ">,</span><span style="font-style: italic; color: rgb(179, 255, 217); "> I</span><span style="font-style: italic; color: rgb(179, 255, 217); ">&#x27;ll</span><span style="font-style: italic; color: rgb(179, 255, 217); "> go</span><span style="font-style: italic; color: rgb(179, 255, 217); "> with</span><span style="font-style: italic; color: rgb(179, 255, 217); "> that</span><span style="font-style: italic; color: rgb(179, 255, 217); "> option</span><span style="font-style: italic; color: rgb(179, 255, 217); ">.<br><br></span><span style="font-style: italic; color: rgb(179, 255, 217); ">The</span><span style="font-style: italic; color: rgb(179, 255, 217); "> user</span><span style="font-style: italic; color: rgb(179, 255, 217); "> mentioned</span><span style="font-style: italic; color: rgb(179, 255, 217); "> pulling</span><span style="font-style: italic; color: rgb(179, 255, 217); "> the</span><span style="font-style: italic; color: rgb(179, 255, 217); "> Docker</span><span style="font-style: italic; color: rgb(179, 255, 217); "> image</span><span style="font-style: italic; color: rgb(179, 255, 217); "> `</span><span style="font-style: italic; color: rgb(179, 255, 217); ">fr</span><span style="font-style: italic; color: rgb(179, 255, 217); ">del</span><span style="font-style: italic; color: rgb(179, 255, 217); ">/</span><span style="font-style: italic; color: rgb(179, 255, 217); ">agent</span><span style="font-style: italic; color: rgb(179, 255, 217); ">-zero</span><span style="font-style: italic; color: rgb(179, 255, 217); ">-run</span><span style="font-style: italic; color: rgb(179, 255, 217); ">`.</span><span style="font-style: italic; color: rgb(179, 255, 217); "> So</span><span style="font-style: italic; color: rgb(179, 255, 217); ">,</span><span style="font-style: italic; color: rgb(179, 255, 217); "> I</span><span style="font-style: italic; color: rgb(179, 255, 217); "> need</span><span style="font-style: italic; color: rgb(179, 255, 217); "> to</span><span style="font-style: italic; color: rgb(179, 255, 217); "> open</span><span style="font-style: italic; color: rgb(179, 255, 217); "> my</span><span style="font-style: italic; color: rgb(179, 255, 217); "> terminal</span><span style="font-style: italic; color: rgb(179, 255, 217); "> and</span><span style="font-style: italic; color: rgb(179, 255, 217); "> type</span><span style="font-style: italic; color: rgb(179, 255, 217); ">:<br><br></span><span style="font-style: italic; color: rgb(179, 255, 217); ">```</span><span style="font-style: italic; color: rgb(179, 255, 217); ">bash</span><span style="font-style: italic; color: rgb(179, 255, 217); "><br></span><span style="font-style: italic; color: rgb(179, 255, 217); ">docker</span><span style="font-style: italic; color: rgb(179, 255, 217); "> pull</span><span style="font-style: italic; color: rgb(179, 255, 217); "> fr</span><span style="font-style: italic; color: rgb(179, 255, 217); ">del</span><span style="font-style: italic; color: rgb(179, 255, 217); ">/</span><span style="font-style: italic; color: rgb(179, 255, 217); ">agent</span><span style="font-style: italic; color: rgb(179, 255, 217); ">-zero</span><span style="font-style: italic; color: rgb(179, 255, 217); ">-run</span><span style="font-style: italic; color: rgb(179, 255, 217); "><br></span><span style="font-style: italic; color: rgb(179, 255, 217); ">``</span><span style="font-style: italic; color: rgb(179, 255, 217); ">`<br><br></span><span style="font-style: italic; color: rgb(179, 255, 217); ">I</span><span style="font-style: italic; color: rgb(179, 255, 217); "> hope</span><span style="font-style: italic; color: rgb(179, 255, 217); "> that</span><span style="font-style: italic; color: rgb(179, 255, 217); "> image</span><span style="font-style: italic; color: rgb(179, 255, 217); "> is</span><span style="font-style: italic; color: rgb(179, 255, 217); "> available</span><span style="font-style: italic; color: rgb(179, 255, 217); "> on</span><span style="font-style: italic; color: rgb(179, 255, 217); "> Docker</span><span style="font-style: italic; color: rgb(179, 255, 217); "> Hub</span><span style="font-style: italic; color: rgb(179, 255, 217); ">.</span><span style="font-style: italic; color: rgb(179, 255, 217); "> Once</span><span style="font-style: italic; color: rgb(179, 255, 217); "> it</span><span style="font-style: italic; color: rgb(179, 255, 217); ">&#x27;s</span><span style="font-style: italic; color: rgb(179, 255, 217); "> pulled</span><span style="font-style: italic; color: rgb(179, 255, 217); ">,</span><span style="font-style: italic; color: rgb(179, 255, 217); "> the</span><span style="font-style: italic; color: rgb(179, 255, 217); "> next</span><span style="font-style: italic; color: rgb(179, 255, 217); "> step</span><span style="font-style: italic; color: rgb(179, 255, 217); "> is</span><span style="font-style: italic; color: rgb(179, 255, 217); "> to</span><span style="font-style: italic; color: rgb(179, 255, 217); "> run</span><span style="font-style: italic; color: rgb(179, 255, 217); "> it</span><span style="font-style: italic; color: rgb(179, 255, 217); "> with</span><span style="font-style: italic; color: rgb(179, 255, 217); "> specific</span><span style="font-style: italic; color: rgb(179, 255, 217); "> port</span><span style="font-style: italic; color: rgb(179, 255, 217); "> mappings</span><span style="font-style: italic; color: rgb(179, 255, 217); ">.</span><span style="font-style: italic; color: rgb(179, 255, 217); "> The</span><span style="font-style: italic; color: rgb(179, 255, 217); "> instructions</span><span style="font-style: italic; color: rgb(179, 255, 217); "> say</span><span style="font-style: italic; color: rgb(179, 255, 217); "> to</span><span style="font-style: italic; color: rgb(179, 255, 217); "> map</span><span style="font-style: italic; color: rgb(179, 255, 217); "> port</span><span style="font-style: italic; color: rgb(179, 255, 217); "> </span><span style="font-style: italic; color: rgb(179, 255, 217); ">5</span><span style="font-style: italic; color: rgb(179, 255, 217); ">0</span><span style="font-style: italic; color: rgb(179, 255, 217); ">0</span><span style="font-style: italic; color: rgb(179, 255, 217); ">0</span><span style="font-style: italic; color: rgb(179, 255, 217); ">1</span><span style="font-style: italic; color: rgb(179, 255, 217); "> for</span><span style="font-style: italic; color: rgb(179, 255, 217); "> the</span><span style="font-style: italic; color: rgb(179, 255, 217); "> Web</span><span style="font-style: italic; color: rgb(179, 255, 217); "> UI</span><span style="font-style: italic; color: rgb(179, 255, 217); "> and</span><span style="font-style: italic; color: rgb(179, 255, 217); "> another</span><span style="font-style: italic; color: rgb(179, 255, 217); "> port</span><span style="font-style: italic; color: rgb(179, 255, 217); "> for</span><span style="font-style: italic; color: rgb(179, 255, 217); "> SSH</span><span style="font-style: italic; color: rgb(179, 255, 217); ">,</span><span style="font-style: italic; color: rgb(179, 255, 217); "> but</span><span style="font-style: italic; color: rgb(179, 255, 217); "> in</span><span style="font-style: italic; color: rgb(179, 255, 217); "> the</span><span style="font-style: italic; color: rgb(179, 255, 217); "> example</span><span style="font-style: italic; color: rgb(179, 255, 217); ">,</span><span style="font-style: italic; color: rgb(179, 255, 217); "> only</span><span style="font-style: italic; color: rgb(179, 255, 217); "> one</span><span style="font-style: italic; color: rgb(179, 255, 217); "> port</span><span style="font-style: italic; color: rgb(179, 255, 217); "> is</span><span style="font-style: italic; color: rgb(179, 255, 217); "> specified</span><span style="font-style: italic; color: rgb(179, 255, 217); ">.</span><span style="font-style: italic; color: rgb(179, 255, 217); "> Wait</span><span style="font-style: italic; color: rgb(179, 255, 217); ">,</span><span style="font-style: italic; color: rgb(179, 255, 217); "> looking</span><span style="font-style: italic; color: rgb(179, 255, 217); "> back</span><span style="font-style: italic; color: rgb(179, 255, 217); ">,</span><span style="font-style: italic; color: rgb(179, 255, 217); "> the</span><span style="font-style: italic; color: rgb(179, 255, 217); "> user</span><span style="font-style: italic; color: rgb(179, 255, 217); "> said</span><span style="font-style: italic; color: rgb(179, 255, 217); "> that</span><span style="font-style: italic; color: rgb(179, 255, 217); "> when</span><span style="font-style: italic; color: rgb(179, 255, 217); "> using</span><span style="font-style: italic; color: rgb(179, 255, 217); "> Docker</span><span style="font-style: italic; color: rgb(179, 255, 217); ">,</span><span style="font-style: italic; color: rgb(179, 255, 217); "> you</span><span style="font-style: italic; color: rgb(179, 255, 217); "> need</span><span style="font-style: italic; color: rgb(179, 255, 217); "> to</span><span style="font-style: italic; color: rgb(179, 255, 217); "> map</span><span style="font-style: italic; color: rgb(179, 255, 217); "> two</span><span style="font-style: italic; color: rgb(179, 255, 217); "> ports</span><span style="font-style: italic; color: rgb(179, 255, 217); ">:</span><span style="font-style: italic; color: rgb(179, 255, 217); "> </span><span style="font-style: italic; color: rgb(179, 255, 217); ">5</span><span style="font-style: italic; color: rgb(179, 255, 217); ">5</span><span style="font-style: italic; color: rgb(179, 255, 217); ">0</span><span style="font-style: italic; color: rgb(179, 255, 217); ">2</span><span style="font-style: italic; color: rgb(179, 255, 217); ">2</span><span style="font-style: italic; color: rgb(179, 255, 217); "> for</span><span style="font-style: italic; color: rgb(179, 255, 217); "> SSH</span><span style="font-style: italic; color: rgb(179, 255, 217); "> and</span><span style="font-style: italic; color: rgb(179, 255, 217); "> </span><span style="font-style: italic; color: rgb(179, 255, 217); ">0</span><span style="font-style: italic; color: rgb(179, 255, 217); "> for</span><span style="font-style: italic; color: rgb(179, 255, 217); "> automatic</span><span style="font-style: italic; color: rgb(179, 255, 217); "> assignment</span><span style="font-style: italic; color: rgb(179, 255, 217); "> of</span><span style="font-style: italic; color: rgb(179, 255, 217); "> the</span><span style="font-style: italic; color: rgb(179, 255, 217); "> Web</span><span style="font-style: italic; color: rgb(179, 255, 217); "> UI</span><span style="font-style: italic; color: rgb(179, 255, 217); "> port</span><span style="font-style: italic; color: rgb(179, 255, 217); ">.</span><span style="font-style: italic; color: rgb(179, 255, 217); "> Hmm</span><span style="font-style: italic; color: rgb(179, 255, 217); ">,</span><span style="font-style: italic; color: rgb(179, 255, 217); "> maybe</span><span style="font-style: italic; color: rgb(179, 255, 217); "> I</span><span style="font-style: italic; color: rgb(179, 255, 217); "> should</span><span style="font-style: italic; color: rgb(179, 255, 217); "> adjust</span><span style="font-style: italic; color: rgb(179, 255, 217); "> that</span><span style="font-style: italic; color: rgb(179, 255, 217); ">.<br><br></span><span style="font-style: italic; color: rgb(179, 255, 217); ">So</span><span style="font-style: italic; color: rgb(179, 255, 217); ">,</span><span style="font-style: italic; color: rgb(179, 255, 217); "> the</span><span style="font-style: italic; color: rgb(179, 255, 217); "> correct</span><span style="font-style: italic; color: rgb(179, 255, 217); "> command</span><span style="font-style: italic; color: rgb(179, 255, 217); "> would</span><span style="font-style: italic; color: rgb(179, 255, 217); "> be</span><span style="font-style: italic; color: rgb(179, 255, 217); ">:<br><br></span><span style="font-style: italic; color: rgb(179, 255, 217); ">```</span><span style="font-style: italic; color: rgb(179, 255, 217); ">bash</span><span style="font-style: italic; color: rgb(179, 255, 217); "><br></span><span style="font-style: italic; color: rgb(179, 255, 217); ">docker</span><span style="font-style: italic; color: rgb(179, 255, 217); "> run</span><span style="font-style: italic; color: rgb(179, 255, 217); "> -</span><span style="font-style: italic; color: rgb(179, 255, 217); ">p</span><span style="font-style: italic; color: rgb(179, 255, 217); "> </span><span style="font-style: italic; color: rgb(179, 255, 217); ">5</span><span style="font-style: italic; color: rgb(179, 255, 217); ">5</span><span style="font-style: italic; color: rgb(179, 255, 217); ">0</span><span style="font-style: italic; color: rgb(179, 255, 217); ">2</span><span style="font-style: italic; color: rgb(179, 255, 217); ">2</span><span style="font-style: italic; color: rgb(179, 255, 217); ">:</span><span style="font-style: italic; color: rgb(179, 255, 217); ">5</span><span style="font-style: italic; color: rgb(179, 255, 217); ">5</span><span style="font-style: italic; color: rgb(179, 255, 217); ">0</span><span style="font-style: italic; color: rgb(179, 255, 217); ">2</span><span style="font-style: italic; color: rgb(179, 255, 217); ">2</span><span style="font-style: italic; color: rgb(179, 255, 217); "> -</span><span style="font-style: italic; color: rgb(179, 255, 217); ">p</span><span style="font-style: italic; color: rgb(179, 255, 217); "> </span><span style="font-style: italic; color: rgb(179, 255, 217); ">5</span><span style="font-style: italic; color: rgb(179, 255, 217); ">0</span><span style="font-style: italic; color: rgb(179, 255, 217); ">0</span><span style="font-style: italic; color: rgb(179, 255, 217); ">0</span><span style="font-style: italic; color: rgb(179, 255, 217); ">1</span><span style="font-style: italic; color: rgb(179, 255, 217); ">:</span><span style="font-style: italic; color: rgb(179, 255, 217); ">8</span><span style="font-style: italic; color: rgb(179, 255, 217); ">0</span><span style="font-style: italic; color: rgb(179, 255, 217); "> fr</span><span style="font-style: italic; color: rgb(179, 255, 217); ">del</span><span style="font-style: italic; color: rgb(179, 255, 217); ">/</span><span style="font-style: italic; color: rgb(179, 255, 217); ">agent</span><span style="font-style: italic; color: rgb(179, 255, 217); ">-zero</span><span style="font-style: italic; color: rgb(179, 255, 217); ">-run</span><span style="font-style: italic; color: rgb(179, 255, 217); "><br></span><span style="font-style: italic; color: rgb(179, 255, 217); ">``</span><span style="font-style: italic; color: rgb(179, 255, 217); ">`<br><br></span><span style="font-style: italic; color: rgb(179, 255, 217); ">This</span><span style="font-style: italic; color: rgb(179, 255, 217); "> way</span><span style="font-style: italic; color: rgb(179, 255, 217); ">,</span><span style="font-style: italic; color: rgb(179, 255, 217); "> port</span><span style="font-style: italic; color: rgb(179, 255, 217); "> </span><span style="font-style: italic; color: rgb(179, 255, 217); ">5</span><span style="font-style: italic; color: rgb(179, 255, 217); ">5</span><span style="font-style: italic; color: rgb(179, 255, 217); ">0</span><span style="font-style: italic; color: rgb(179, 255, 217); ">2</span><span style="font-style: italic; color: rgb(179, 255, 217); ">2</span><span style="font-style: italic; color: rgb(179, 255, 217); "> is</span><span style="font-style: italic; color: rgb(179, 255, 217); "> mapped</span><span style="font-style: italic; color: rgb(179, 255, 217); "> for</span>