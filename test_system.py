#!/usr/bin/env python3
"""
Simple test script to use your unified AI system
"""

import requests
import json

def test_system():
    """Test the system with a simple request"""
    print("🧪 Testing your Unified AI System...")
    print("=" * 50)
    
    try:
        # Test basic chat
        url = "http://localhost:9001/chat"
        payload = {"message": "Hello! Are you working? Tell me what you can do."}
        
        print("📤 Sending request...")
        response = requests.post(url, json=payload, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ SUCCESS! System is working!")
            print(f"🤖 Response: {result.get('response', 'No response')}")
            print(f"🛠️ Capability used: {result.get('capability_used', 'unknown')}")
            print(f"⚡ Response time: {result.get('execution_time', 0):.1f}s")
            return True
        else:
            print(f"❌ Error: HTTP {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def interactive_chat():
    """Interactive chat with the AI system"""
    print("\n💬 INTERACTIVE CHAT MODE")
    print("=" * 50)
    print("Type your messages below. Type 'quit' to exit.")
    print("Examples:")
    print("  - 'Write a Python hello world function'")
    print("  - 'Search for Python tutorials'")
    print("  - 'Plan a software project'")
    print("  - 'Analyze code quality'")
    print()
    
    while True:
        try:
            message = input("You: ").strip()
            
            if message.lower() in ['quit', 'exit', 'bye']:
                print("👋 Goodbye!")
                break
            
            if not message:
                continue
            
            print("🤖 AI is thinking...")
            
            url = "http://localhost:9001/chat"
            payload = {"message": message}
            
            response = requests.post(url, json=payload, timeout=60)
            
            if response.status_code == 200:
                result = response.json()
                print(f"\n🤖 AI ({result.get('capability_used', 'unknown')}):")
                print(result.get('response', 'No response'))
                print(f"⚡ ({result.get('execution_time', 0):.1f}s)")
                print()
            else:
                print(f"❌ Error: HTTP {response.status_code}")
                print(f"Response: {response.text}")
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

def show_capabilities():
    """Show all available capabilities"""
    print("\n🛠️ AVAILABLE CAPABILITIES")
    print("=" * 50)
    
    try:
        response = requests.get("http://localhost:9001/", timeout=10)
        if response.status_code == 200:
            info = response.json()
            capabilities = info.get('capabilities', [])
            
            print(f"📊 Total capabilities: {len(capabilities)}")
            print()
            
            capability_descriptions = {
                'code_generation': '💻 Write, debug, and optimize code',
                'web_search': '🔍 Search and analyze web content',
                'voice_processing': '🎤 Speech-to-text and text-to-speech',
                'multimodal_processing': '🖼️ Analyze images and documents',
                'task_planning': '📋 Plan and organize complex tasks',
                'autonomous_execution': '🤖 Execute tasks automatically',
                'advanced_development': '🔧 Advanced code analysis (NEW)',
                'advanced_voice': '🎵 Voice cloning and synthesis (NEW)'
            }
            
            for i, cap in enumerate(capabilities, 1):
                desc = capability_descriptions.get(cap, '❓ Unknown capability')
                print(f"{i:2d}. {desc}")
                
        else:
            print("❌ Could not fetch capabilities")
            
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    """Main menu"""
    print("🚀 UNIFIED AI SYSTEM - USER INTERFACE")
    print("=" * 60)
    
    # First test if system is working
    if not test_system():
        print("\n❌ System is not working properly!")
        print("Make sure the unified_local_agent.py is running.")
        return
    
    while True:
        print("\n📋 WHAT WOULD YOU LIKE TO DO?")
        print("1. 💬 Interactive Chat")
        print("2. 🛠️ Show Capabilities") 
        print("3. 🧪 Test System")
        print("4. 🚪 Exit")
        
        choice = input("\nEnter your choice (1-4): ").strip()
        
        if choice == '1':
            interactive_chat()
        elif choice == '2':
            show_capabilities()
        elif choice == '3':
            test_system()
        elif choice == '4':
            print("👋 Goodbye!")
            break
        else:
            print("❌ Invalid choice. Please enter 1-4.")

if __name__ == "__main__":
    main()
