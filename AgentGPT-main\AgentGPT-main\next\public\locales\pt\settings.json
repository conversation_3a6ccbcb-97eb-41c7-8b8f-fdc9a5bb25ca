{"ADVANCED_SETTINGS": "Configurações avançadas", "API_KEY": "Chave de <PERSON>", "AUTOMATIC_MODE": "Modo automá<PERSON>", "AUTOMATIC_MODE_DESCRIPTION": "(Padrão): O agente executa automaticamente todas as tarefas.", "CONTROL_MAXIMUM_OF_TOKENS_DESCRIPTION": "Controla o número máximo de tokens usados em cada chamada de API (valores mais altos resultam em respostas mais detalhadas, mas também mais caras).", "CONTROL_THE_MAXIMUM_NUM_OF_LOOPS": "Controla o número máximo de loops executados pelo agente (valores mais altos resultam em mais chamadas de API).", "GET_YOUR_OWN_APIKEY": "Obtenha sua própria chave de API da OpenAI", "HERE": "aqui", "HERE_YOU_CAN_ADD_YOUR_OPENAI_API_KEY": "Aqui você pode adicionar sua chave de API da OpenAI. Isso significa que você terá que pagar pelo uso de seu próprio token da OpenAI, mas terá acesso mais amplo ao ChatGPT! Além disso, você pode escolher qualquer modelo oferecido pela OpenAI.", "HIGHER_VALUES_MAKE_OUTPUT_MORE_RANDOM": "Valores mais altos tornam a saída mais aleatória, enquanto valores mais baixos a tornam mais focada e definida.", "INFO_TO_USE_GPT4": "Para usar o modelo GPT-4, é necessário fornecer a chave de API. Você pode obtê-la", "INVALID_OPENAI_API_KEY": "Chave de API inválida!", "LABEL_MODE": "Modo", "LABEL_MODEL": "<PERSON><PERSON>", "LANG": "Idioma", "LINK": "Solicitar chave de API", "LOOP": "Laço", "MUST_CONNECT_CREDIT_CARD": "Nota: Você deve conectar um cartão de crédito à sua conta", "NOTE_API_KEY_USAGE": "Esta chave só será usada nesta sessão do navegador.", "NOTE_TO_GET_OPENAI_KEY": "OBSERVAÇÃO: Para obter uma chave de API, você precisa se registrar em uma conta da OpenAI, o que pode ser feito no seguinte link:", "PAUSE": "Pausar", "PAUSE_MODE": "<PERSON><PERSON> de pausa", "PAUSE_MODE_DESCRIPTION": "O agente pausa após cada conjunto de tarefa(s)", "PENAI_API_KEY": "Chave de API OpenAI inválida", "PLAY": "Reproduzir", "SETTINGS_DIALOG_HEADER": "Configurações ⚙", "SUBSCRIPTION_WILL_NOT_WORK": "(A assinatura do ChatGPT Plus não funcionará)", "TEMPERATURE": "Temperatura", "TOKENS": "Tokens"}