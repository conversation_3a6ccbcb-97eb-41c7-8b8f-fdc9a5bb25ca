<html><body style='background-color:black;font-family: Arial, Helvetica, sans-serif;'><pre>
<span style=" ">Initializing framework...</span><br>
<span style=" ">Starting server...</span><br>
<br><span style="color: rgb(128, 128, 128); ">Debug: Registered middleware for MCP and MCP token</span><br>
<br><span style="color: rgb(128, 128, 128); ">Debug: Starting server at localhost:50001...</span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: Failed to pause job loop by development instance: No RFC password, cannot handle RFC calls.</span><br>
<br><span style="color: rgb(128, 128, 128); ">Debug: Changing timezone from None to UTC</span><br>
<br><br><span style="color: rgb(128, 128, 128); ">Debug: Changing timezone from UTC to America/New_York</span><br>
<span style="color: rgb(128, 128, 128); ">Debug: Changing timezone from UTC to America/New_York</span><br>
<br><span style="font-weight: bold; color: rgb(255, 255, 255); background-color: rgb(108, 52, 131);">User message:</span><br>
<span style="color: rgb(255, 255, 255); ">&gt; hi</span><br>
<br><span style="color: rgb(128, 128, 128); ">Debug: Loading prompt variables from C:\Users\<USER>\Downloads\noryonv3\agent-zero-main\prompts/default\agent.system.tool.call_sub.py</span><br>
<br><span style="color: rgb(255, 0, 0); ">Traceback (most recent call last):<br>Traceback (most recent call last):<br>  File &quot;C:\Users\<USER>\Downloads\noryonv3\agent-zero-main\agent.py&quot;, line 334, in monologue<br>    prompt = await self.prepare_prompt(loop_data=self.loop_data)<br>             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^<br>  File &quot;C:\Users\<USER>\Downloads\noryonv3\agent-zero-main\agent.py&quot;, line 423, in prepare_prompt<br>    await self.call_extensions(&quot;message_loop_prompts_after&quot;, loop_data=loop_data)<br>  File &quot;C:\Users\<USER>\Downloads\noryonv3\agent-zero-main\agent.py&quot;, line 833, in call_extensions<br>    await cls(agent=self).execute(**kwargs)<br>  File &quot;C:\Users\<USER>\Downloads\noryonv3\agent-zero-main\python\extensions\message_loop_prompts_after\_91_recall_wait.py&quot;, line 13, in execute<br>    await task<br>  File &quot;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\futures.py&quot;, line 287, in __await__<br>    yield self  # This tells Task to wait for completion.<br>    ^^^^^^^^^^<br>  File &quot;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\tasks.py&quot;, line 385, in __wakeup<br>    future.result()<br>  File &quot;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\futures.py&quot;, line 203, in result<br>    raise self._exception.with_traceback(self._exception_tb)<br>  File &quot;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\tasks.py&quot;, line 314, in __step_run_and_handle_result<br>    result = coro.send(None)<br>             ^^^^^^^^^^^^^^^<br>  File &quot;C:\Users\<USER>\Downloads\noryonv3\agent-zero-main\python\extensions\message_loop_prompts_after\_50_recall_memories.py&quot;, line 60, in search_memories<br>    query = await self.agent.call_utility_model(<br>            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^<br>  File &quot;C:\Users\<USER>\Downloads\noryonv3\agent-zero-main\agent.py&quot;, line 633, in call_utility_model<br>    async for chunk in (prompt | model).astream({}):<br>  File &quot;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\langchain_core\runnables\base.py&quot;, line 3434, in astream<br>    async for chunk in self.atransform(input_aiter(), config, **kwargs):<br>  File &quot;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\langchain_core\runnables\base.py&quot;, line 3417, in atransform<br>    async for chunk in self._atransform_stream_with_config(<br>  File &quot;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\langchain_core\runnables\base.py&quot;, line 2305, in _atransform_stream_with_config<br>    chunk: Output = await asyncio.create_task(  # type: ignore[call-arg]<br>                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^<br>  File &quot;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\futures.py&quot;, line 287, in __await__<br>    yield self  # This tells Task to wait for completion.<br>    ^^^^^^^^^^<br>  File &quot;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\tasks.py&quot;, line 385, in __wakeup<br>    future.result()<br><br>&gt;&gt;&gt;  15 stack lines skipped &lt;&lt;&lt;<br><br>  File &quot;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\langchain_core\language_models\chat_models.py&quot;, line 512, in astream<br>    async for chunk in self._astream(<br>  File &quot;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\langchain_openai\chat_models\base.py&quot;, line 2316, in _astream<br>    async for chunk in super()._astream(*args, **kwargs):<br>  File &quot;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\langchain_openai\chat_models\base.py&quot;, line 1069, in _astream<br>    response = await self.async_client.create(**payload)<br>               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^<br>  File &quot;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openai\resources\chat\completions\completions.py&quot;, line 2454, in create<br>    return await self._post(<br>           ^^^^^^^^^^^^^^^^^<br>  File &quot;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openai\_base_client.py&quot;, line 1784, in post<br>    return await self.request(cast_to, opts, stream=stream, stream_cls=stream_cls)<br>           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^<br>  File &quot;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openai\_base_client.py&quot;, line 1584, in request<br>    raise self._make_status_error_from_response(err.response) from None<br>openai.AuthenticationError: Error code: 401 - {&#x27;error&#x27;: {&#x27;message&#x27;: &#x27;Incorrect API key provided: None. You can find your API key at https://platform.openai.com/account/api-keys.&#x27;, &#x27;type&#x27;: &#x27;invalid_request_error&#x27;, &#x27;param&#x27;: None, &#x27;code&#x27;: &#x27;invalid_api_key&#x27;}}<br><br><br>openai.AuthenticationError: Error code: 401 - {&#x27;error&#x27;: {&#x27;message&#x27;: &#x27;Incorrect API key provided: None. You can find your API key at https://platform.openai.com/account/api-keys.&#x27;, &#x27;type&#x27;: &#x27;invalid_request_error&#x27;, &#x27;param&#x27;: None, &#x27;code&#x27;: &#x27;invalid_api_key&#x27;}}</span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: Failed to pause job loop by development instance: No RFC password, cannot handle RFC calls.</span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: Failed to pause job loop by development instance: No RFC password, cannot handle RFC calls.</span><br>
<br><span style="font-weight: bold; color: rgb(255, 255, 255); background-color: rgb(108, 52, 131);">User message:</span><br>
<span style="color: rgb(255, 255, 255); ">&gt; hi</span><br>
<br><span style="color: rgb(128, 128, 128); ">Debug: Loading prompt variables from C:\Users\<USER>\Downloads\noryonv3\agent-zero-main\prompts/default\agent.system.tool.call_sub.py</span><br>
<br><span style="color: rgb(255, 0, 0); ">Traceback (most recent call last):<br>Traceback (most recent call last):<br>  File &quot;C:\Users\<USER>\Downloads\noryonv3\agent-zero-main\agent.py&quot;, line 334, in monologue<br>    prompt = await self.prepare_prompt(loop_data=self.loop_data)<br>             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^<br>  File &quot;C:\Users\<USER>\Downloads\noryonv3\agent-zero-main\agent.py&quot;, line 423, in prepare_prompt<br>    await self.call_extensions(&quot;message_loop_prompts_after&quot;, loop_data=loop_data)<br>  File &quot;C:\Users\<USER>\Downloads\noryonv3\agent-zero-main\agent.py&quot;, line 833, in call_extensions<br>    await cls(agent=self).execute(**kwargs)<br>  File &quot;C:\Users\<USER>\Downloads\noryonv3\agent-zero-main\python\extensions\message_loop_prompts_after\_91_recall_wait.py&quot;, line 13, in execute<br>    await task<br>  File &quot;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\futures.py&quot;, line 287, in __await__<br>    yield self  # This tells Task to wait for completion.<br>    ^^^^^^^^^^<br>  File &quot;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\tasks.py&quot;, line 385, in __wakeup<br>    future.result()<br>  File &quot;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\futures.py&quot;, line 203, in result<br>    raise self._exception.with_traceback(self._exception_tb)<br>  File &quot;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\tasks.py&quot;, line 314, in __step_run_and_handle_result<br>    result = coro.send(None)<br>             ^^^^^^^^^^^^^^^<br>  File &quot;C:\Users\<USER>\Downloads\noryonv3\agent-zero-main\python\extensions\message_loop_prompts_after\_50_recall_memories.py&quot;, line 60, in search_memories<br>    query = await self.agent.call_utility_model(<br>            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^<br>  File &quot;C:\Users\<USER>\Downloads\noryonv3\agent-zero-main\agent.py&quot;, line 633, in call_utility_model<br>    async for chunk in (prompt | model).astream({}):<br>  File &quot;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\langchain_core\runnables\base.py&quot;, line 3434, in astream<br>    async for chunk in self.atransform(input_aiter(), config, **kwargs):<br>  File &quot;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\langchain_core\runnables\base.py&quot;, line 3417, in atransform<br>    async for chunk in self._atransform_stream_with_config(<br>  File &quot;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\langchain_core\runnables\base.py&quot;, line 2305, in _atransform_stream_with_config<br>    chunk: Output = await asyncio.create_task(  # type: ignore[call-arg]<br>                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^<br>  File &quot;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\futures.py&quot;, line 287, in __await__<br>    yield self  # This tells Task to wait for completion.<br>    ^^^^^^^^^^<br>  File &quot;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\tasks.py&quot;, line 385, in __wakeup<br>    future.result()<br><br>&gt;&gt;&gt;  15 stack lines skipped &lt;&lt;&lt;<br><br>  File &quot;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\langchain_core\language_models\chat_models.py&quot;, line 512, in astream<br>    async for chunk in self._astream(<br>  File &quot;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\langchain_openai\chat_models\base.py&quot;, line 2316, in _astream<br>    async for chunk in super()._astream(*args, **kwargs):<br>  File &quot;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\langchain_openai\chat_models\base.py&quot;, line 1069, in _astream<br>    response = await self.async_client.create(**payload)<br>               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^<br>  File &quot;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openai\resources\chat\completions\completions.py&quot;, line 2454, in create<br>    return await self._post(<br>           ^^^^^^^^^^^^^^^^^<br>  File &quot;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openai\_base_client.py&quot;, line 1784, in post<br>    return await self.request(cast_to, opts, stream=stream, stream_cls=stream_cls)<br>           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^<br>  File &quot;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openai\_base_client.py&quot;, line 1584, in request<br>    raise self._make_status_error_from_response(err.response) from None<br>openai.AuthenticationError: Error code: 401 - {&#x27;error&#x27;: {&#x27;message&#x27;: &#x27;Incorrect API key provided: None. You can find your API key at https://platform.openai.com/account/api-keys.&#x27;, &#x27;type&#x27;: &#x27;invalid_request_error&#x27;, &#x27;param&#x27;: None, &#x27;code&#x27;: &#x27;invalid_api_key&#x27;}}<br><br><br>openai.AuthenticationError: Error code: 401 - {&#x27;error&#x27;: {&#x27;message&#x27;: &#x27;Incorrect API key provided: None. You can find your API key at https://platform.openai.com/account/api-keys.&#x27;, &#x27;type&#x27;: &#x27;invalid_request_error&#x27;, &#x27;param&#x27;: None, &#x27;code&#x27;: &#x27;invalid_api_key&#x27;}}</span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: API error: Traceback (most recent call last):<br>Traceback (most recent call last):<br>  File &quot;C:\Users\<USER>\Downloads\noryonv3\agent-zero-main\python\helpers\api.py&quot;, line 63, in handle_request<br>    output = await self.process(input_data, request)<br>             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^<br>  File &quot;C:\Users\<USER>\Downloads\noryonv3\agent-zero-main\python\api\get_work_dir_files.py&quot;, line 25, in process<br>    result = await runtime.call_development_function(get_files, current_path)<br>             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^<br>  File &quot;C:\Users\<USER>\Downloads\noryonv3\agent-zero-main\python\helpers\runtime.py&quot;, line 70, in call_development_function<br>    password = _get_rfc_password()<br>               ^^^^^^^^^^^^^^^^^^^<br>  File &quot;C:\Users\<USER>\Downloads\noryonv3\agent-zero-main\python\helpers\runtime.py&quot;, line 94, in _get_rfc_password<br>    raise Exception(&quot;No RFC password, cannot handle RFC calls.&quot;)<br>Exception: No RFC password, cannot handle RFC calls.<br></span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: API error: Traceback (most recent call last):<br>Traceback (most recent call last):<br>  File &quot;C:\Users\<USER>\Downloads\noryonv3\agent-zero-main\python\helpers\api.py&quot;, line 63, in handle_request<br>    output = await self.process(input_data, request)<br>             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^<br>  File &quot;C:\Users\<USER>\Downloads\noryonv3\agent-zero-main\python\api\get_work_dir_files.py&quot;, line 25, in process<br>    result = await runtime.call_development_function(get_files, current_path)<br>             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^<br>  File &quot;C:\Users\<USER>\Downloads\noryonv3\agent-zero-main\python\helpers\runtime.py&quot;, line 70, in call_development_function<br>    password = _get_rfc_password()<br>               ^^^^^^^^^^^^^^^^^^^<br>  File &quot;C:\Users\<USER>\Downloads\noryonv3\agent-zero-main\python\helpers\runtime.py&quot;, line 94, in _get_rfc_password<br>    raise Exception(&quot;No RFC password, cannot handle RFC calls.&quot;)<br>Exception: No RFC password, cannot handle RFC calls.<br></span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: Failed to pause job loop by development instance: No RFC password, cannot handle RFC calls.</span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: Failed to pause job loop by development instance: No RFC password, cannot handle RFC calls.</span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: Failed to pause job loop by development instance: No RFC password, cannot handle RFC calls.</span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: Failed to pause job loop by development instance: No RFC password, cannot handle RFC calls.</span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: Failed to pause job loop by development instance: No RFC password, cannot handle RFC calls.</span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: Failed to pause job loop by development instance: No RFC password, cannot handle RFC calls.</span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: Failed to pause job loop by development instance: No RFC password, cannot handle RFC calls.</span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: Failed to pause job loop by development instance: No RFC password, cannot handle RFC calls.</span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: Failed to pause job loop by development instance: No RFC password, cannot handle RFC calls.</span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: Failed to pause job loop by development instance: No RFC password, cannot handle RFC calls.</span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: Failed to pause job loop by development instance: No RFC password, cannot handle RFC calls.</span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: Failed to pause job loop by development instance: No RFC password, cannot handle RFC calls.</span><br>
<br><span style="font-weight: bold; color: rgb(255, 255, 255); background-color: rgb(108, 52, 131);">User message:</span><br>
<span style="color: rgb(255, 255, 255); ">&gt; hi</span><br>
<br><span style="color: rgb(128, 128, 128); ">Debug: Loading prompt variables from C:\Users\<USER>\Downloads\noryonv3\agent-zero-main\prompts/default\agent.system.tool.call_sub.py</span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: Failed to pause job loop by development instance: No RFC password, cannot handle RFC calls.</span><br>
<br><span style="color: rgb(255, 0, 0); ">Traceback (most recent call last):<br>Traceback (most recent call last):<br>  File &quot;C:\Users\<USER>\Downloads\noryonv3\agent-zero-main\agent.py&quot;, line 334, in monologue<br>    prompt = await self.prepare_prompt(loop_data=self.loop_data)<br>             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^<br>  File &quot;C:\Users\<USER>\Downloads\noryonv3\agent-zero-main\agent.py&quot;, line 423, in prepare_prompt<br>    await self.call_extensions(&quot;message_loop_prompts_after&quot;, loop_data=loop_data)<br>  File &quot;C:\Users\<USER>\Downloads\noryonv3\agent-zero-main\agent.py&quot;, line 833, in call_extensions<br>    await cls(agent=self).execute(**kwargs)<br>  File &quot;C:\Users\<USER>\Downloads\noryonv3\agent-zero-main\python\extensions\message_loop_prompts_after\_91_recall_wait.py&quot;, line 13, in execute<br>    await task<br>  File &quot;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\futures.py&quot;, line 287, in __await__<br>    yield self  # This tells Task to wait for completion.<br>    ^^^^^^^^^^<br>  File &quot;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\tasks.py&quot;, line 385, in __wakeup<br>    future.result()<br>  File &quot;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\futures.py&quot;, line 203, in result<br>    raise self._exception.with_traceback(self._exception_tb)<br>  File &quot;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\tasks.py&quot;, line 314, in __step_run_and_handle_result<br>    result = coro.send(None)<br>             ^^^^^^^^^^^^^^^<br>  File &quot;C:\Users\<USER>\Downloads\noryonv3\agent-zero-main\python\extensions\message_loop_prompts_after\_50_recall_memories.py&quot;, line 60, in search_memories<br>    query = await self.agent.call_utility_model(<br>            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^<br>  File &quot;C:\Users\<USER>\Downloads\noryonv3\agent-zero-main\agent.py&quot;, line 633, in call_utility_model<br>    async for chunk in (prompt | model).astream({}):<br>  File &quot;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\langchain_core\runnables\base.py&quot;, line 3434, in astream<br>    async for chunk in self.atransform(input_aiter(), config, **kwargs):<br>  File &quot;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\langchain_core\runnables\base.py&quot;, line 3417, in atransform<br>    async for chunk in self._atransform_stream_with_config(<br>  File &quot;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\langchain_core\runnables\base.py&quot;, line 2305, in _atransform_stream_with_config<br>    chunk: Output = await asyncio.create_task(  # type: ignore[call-arg]<br>                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^<br>  File &quot;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\futures.py&quot;, line 287, in __await__<br>    yield self  # This tells Task to wait for completion.<br>    ^^^^^^^^^^<br>  File &quot;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\tasks.py&quot;, line 385, in __wakeup<br>    future.result()<br><br>&gt;&gt;&gt;  15 stack lines skipped &lt;&lt;&lt;<br><br>  File &quot;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\langchain_core\language_models\chat_models.py&quot;, line 512, in astream<br>    async for chunk in self._astream(<br>  File &quot;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\langchain_openai\chat_models\base.py&quot;, line 2316, in _astream<br>    async for chunk in super()._astream(*args, **kwargs):<br>  File &quot;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\langchain_openai\chat_models\base.py&quot;, line 1069, in _astream<br>    response = await self.async_client.create(**payload)<br>               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^<br>  File &quot;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openai\resources\chat\completions\completions.py&quot;, line 2454, in create<br>    return await self._post(<br>           ^^^^^^^^^^^^^^^^^<br>  File &quot;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openai\_base_client.py&quot;, line 1784, in post<br>    return await self.request(cast_to, opts, stream=stream, stream_cls=stream_cls)<br>           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^<br>  File &quot;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\openai\_base_client.py&quot;, line 1584, in request<br>    raise self._make_status_error_from_response(err.response) from None<br>openai.AuthenticationError: Error code: 401 - {&#x27;error&#x27;: {&#x27;message&#x27;: &#x27;Incorrect API key provided: None. You can find your API key at https://platform.openai.com/account/api-keys.&#x27;, &#x27;type&#x27;: &#x27;invalid_request_error&#x27;, &#x27;param&#x27;: None, &#x27;code&#x27;: &#x27;invalid_api_key&#x27;}}<br><br><br>openai.AuthenticationError: Error code: 401 - {&#x27;error&#x27;: {&#x27;message&#x27;: &#x27;Incorrect API key provided: None. You can find your API key at https://platform.openai.com/account/api-keys.&#x27;, &#x27;type&#x27;: &#x27;invalid_request_error&#x27;, &#x27;param&#x27;: None, &#x27;code&#x27;: &#x27;invalid_api_key&#x27;}}</span><br>
