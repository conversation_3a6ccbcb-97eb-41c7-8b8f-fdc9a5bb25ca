You are tasked with generating a prompt that will be used by another AI to update a special reference file. This file contains important information and learnings that are used to carry out certain tasks. The file can be extended over time to incorporate new knowledge and experiences.

You have been provided with a subset of new events that may require updates to the special file. These events are:
<events>
{{ events }}
</events>

Your task is to analyze these events and determine what updates, if any, should be made to the special file. Then, you need to generate a prompt that will instruct another AI to make these updates correctly and efficiently.

When creating your prompt, follow these guidelines:
1. Clearly specify which parts of the file need to be updated or if new sections should be added.
2. Provide context for why these updates are necessary based on the new events.
3. Be specific about the information that should be added or modified.
4. Maintain the existing structure and formatting of the file.
5. Ensure that the updates are consistent with the current content and don't contradict existing information.

Now, based on the new events provided, generate a prompt that will guide the AI in making the appropriate updates to the special file. Your prompt should be clear, specific, and actionable. Include your prompt within <update_prompt> tags.

<update_prompt>

</update_prompt>
