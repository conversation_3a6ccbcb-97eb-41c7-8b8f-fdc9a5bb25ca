"""Third-party runtime implementations for OpenHands.

This module contains runtime implementations provided by third-party vendors.
These runtimes are optional and require additional dependencies to be installed.

To use third-party runtimes, install OpenHands with the third_party_runtimes extra:
    pip install openhands-ai[third_party_runtimes]

Available third-party runtimes:
- daytona: Daytona cloud development environment
- e2b: E2B secure sandbox environment  
- modal: Modal cloud compute platform
- runloop: Runloop AI sandbox environment
"""