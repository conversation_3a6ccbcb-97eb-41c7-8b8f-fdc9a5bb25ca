#!/usr/bin/env python3
"""
Local Voice Processing Module for Unified AI Agent
Handles speech-to-text and text-to-speech locally
"""

import os
import tempfile
import logging
from typing import Optional, Dict, Any
import asyncio
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LocalVoiceProcessor:
    """Local voice processing using Whisper and local TTS"""
    
    def __init__(self):
        self.whisper_model = None
        self.tts_engine = None
        self.initialized = False
        
    async def initialize(self):
        """Initialize voice processing components"""
        try:
            # Try to import and initialize Whisper
            await self._init_whisper()
            
            # Try to initialize TTS
            await self._init_tts()
            
            self.initialized = True
            logger.info("🎤 Local voice processor initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize voice processor: {e}")
            self.initialized = False
    
    async def _init_whisper(self):
        """Initialize Whisper for speech-to-text"""
        try:
            import whisper
            
            # Use the base model for good balance of speed/accuracy
            logger.info("🔄 Loading Whisper model...")
            self.whisper_model = whisper.load_model("base")
            logger.info("✅ Whisper model loaded successfully")
            
        except ImportError:
            logger.warning("⚠️ Whisper not installed. Install with: pip install openai-whisper")
            raise
        except Exception as e:
            logger.error(f"❌ Failed to load Whisper: {e}")
            raise
    
    async def _init_tts(self):
        """Initialize TTS engine - Windows SAPI only for reliability"""
        try:
            if os.name == 'nt':  # Windows
                import win32com.client
                self.tts_engine = win32com.client.Dispatch("SAPI.SpVoice")
                logger.info("✅ Windows SAPI TTS engine initialized successfully")
            else:
                # For non-Windows systems, use a simple fallback
                logger.warning("⚠️ Non-Windows system detected. TTS may not be available.")
                self.tts_engine = None
                raise Exception("TTS not supported on non-Windows systems in this implementation")

        except ImportError:
            logger.error("❌ pywin32 not available. Install with: pip install pywin32")
            raise
        except Exception as e:
            logger.error(f"❌ Failed to initialize TTS: {e}")
            raise
    
    async def speech_to_text(self, audio_file_path: str) -> Optional[str]:
        """Convert speech to text using Whisper"""
        if not self.initialized or not self.whisper_model:
            logger.error("❌ Voice processor not initialized")
            return None
        
        try:
            logger.info(f"🎤 Processing audio file: {audio_file_path}")
            
            # Use Whisper to transcribe
            result = self.whisper_model.transcribe(audio_file_path)
            text = result["text"].strip()
            
            logger.info(f"📝 Transcription: {text}")
            return text
            
        except Exception as e:
            logger.error(f"❌ Speech-to-text failed: {e}")
            return None
    
    async def text_to_speech(self, text: str, output_file: Optional[str] = None) -> bool:
        """Convert text to speech using Windows SAPI"""
        if not self.initialized or not self.tts_engine:
            logger.error("❌ Voice processor not initialized")
            return False

        try:
            logger.info(f"🔊 Converting text to speech: {text[:50]}...")

            # Use Windows SAPI to speak the text
            self.tts_engine.Speak(text)
            logger.info("🔊 Audio played via Windows SAPI")

            return True

        except Exception as e:
            logger.error(f"❌ Text-to-speech failed: {e}")
            return False
    
    async def process_voice_conversation(self, audio_file_path: str) -> Dict[str, Any]:
        """Process a complete voice conversation cycle"""
        if not self.initialized:
            return {
                "success": False,
                "error": "Voice processor not initialized"
            }
        
        try:
            # Step 1: Convert speech to text
            transcribed_text = await self.speech_to_text(audio_file_path)
            if not transcribed_text:
                return {
                    "success": False,
                    "error": "Failed to transcribe audio"
                }
            
            return {
                "success": True,
                "transcribed_text": transcribed_text,
                "audio_file": audio_file_path
            }
            
        except Exception as e:
            logger.error(f"❌ Voice conversation processing failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_status(self) -> Dict[str, Any]:
        """Get voice processor status"""
        return {
            "initialized": self.initialized,
            "whisper_available": self.whisper_model is not None,
            "tts_available": self.tts_engine is not None,
            "capabilities": [
                "speech_to_text",
                "text_to_speech", 
                "voice_conversation"
            ] if self.initialized else []
        }

# Global voice processor instance
voice_processor = LocalVoiceProcessor()

async def get_voice_processor() -> LocalVoiceProcessor:
    """Get initialized voice processor"""
    if not voice_processor.initialized:
        await voice_processor.initialize()
    return voice_processor
