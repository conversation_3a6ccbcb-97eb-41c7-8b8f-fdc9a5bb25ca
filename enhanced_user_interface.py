#!/usr/bin/env python3
"""
Enhanced User Interface - Easy-to-use interface for the Unified AI System
"""

import requests
import json
import time
import os
from typing import Dict, Any, Optional

class UnifiedAIInterface:
    """Enhanced user interface for the Unified AI System"""
    
    def __init__(self):
        self.base_url = "http://localhost:9001"
        self.session = requests.Session()
        self.capabilities = [
            "code_generation", "web_search", "voice_processing", 
            "multimodal_processing", "task_planning", "autonomous_execution",
            "advanced_development", "advanced_voice", "general_chat"
        ]
    
    def clear_screen(self):
        """Clear the terminal screen"""
        os.system('cls' if os.name == 'nt' else 'clear')
    
    def print_header(self):
        """Print the application header"""
        print("🤖 UNIFIED AI SYSTEM - Enhanced Interface")
        print("=" * 60)
        print("🚀 All capabilities working optimally!")
        print("💡 Use clear action words for best results")
        print("-" * 60)
    
    def print_capabilities(self):
        """Print available capabilities with examples"""
        print("\n📋 AVAILABLE CAPABILITIES:")
        print("-" * 40)
        
        examples = {
            "🔍 Web Search": [
                "Search for latest AI news",
                "Find information about Python 3.12",
                "Research quantum computing trends"
            ],
            "💻 Code Generation": [
                "Write a Python calculator function",
                "Create a JavaScript sorting algorithm",
                "Generate SQL query for user data"
            ],
            "🎯 Task Planning": [
                "Plan a web development project",
                "Create a learning roadmap for AI",
                "Organize a software migration plan"
            ],
            "🎤 Voice Processing": [
                "Convert text to speech",
                "Process audio commands",
                "Generate voice responses"
            ],
            "👁️ Multimodal": [
                "Analyze this image",
                "Describe visual content",
                "Process multimedia data"
            ],
            "🔧 Advanced Development": [
                "Review code architecture",
                "Optimize system performance",
                "Debug complex issues"
            ],
            "💬 General Chat": [
                "Explain machine learning concepts",
                "Discuss technology trends",
                "Answer general questions"
            ]
        }
        
        for capability, example_list in examples.items():
            print(f"\n{capability}:")
            for example in example_list:
                print(f"   • {example}")
    
    def send_request(self, message: str, capability: Optional[str] = None) -> Dict[str, Any]:
        """Send request to the AI system"""
        try:
            payload = {"message": message}
            if capability:
                payload["capability"] = capability
            
            print(f"\n🔄 Processing: {message}")
            start_time = time.time()
            
            response = self.session.post(
                f"{self.base_url}/chat",
                json=payload,
                timeout=60
            )
            
            end_time = time.time()
            
            if response.status_code == 200:
                data = response.json()
                data["response_time"] = end_time - start_time
                return data
            else:
                return {
                    "error": f"Request failed with status {response.status_code}",
                    "response_time": end_time - start_time
                }
                
        except Exception as e:
            return {
                "error": f"Request error: {str(e)}",
                "response_time": 0
            }
    
    def format_response(self, data: Dict[str, Any]) -> str:
        """Format the AI response for display"""
        if "error" in data:
            return f"❌ Error: {data['error']}"
        
        response_text = data.get("response", "No response received")
        capability_used = data.get("capability_used", "unknown")
        model_used = data.get("model_used", "unknown")
        response_time = data.get("response_time", 0)
        
        formatted = f"""
🎯 Capability Used: {capability_used}
🤖 Model Used: {model_used}
⏱️ Response Time: {response_time:.2f}s
{'=' * 60}

{response_text}

{'=' * 60}
"""
        return formatted
    
    def interactive_mode(self):
        """Run interactive chat mode"""
        self.clear_screen()
        self.print_header()
        
        print("\n🎮 INTERACTIVE MODE")
        print("Type 'help' for examples, 'capabilities' to see all options, 'quit' to exit")
        print("-" * 60)
        
        while True:
            try:
                user_input = input("\n💬 You: ").strip()
                
                if not user_input:
                    continue
                
                if user_input.lower() in ['quit', 'exit', 'q']:
                    print("👋 Goodbye!")
                    break
                
                if user_input.lower() == 'help':
                    self.show_help()
                    continue
                
                if user_input.lower() == 'capabilities':
                    self.print_capabilities()
                    continue
                
                if user_input.lower() == 'clear':
                    self.clear_screen()
                    self.print_header()
                    continue
                
                # Send request to AI
                response_data = self.send_request(user_input)
                formatted_response = self.format_response(response_data)
                print(f"\n🤖 AI: {formatted_response}")
                
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"\n❌ Error: {e}")
    
    def show_help(self):
        """Show help information"""
        print("\n📚 HELP - How to get the best results:")
        print("-" * 50)
        print("🔍 For Web Search:")
        print("   • Start with 'Search for', 'Find information about', 'Research'")
        print("   • Example: 'Search for latest Python updates'")
        
        print("\n💻 For Code Generation:")
        print("   • Start with 'Write', 'Create', 'Generate'")
        print("   • Example: 'Write a Python function to sort a list'")
        
        print("\n🎯 For Task Planning:")
        print("   • Start with 'Plan', 'Create a plan', 'Organize'")
        print("   • Example: 'Plan a machine learning project'")
        
        print("\n💡 Pro Tips:")
        print("   • Be specific about what you want")
        print("   • Use clear action words")
        print("   • Ask follow-up questions for clarification")
        
        print("\n🎮 Commands:")
        print("   • 'help' - Show this help")
        print("   • 'capabilities' - Show all capabilities with examples")
        print("   • 'clear' - Clear screen")
        print("   • 'quit' - Exit the program")
    
    def quick_test_mode(self):
        """Run quick test of all capabilities"""
        self.clear_screen()
        self.print_header()
        
        print("\n🧪 QUICK TEST MODE")
        print("Testing all capabilities...")
        print("-" * 60)
        
        test_cases = [
            ("Search for latest AI developments", "web_search"),
            ("Write a Python hello world function", "code_generation"),
            ("Plan a simple web project", "task_planning"),
            ("Explain machine learning", "general_chat")
        ]
        
        for message, expected_capability in test_cases:
            print(f"\n🔄 Testing: {message}")
            response_data = self.send_request(message)
            
            if "error" not in response_data:
                capability_used = response_data.get("capability_used", "unknown")
                response_time = response_data.get("response_time", 0)
                
                if capability_used == expected_capability:
                    print(f"✅ Success: {capability_used} ({response_time:.2f}s)")
                else:
                    print(f"⚠️  Routed to: {capability_used} (expected {expected_capability})")
            else:
                print(f"❌ Failed: {response_data['error']}")
        
        print("\n✅ Quick test completed!")
        input("\nPress Enter to continue...")

def main():
    """Main function"""
    interface = UnifiedAIInterface()
    
    while True:
        interface.clear_screen()
        interface.print_header()
        
        print("\n🎯 SELECT MODE:")
        print("1. Interactive Chat Mode")
        print("2. Quick Test Mode")
        print("3. Show Capabilities")
        print("4. Exit")
        
        choice = input("\nEnter your choice (1-4): ").strip()
        
        if choice == '1':
            interface.interactive_mode()
        elif choice == '2':
            interface.quick_test_mode()
        elif choice == '3':
            interface.print_capabilities()
            input("\nPress Enter to continue...")
        elif choice == '4':
            print("👋 Goodbye!")
            break
        else:
            print("❌ Invalid choice. Please try again.")
            time.sleep(1)

if __name__ == "__main__":
    main()
