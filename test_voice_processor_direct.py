#!/usr/bin/env python3
"""
Test the voice processor directly to see if it's actually working
"""

import asyncio
import tempfile
import os
from local_voice_processor import get_voice_processor

async def test_voice_processor():
    print("🧪 Testing Voice Processor Directly...")
    
    try:
        # Get voice processor
        processor = await get_voice_processor()
        
        # Check status
        status = processor.get_status()
        print(f"📊 Voice Processor Status:")
        print(f"   Initialized: {status['initialized']}")
        print(f"   Whisper Available: {status['whisper_available']}")
        print(f"   TTS Available: {status['tts_available']}")
        print(f"   Capabilities: {status['capabilities']}")
        
        if status['initialized']:
            print("✅ Voice processor is properly initialized!")
            
            # Test TTS
            print("\n🔊 Testing Text-to-Speech...")
            tts_result = await processor.text_to_speech("Hello, this is a test of the voice processing system.")
            print(f"   TTS Result: {tts_result}")
            
            # Test with a simple audio file (we'll create a dummy one)
            print("\n🎤 Testing Speech-to-Text...")
            print("   (Note: This would require an actual audio file)")
            
        else:
            print("❌ Voice processor failed to initialize")
            
    except Exception as e:
        print(f"💥 Error testing voice processor: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    asyncio.run(test_voice_processor())
