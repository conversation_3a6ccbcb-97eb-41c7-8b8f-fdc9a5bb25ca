#!/usr/bin/env python3
"""
Test voice processing and image analysis capabilities
"""

import asyncio
import aiohttp
import json
import time
import base64
import os
from typing import Dict, Any

class VoiceVisionTester:
    def __init__(self):
        self.base_url = "http://localhost:9002"
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def test_voice_processing(self) -> Dict[str, Any]:
        """Test voice processing capabilities"""
        print("🎤 TESTING VOICE PROCESSING")
        print("=" * 40)
        
        # Test text-to-speech
        print("🔊 Testing Text-to-Speech...")
        start_time = time.time()
        
        try:
            tts_data = {
                "text": "Hello! This is a test of the text to speech system. The unified AI agent is working perfectly!",
                "voice": "default"
            }
            
            async with self.session.post(
                f"{self.base_url}/tts", 
                json=tts_data,
                timeout=30
            ) as response:
                end_time = time.time()
                response_time = end_time - start_time
                
                if response.status == 200:
                    result = await response.json()
                    print(f"   ✅ TTS Success ({response_time:.2f}s)")
                    print(f"   Status: {result.get('status', 'unknown')}")
                    return {
                        "tts_status": "SUCCESS",
                        "tts_time": f"{response_time:.2f}s",
                        "tts_result": result
                    }
                else:
                    print(f"   ❌ TTS Failed: HTTP {response.status}")
                    return {
                        "tts_status": "ERROR",
                        "tts_time": f"{response_time:.2f}s",
                        "tts_error": f"HTTP {response.status}"
                    }
        except Exception as e:
            end_time = time.time()
            response_time = end_time - start_time
            print(f"   ❌ TTS Error: {str(e)}")
            return {
                "tts_status": "ERROR",
                "tts_time": f"{response_time:.2f}s",
                "tts_error": str(e)
            }
    
    async def test_image_analysis(self) -> Dict[str, Any]:
        """Test image analysis capabilities"""
        print("\n👁️ TESTING IMAGE ANALYSIS")
        print("=" * 40)

        # Create a minimal test PNG image (1x1 red pixel)
        import base64

        # This is a minimal 1x1 red pixel PNG image in base64
        test_png_b64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="
        img_bytes = base64.b64decode(test_png_b64)

        print("🖼️ Testing Image Analysis with test image...")
        start_time = time.time()

        try:
            # Create form data for file upload
            data = aiohttp.FormData()
            data.add_field('file', img_bytes, filename='test_image.png', content_type='image/png')
            data.add_field('analysis_type', 'general')

            async with self.session.post(
                f"{self.base_url}/image",
                data=data,
                timeout=120  # Image analysis can take longer
            ) as response:
                end_time = time.time()
                response_time = end_time - start_time

                if response.status == 200:
                    result = await response.json()
                    analysis = result.get('analysis', 'No analysis provided')
                    print(f"   ✅ Image Analysis Success ({response_time:.2f}s)")
                    print(f"   Analysis Preview: {analysis[:100]}...")
                    return {
                        "vision_status": "SUCCESS",
                        "vision_time": f"{response_time:.2f}s",
                        "vision_analysis": analysis,
                        "vision_model": result.get('model_used', 'unknown')
                    }
                else:
                    error_text = await response.text()
                    print(f"   ❌ Image Analysis Failed: HTTP {response.status}")
                    print(f"   Error: {error_text[:100]}...")
                    return {
                        "vision_status": "ERROR",
                        "vision_time": f"{response_time:.2f}s",
                        "vision_error": f"HTTP {response.status}: {error_text[:100]}"
                    }
        except Exception as e:
            end_time = time.time()
            response_time = end_time - start_time
            print(f"   ❌ Image Analysis Error: {str(e)}")
            return {
                "vision_status": "ERROR",
                "vision_time": f"{response_time:.2f}s",
                "vision_error": str(e)
            }
    
    async def test_multimodal_chat(self) -> Dict[str, Any]:
        """Test multimodal chat with voice-like query"""
        print("\n🧠 TESTING MULTIMODAL CHAT")
        print("=" * 40)
        
        print("💬 Testing voice-style conversation...")
        start_time = time.time()
        
        try:
            chat_data = {
                "message": "Can you hear me? I want to test if you can process voice-like requests and respond naturally."
            }
            
            async with self.session.post(
                f"{self.base_url}/chat", 
                json=chat_data,
                timeout=30
            ) as response:
                end_time = time.time()
                response_time = end_time - start_time
                
                if response.status == 200:
                    result = await response.json()
                    response_text = result.get('response', 'No response')
                    print(f"   ✅ Multimodal Chat Success ({response_time:.2f}s)")
                    print(f"   Response Preview: {response_text[:100]}...")
                    return {
                        "chat_status": "SUCCESS",
                        "chat_time": f"{response_time:.2f}s",
                        "chat_response": response_text,
                        "chat_model": result.get('model_used', 'unknown')
                    }
                else:
                    print(f"   ❌ Multimodal Chat Failed: HTTP {response.status}")
                    return {
                        "chat_status": "ERROR",
                        "chat_time": f"{response_time:.2f}s",
                        "chat_error": f"HTTP {response.status}"
                    }
        except Exception as e:
            end_time = time.time()
            response_time = end_time - start_time
            print(f"   ❌ Multimodal Chat Error: {str(e)}")
            return {
                "chat_status": "ERROR",
                "chat_time": f"{response_time:.2f}s",
                "chat_error": str(e)
            }

async def main():
    """Main test function"""
    
    print("🎯 VOICE & VISION CAPABILITIES TEST")
    print("=" * 50)
    print("Testing advanced capabilities of the unified AI system")
    print()
    
    # Check if server is running
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get("http://localhost:9002/health", timeout=5) as response:
                if response.status != 200:
                    print("❌ Unified AI Agent is not running on port 9002")
                    return
    except Exception as e:
        print("❌ Cannot connect to Unified AI Agent on port 9002")
        return
    
    # Run tests
    async with VoiceVisionTester() as tester:
        # Test all capabilities
        voice_result = await tester.test_voice_processing()
        vision_result = await tester.test_image_analysis()
        chat_result = await tester.test_multimodal_chat()
        
        # Summary
        print("\n" + "=" * 50)
        print("📊 VOICE & VISION TEST SUMMARY")
        print("=" * 50)
        
        capabilities = [
            ("🎤 Voice Processing (TTS)", voice_result.get("tts_status"), voice_result.get("tts_time")),
            ("👁️ Image Analysis", vision_result.get("vision_status"), vision_result.get("vision_time")),
            ("🧠 Multimodal Chat", chat_result.get("chat_status"), chat_result.get("chat_time"))
        ]
        
        success_count = 0
        for name, status, time_taken in capabilities:
            status_icon = "✅" if status == "SUCCESS" else "❌"
            print(f"{status_icon} {name}: {status} ({time_taken})")
            if status == "SUCCESS":
                success_count += 1
        
        print(f"\n🎯 SUCCESS RATE: {success_count}/3 ({success_count/3*100:.1f}%)")
        
        if success_count == 3:
            print("\n🎉 ALL VOICE & VISION CAPABILITIES WORKING!")
            print("Your unified AI system supports:")
            print("   ✅ Text-to-Speech (Voice Output)")
            print("   ✅ Image Analysis (Computer Vision)")
            print("   ✅ Multimodal Conversations")
        elif success_count >= 2:
            print("\n⚠️ MOST CAPABILITIES WORKING")
            print("Working capabilities:")
            for name, status, _ in capabilities:
                if status == "SUCCESS":
                    print(f"   ✅ {name}")
        else:
            print("\n❌ CAPABILITIES NEED DEBUGGING")
        
        # Show detailed results
        if vision_result.get("vision_status") == "SUCCESS":
            print(f"\n🔍 IMAGE ANALYSIS DETAILS:")
            print(f"   Model: {vision_result.get('vision_model', 'unknown')}")
            print(f"   Analysis: {vision_result.get('vision_analysis', 'No analysis')[:200]}...")

if __name__ == "__main__":
    asyncio.run(main())
