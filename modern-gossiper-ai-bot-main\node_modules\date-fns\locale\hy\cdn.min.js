var q=function(J){return q=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(G){return typeof G}:function(G){return G&&typeof Symbol=="function"&&G.constructor===Symbol&&G!==Symbol.prototype?"symbol":typeof G},q(J)},z=function(J,G){var Y=Object.keys(J);if(Object.getOwnPropertySymbols){var A=Object.getOwnPropertySymbols(J);G&&(A=A.filter(function(D){return Object.getOwnPropertyDescriptor(J,D).enumerable})),Y.push.apply(Y,A)}return Y},x=function(J){for(var G=1;G<arguments.length;G++){var Y=arguments[G]!=null?arguments[G]:{};G%2?z(Object(Y),!0).forEach(function(A){B5(J,A,Y[A])}):Object.getOwnPropertyDescriptors?Object.defineProperties(J,Object.getOwnPropertyDescriptors(Y)):z(Object(Y)).forEach(function(A){Object.defineProperty(J,A,Object.getOwnPropertyDescriptor(Y,A))})}return J},B5=function(J,G,Y){if(G=H5(G),G in J)Object.defineProperty(J,G,{value:Y,enumerable:!0,configurable:!0,writable:!0});else J[G]=Y;return J},H5=function(J){var G=U5(J,"string");return q(G)=="symbol"?G:String(G)},U5=function(J,G){if(q(J)!="object"||!J)return J;var Y=J[Symbol.toPrimitive];if(Y!==void 0){var A=Y.call(J,G||"default");if(q(A)!="object")return A;throw new TypeError("@@toPrimitive must return a primitive value.")}return(G==="string"?String:Number)(J)};(function(J){var G=Object.defineProperty,Y=function B(C,U){for(var H in U)G(C,H,{get:U[H],enumerable:!0,configurable:!0,set:function X(Z){return U[H]=function(){return Z}}})},A={lessThanXSeconds:{one:"\u0561\u057E\u0565\u056C\u056B \u0584\u056B\u0579 \u0584\u0561\u0576 1 \u057E\u0561\u0575\u0580\u056F\u0575\u0561\u0576",other:"\u0561\u057E\u0565\u056C\u056B \u0584\u056B\u0579 \u0584\u0561\u0576 {{count}} \u057E\u0561\u0575\u0580\u056F\u0575\u0561\u0576"},xSeconds:{one:"1 \u057E\u0561\u0575\u0580\u056F\u0575\u0561\u0576",other:"{{count}} \u057E\u0561\u0575\u0580\u056F\u0575\u0561\u0576"},halfAMinute:"\u056F\u0565\u057D \u0580\u0578\u057A\u0565",lessThanXMinutes:{one:"\u0561\u057E\u0565\u056C\u056B \u0584\u056B\u0579 \u0584\u0561\u0576 1 \u0580\u0578\u057A\u0565",other:"\u0561\u057E\u0565\u056C\u056B \u0584\u056B\u0579 \u0584\u0561\u0576 {{count}} \u0580\u0578\u057A\u0565"},xMinutes:{one:"1 \u0580\u0578\u057A\u0565",other:"{{count}} \u0580\u0578\u057A\u0565"},aboutXHours:{one:"\u0574\u0578\u057F 1 \u056A\u0561\u0574",other:"\u0574\u0578\u057F {{count}} \u056A\u0561\u0574"},xHours:{one:"1 \u056A\u0561\u0574",other:"{{count}} \u056A\u0561\u0574"},xDays:{one:"1 \u0585\u0580",other:"{{count}} \u0585\u0580"},aboutXWeeks:{one:"\u0574\u0578\u057F 1 \u0577\u0561\u0562\u0561\u0569",other:"\u0574\u0578\u057F {{count}} \u0577\u0561\u0562\u0561\u0569"},xWeeks:{one:"1 \u0577\u0561\u0562\u0561\u0569",other:"{{count}} \u0577\u0561\u0562\u0561\u0569"},aboutXMonths:{one:"\u0574\u0578\u057F 1 \u0561\u0574\u056B\u057D",other:"\u0574\u0578\u057F {{count}} \u0561\u0574\u056B\u057D"},xMonths:{one:"1 \u0561\u0574\u056B\u057D",other:"{{count}} \u0561\u0574\u056B\u057D"},aboutXYears:{one:"\u0574\u0578\u057F 1 \u057F\u0561\u0580\u056B",other:"\u0574\u0578\u057F {{count}} \u057F\u0561\u0580\u056B"},xYears:{one:"1 \u057F\u0561\u0580\u056B",other:"{{count}} \u057F\u0561\u0580\u056B"},overXYears:{one:"\u0561\u057E\u0565\u056C\u056B \u0584\u0561\u0576 1 \u057F\u0561\u0580\u056B",other:"\u0561\u057E\u0565\u056C\u056B \u0584\u0561\u0576 {{count}} \u057F\u0561\u0580\u056B"},almostXYears:{one:"\u0570\u0561\u0574\u0561\u0580\u0575\u0561 1 \u057F\u0561\u0580\u056B",other:"\u0570\u0561\u0574\u0561\u0580\u0575\u0561 {{count}} \u057F\u0561\u0580\u056B"}},D=function B(C,U,H){var X,Z=A[C];if(typeof Z==="string")X=Z;else if(U===1)X=Z.one;else X=Z.other.replace("{{count}}",String(U));if(H!==null&&H!==void 0&&H.addSuffix)if(H.comparison&&H.comparison>0)return X+" \u0570\u0565\u057F\u0578";else return X+" \u0561\u057C\u0561\u057B";return X};function N(B){return function(){var C=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},U=C.width?String(C.width):B.defaultWidth,H=B.formats[U]||B.formats[B.defaultWidth];return H}}var M={full:"d MMMM, y, EEEE",long:"d MMMM, y",medium:"d MMM, y",short:"dd.MM.yyyy"},$={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},R={full:"{{date}} '\u056A\u2024'{{time}}",long:"{{date}} '\u056A\u2024'{{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},S={date:N({formats:M,defaultWidth:"full"}),time:N({formats:$,defaultWidth:"full"}),dateTime:N({formats:R,defaultWidth:"full"})},L={lastWeek:"'\u0576\u0561\u056D\u0578\u0580\u0564' eeee p'\u058A\u056B\u0576'",yesterday:"'\u0565\u0580\u0565\u056F' p'\u058A\u056B\u0576'",today:"'\u0561\u0575\u057D\u0585\u0580' p'\u058A\u056B\u0576'",tomorrow:"'\u057E\u0561\u0572\u0568' p'\u058A\u056B\u0576'",nextWeek:"'\u0570\u0561\u057B\u0578\u0580\u0564' eeee p'\u058A\u056B\u0576'",other:"P"},V=function B(C,U,H,X){return L[C]};function O(B){return function(C,U){var H=U!==null&&U!==void 0&&U.context?String(U.context):"standalone",X;if(H==="formatting"&&B.formattingValues){var Z=B.defaultFormattingWidth||B.defaultWidth,I=U!==null&&U!==void 0&&U.width?String(U.width):Z;X=B.formattingValues[I]||B.formattingValues[Z]}else{var T=B.defaultWidth,K=U!==null&&U!==void 0&&U.width?String(U.width):B.defaultWidth;X=B.values[K]||B.values[T]}var E=B.argumentCallback?B.argumentCallback(C):C;return X[E]}}var f={narrow:["\u0554","\u0544"],abbreviated:["\u0554\u0531","\u0544\u0539"],wide:["\u0554\u0580\u056B\u057D\u057F\u0578\u057D\u056B\u0581 \u0561\u057C\u0561\u057B","\u0544\u0565\u0580 \u0569\u057E\u0561\u0580\u056F\u0578\u0582\u0569\u0575\u0561\u0576"]},j={narrow:["1","2","3","4"],abbreviated:["\u05541","\u05542","\u05543","\u05544"],wide:["1\u058A\u056B\u0576 \u0584\u0561\u057C\u0578\u0580\u0564","2\u058A\u0580\u0564 \u0584\u0561\u057C\u0578\u0580\u0564","3\u058A\u0580\u0564 \u0584\u0561\u057C\u0578\u0580\u0564","4\u058A\u0580\u0564 \u0584\u0561\u057C\u0578\u0580\u0564"]},F={narrow:["\u0540","\u0553","\u0544","\u0531","\u0544","\u0540","\u0540","\u0555","\u054D","\u0540","\u0546","\u0534"],abbreviated:["\u0570\u0578\u0582\u0576","\u0583\u0565\u057F","\u0574\u0561\u0580","\u0561\u057A\u0580","\u0574\u0561\u0575","\u0570\u0578\u0582\u0576","\u0570\u0578\u0582\u056C","\u0585\u0563\u057D","\u057D\u0565\u057A","\u0570\u0578\u056F","\u0576\u0578\u0575","\u0564\u0565\u056F"],wide:["\u0570\u0578\u0582\u0576\u057E\u0561\u0580","\u0583\u0565\u057F\u0580\u057E\u0561\u0580","\u0574\u0561\u0580\u057F","\u0561\u057A\u0580\u056B\u056C","\u0574\u0561\u0575\u056B\u057D","\u0570\u0578\u0582\u0576\u056B\u057D","\u0570\u0578\u0582\u056C\u056B\u057D","\u0585\u0563\u0578\u057D\u057F\u0578\u057D","\u057D\u0565\u057A\u057F\u0565\u0574\u0562\u0565\u0580","\u0570\u0578\u056F\u057F\u0565\u0574\u0562\u0565\u0580","\u0576\u0578\u0575\u0565\u0574\u0562\u0565\u0580","\u0564\u0565\u056F\u057F\u0565\u0574\u0562\u0565\u0580"]},v={narrow:["\u053F","\u0535","\u0535","\u0549","\u0540","\u0548","\u0547"],short:["\u056F\u0580","\u0565\u0580","\u0565\u0584","\u0579\u0584","\u0570\u0563","\u0578\u0582\u0580","\u0577\u0562"],abbreviated:["\u056F\u056B\u0580","\u0565\u0580\u056F","\u0565\u0580\u0584","\u0579\u0578\u0580","\u0570\u0576\u0563","\u0578\u0582\u0580\u0562","\u0577\u0561\u0562"],wide:["\u056F\u056B\u0580\u0561\u056F\u056B","\u0565\u0580\u056F\u0578\u0582\u0577\u0561\u0562\u0569\u056B","\u0565\u0580\u0565\u0584\u0577\u0561\u0562\u0569\u056B","\u0579\u0578\u0580\u0565\u0584\u0577\u0561\u0562\u0569\u056B","\u0570\u056B\u0576\u0563\u0577\u0561\u0562\u0569\u056B","\u0578\u0582\u0580\u0562\u0561\u0569","\u0577\u0561\u0562\u0561\u0569"]},w={narrow:{am:"a",pm:"p",midnight:"\u056F\u0565\u057D\u0563\u0577",noon:"\u056F\u0565\u057D\u0585\u0580",morning:"\u0561\u057C\u0561\u057E\u0578\u057F",afternoon:"\u0581\u0565\u0580\u0565\u056F",evening:"\u0565\u0580\u0565\u056F\u0578",night:"\u0563\u056B\u0577\u0565\u0580"},abbreviated:{am:"AM",pm:"PM",midnight:"\u056F\u0565\u057D\u0563\u056B\u0577\u0565\u0580",noon:"\u056F\u0565\u057D\u0585\u0580",morning:"\u0561\u057C\u0561\u057E\u0578\u057F",afternoon:"\u0581\u0565\u0580\u0565\u056F",evening:"\u0565\u0580\u0565\u056F\u0578",night:"\u0563\u056B\u0577\u0565\u0580"},wide:{am:"a.m.",pm:"p.m.",midnight:"\u056F\u0565\u057D\u0563\u056B\u0577\u0565\u0580",noon:"\u056F\u0565\u057D\u0585\u0580",morning:"\u0561\u057C\u0561\u057E\u0578\u057F",afternoon:"\u0581\u0565\u0580\u0565\u056F",evening:"\u0565\u0580\u0565\u056F\u0578",night:"\u0563\u056B\u0577\u0565\u0580"}},_={narrow:{am:"a",pm:"p",midnight:"\u056F\u0565\u057D\u0563\u0577",noon:"\u056F\u0565\u057D\u0585\u0580",morning:"\u0561\u057C\u0561\u057E\u0578\u057F\u0568",afternoon:"\u0581\u0565\u0580\u0565\u056F\u0568",evening:"\u0565\u0580\u0565\u056F\u0578\u0575\u0561\u0576",night:"\u0563\u056B\u0577\u0565\u0580\u0568"},abbreviated:{am:"AM",pm:"PM",midnight:"\u056F\u0565\u057D\u0563\u056B\u0577\u0565\u0580\u056B\u0576",noon:"\u056F\u0565\u057D\u0585\u0580\u056B\u0576",morning:"\u0561\u057C\u0561\u057E\u0578\u057F\u0568",afternoon:"\u0581\u0565\u0580\u0565\u056F\u0568",evening:"\u0565\u0580\u0565\u056F\u0578\u0575\u0561\u0576",night:"\u0563\u056B\u0577\u0565\u0580\u0568"},wide:{am:"a.m.",pm:"p.m.",midnight:"\u056F\u0565\u057D\u0563\u056B\u0577\u0565\u0580\u056B\u0576",noon:"\u056F\u0565\u057D\u0585\u0580\u056B\u0576",morning:"\u0561\u057C\u0561\u057E\u0578\u057F\u0568",afternoon:"\u0581\u0565\u0580\u0565\u056F\u0568",evening:"\u0565\u0580\u0565\u056F\u0578\u0575\u0561\u0576",night:"\u0563\u056B\u0577\u0565\u0580\u0568"}},P=function B(C,U){var H=Number(C),X=H%100;if(X<10){if(X%10===1)return H+"\u058A\u056B\u0576"}return H+"\u058A\u0580\u0564"},k={ordinalNumber:P,era:O({values:f,defaultWidth:"wide"}),quarter:O({values:j,defaultWidth:"wide",argumentCallback:function B(C){return C-1}}),month:O({values:F,defaultWidth:"wide"}),day:O({values:v,defaultWidth:"wide"}),dayPeriod:O({values:w,defaultWidth:"wide",formattingValues:_,defaultFormattingWidth:"wide"})};function Q(B){return function(C){var U=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},H=U.width,X=H&&B.matchPatterns[H]||B.matchPatterns[B.defaultMatchWidth],Z=C.match(X);if(!Z)return null;var I=Z[0],T=H&&B.parsePatterns[H]||B.parsePatterns[B.defaultParseWidth],K=Array.isArray(T)?b(T,function(W){return W.test(I)}):u(T,function(W){return W.test(I)}),E;E=B.valueCallback?B.valueCallback(K):K,E=U.valueCallback?U.valueCallback(E):E;var t=C.slice(I.length);return{value:E,rest:t}}}var u=function B(C,U){for(var H in C)if(Object.prototype.hasOwnProperty.call(C,H)&&U(C[H]))return H;return},b=function B(C,U){for(var H=0;H<C.length;H++)if(U(C[H]))return H;return};function h(B){return function(C){var U=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},H=C.match(B.matchPattern);if(!H)return null;var X=H[0],Z=C.match(B.parsePattern);if(!Z)return null;var I=B.valueCallback?B.valueCallback(Z[0]):Z[0];I=U.valueCallback?U.valueCallback(I):I;var T=C.slice(X.length);return{value:I,rest:T}}}var c=/^(\d+)((-|֊)?(ին|րդ))?/i,m=/\d+/i,y={narrow:/^(Ք|Մ)/i,abbreviated:/^(Ք\.?\s?Ա\.?|Մ\.?\s?Թ\.?\s?Ա\.?|Մ\.?\s?Թ\.?|Ք\.?\s?Հ\.?)/i,wide:/^(քրիստոսից առաջ|մեր թվարկությունից առաջ|մեր թվարկության|քրիստոսից հետո)/i},g={any:[/^ք/i,/^մ/i]},p={narrow:/^[1234]/i,abbreviated:/^ք[1234]/i,wide:/^[1234]((-|֊)?(ին|րդ)) քառորդ/i},d={any:[/1/i,/2/i,/3/i,/4/i]},l={narrow:/^[հփմաօսնդ]/i,abbreviated:/^(հուն|փետ|մար|ապր|մայ|հուն|հուլ|օգս|սեպ|հոկ|նոյ|դեկ)/i,wide:/^(հունվար|փետրվար|մարտ|ապրիլ|մայիս|հունիս|հուլիս|օգոստոս|սեպտեմբեր|հոկտեմբեր|նոյեմբեր|դեկտեմբեր)/i},i={narrow:[/^հ/i,/^փ/i,/^մ/i,/^ա/i,/^մ/i,/^հ/i,/^հ/i,/^օ/i,/^ս/i,/^հ/i,/^ն/i,/^դ/i],any:[/^հու/i,/^փ/i,/^մար/i,/^ա/i,/^մայ/i,/^հուն/i,/^հուլ/i,/^օ/i,/^ս/i,/^հոկ/i,/^ն/i,/^դ/i]},n={narrow:/^[եչհոշկ]/i,short:/^(կր|եր|եք|չք|հգ|ուր|շբ)/i,abbreviated:/^(կիր|երկ|երք|չոր|հնգ|ուրբ|շաբ)/i,wide:/^(կիրակի|երկուշաբթի|երեքշաբթի|չորեքշաբթի|հինգշաբթի|ուրբաթ|շաբաթ)/i},s={narrow:[/^կ/i,/^ե/i,/^ե/i,/^չ/i,/^հ/i,/^(ո|Ո)/,/^շ/i],short:[/^կ/i,/^եր/i,/^եք/i,/^չ/i,/^հ/i,/^(ո|Ո)/,/^շ/i],abbreviated:[/^կ/i,/^երկ/i,/^երք/i,/^չ/i,/^հ/i,/^(ո|Ո)/,/^շ/i],wide:[/^կ/i,/^երկ/i,/^երե/i,/^չ/i,/^հ/i,/^(ո|Ո)/,/^շ/i]},o={narrow:/^([ap]|կեսգշ|կեսօր|(առավոտը?|ցերեկը?|երեկո(յան)?|գիշերը?))/i,any:/^([ap]\.?\s?m\.?|կեսգիշեր(ին)?|կեսօր(ին)?|(առավոտը?|ցերեկը?|երեկո(յան)?|գիշերը?))/i},r={any:{am:/^a/i,pm:/^p/i,midnight:/կեսգիշեր/i,noon:/կեսօր/i,morning:/առավոտ/i,afternoon:/ցերեկ/i,evening:/երեկո/i,night:/գիշեր/i}},e={ordinalNumber:h({matchPattern:c,parsePattern:m,valueCallback:function B(C){return parseInt(C,10)}}),era:Q({matchPatterns:y,defaultMatchWidth:"wide",parsePatterns:g,defaultParseWidth:"any"}),quarter:Q({matchPatterns:p,defaultMatchWidth:"wide",parsePatterns:d,defaultParseWidth:"any",valueCallback:function B(C){return C+1}}),month:Q({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any"}),day:Q({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"wide"}),dayPeriod:Q({matchPatterns:o,defaultMatchWidth:"any",parsePatterns:r,defaultParseWidth:"any"})},a={code:"hy",formatDistance:D,formatLong:S,formatRelative:V,localize:k,match:e,options:{weekStartsOn:1,firstWeekContainsDate:1}};window.dateFns=x(x({},window.dateFns),{},{locale:x(x({},(J=window.dateFns)===null||J===void 0?void 0:J.locale),{},{hy:a})})})();

//# debugId=07D6D4B855EE005564756e2164756e21
