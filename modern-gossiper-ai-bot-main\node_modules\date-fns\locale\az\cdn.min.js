var A=function(H){return A=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(G){return typeof G}:function(G){return G&&typeof Symbol=="function"&&G.constructor===Symbol&&G!==Symbol.prototype?"symbol":typeof G},A(H)},D=function(H,G){var J=Object.keys(H);if(Object.getOwnPropertySymbols){var Z=Object.getOwnPropertySymbols(H);G&&(Z=Z.filter(function(N){return Object.getOwnPropertyDescriptor(H,N).enumerable})),J.push.apply(J,Z)}return J},K=function(H){for(var G=1;G<arguments.length;G++){var J=arguments[G]!=null?arguments[G]:{};G%2?D(Object(J),!0).forEach(function(Z){UC(H,Z,J[Z])}):Object.getOwnPropertyDescriptors?Object.defineProperties(H,Object.getOwnPropertyDescriptors(J)):D(Object(J)).forEach(function(Z){Object.defineProperty(H,Z,Object.getOwnPropertyDescriptor(J,Z))})}return H},UC=function(H,G,J){if(G=XC(G),G in H)Object.defineProperty(H,G,{value:J,enumerable:!0,configurable:!0,writable:!0});else H[G]=J;return H},XC=function(H){var G=GC(H,"string");return A(G)=="symbol"?G:String(G)},GC=function(H,G){if(A(H)!="object"||!H)return H;var J=H[Symbol.toPrimitive];if(J!==void 0){var Z=J.call(H,G||"default");if(A(Z)!="object")return Z;throw new TypeError("@@toPrimitive must return a primitive value.")}return(G==="string"?String:Number)(H)};(function(H){var G=Object.defineProperty,J=function C(B,U){for(var X in U)G(B,X,{get:U[X],enumerable:!0,configurable:!0,set:function I(Y){return U[X]=function(){return Y}}})},Z={lessThanXSeconds:{one:"bir saniy\u0259d\u0259n az",other:"{{count}} bir saniy\u0259d\u0259n az"},xSeconds:{one:"1 saniy\u0259",other:"{{count}} saniy\u0259"},halfAMinute:"yar\u0131m d\u0259qiq\u0259",lessThanXMinutes:{one:"bir d\u0259qiq\u0259d\u0259n az",other:"{{count}} bir d\u0259qiq\u0259d\u0259n az"},xMinutes:{one:"bir d\u0259qiq\u0259",other:"{{count}} d\u0259qiq\u0259"},aboutXHours:{one:"t\u0259xmin\u0259n 1 saat",other:"t\u0259xmin\u0259n {{count}} saat"},xHours:{one:"1 saat",other:"{{count}} saat"},xDays:{one:"1 g\xFCn",other:"{{count}} g\xFCn"},aboutXWeeks:{one:"t\u0259xmin\u0259n 1 h\u0259ft\u0259",other:"t\u0259xmin\u0259n {{count}} h\u0259ft\u0259"},xWeeks:{one:"1 h\u0259ft\u0259",other:"{{count}} h\u0259ft\u0259"},aboutXMonths:{one:"t\u0259xmin\u0259n 1 ay",other:"t\u0259xmin\u0259n {{count}} ay"},xMonths:{one:"1 ay",other:"{{count}} ay"},aboutXYears:{one:"t\u0259xmin\u0259n 1 il",other:"t\u0259xmin\u0259n {{count}} il"},xYears:{one:"1 il",other:"{{count}} il"},overXYears:{one:"1 ild\u0259n \xE7ox",other:"{{count}} ild\u0259n \xE7ox"},almostXYears:{one:"dem\u0259k olar ki 1 il",other:"dem\u0259k olar ki {{count}} il"}},N=function C(B,U,X){var I,Y=Z[B];if(typeof Y==="string")I=Y;else if(U===1)I=Y.one;else I=Y.other.replace("{{count}}",String(U));if(X!==null&&X!==void 0&&X.addSuffix)if(X.comparison&&X.comparison>0)return I+" sonra";else return I+" \u0259vv\u0259l";return I};function W(C){return function(){var B=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},U=B.width?String(B.width):C.defaultWidth,X=C.formats[U]||C.formats[C.defaultWidth];return X}}var R={full:"EEEE, do MMMM y 'il'",long:"do MMMM y 'il'",medium:"d MMM y 'il'",short:"dd.MM.yyyy"},S={full:"H:mm:ss zzzz",long:"H:mm:ss z",medium:"H:mm:ss",short:"H:mm"},x={full:"{{date}} {{time}} - 'd\u0259'",long:"{{date}} {{time}} - 'd\u0259'",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},L={date:W({formats:R,defaultWidth:"full"}),time:W({formats:S,defaultWidth:"full"}),dateTime:W({formats:x,defaultWidth:"full"})},V={lastWeek:"'sonuncu' eeee p -'d\u0259'",yesterday:"'d\xFCn\u0259n' p -'d\u0259'",today:"'bug\xFCn' p -'d\u0259'",tomorrow:"'sabah' p -'d\u0259'",nextWeek:"eeee p -'d\u0259'",other:"P"},j=function C(B,U,X,I){return V[B]};function E(C){return function(B,U){var X=U!==null&&U!==void 0&&U.context?String(U.context):"standalone",I;if(X==="formatting"&&C.formattingValues){var Y=C.defaultFormattingWidth||C.defaultWidth,Q=U!==null&&U!==void 0&&U.width?String(U.width):Y;I=C.formattingValues[Q]||C.formattingValues[Y]}else{var T=C.defaultWidth,z=U!==null&&U!==void 0&&U.width?String(U.width):C.defaultWidth;I=C.values[z]||C.values[T]}var q=C.argumentCallback?C.argumentCallback(B):B;return I[q]}}var v={narrow:["e.\u0259","b.e"],abbreviated:["e.\u0259","b.e"],wide:["eram\u0131zdan \u0259vv\u0259l","bizim era"]},w={narrow:["1","2","3","4"],abbreviated:["K1","K2","K3","K4"],wide:["1ci kvartal","2ci kvartal","3c\xFC kvartal","4c\xFC kvartal"]},P={narrow:["Y","F","M","A","M","\u0130","\u0130","A","S","O","N","D"],abbreviated:["Yan","Fev","Mar","Apr","May","\u0130yun","\u0130yul","Avq","Sen","Okt","Noy","Dek"],wide:["Yanvar","Fevral","Mart","Aprel","May","\u0130yun","\u0130yul","Avqust","Sentyabr","Oktyabr","Noyabr","Dekabr"]},_={narrow:["B.","B.e","\xC7.a","\xC7.","C.a","C.","\u015E."],short:["B.","B.e","\xC7.a","\xC7.","C.a","C.","\u015E."],abbreviated:["Baz","Baz.e","\xC7\u0259r.a","\xC7\u0259r","C\xFCm.a","C\xFCm","\u015E\u0259"],wide:["Bazar","Bazar ert\u0259si","\xC7\u0259r\u015F\u0259nb\u0259 ax\u015Fam\u0131","\xC7\u0259r\u015F\u0259nb\u0259","C\xFCm\u0259 ax\u015Fam\u0131","C\xFCm\u0259","\u015E\u0259nb\u0259"]},F={narrow:{am:"am",pm:"pm",midnight:"gec\u0259yar\u0131",noon:"g\xFCn",morning:"s\u0259h\u0259r",afternoon:"g\xFCnd\xFCz",evening:"ax\u015Fam",night:"gec\u0259"},abbreviated:{am:"AM",pm:"PM",midnight:"gec\u0259yar\u0131",noon:"g\xFCn",morning:"s\u0259h\u0259r",afternoon:"g\xFCnd\xFCz",evening:"ax\u015Fam",night:"gec\u0259"},wide:{am:"a.m.",pm:"p.m.",midnight:"gec\u0259yar\u0131",noon:"g\xFCn",morning:"s\u0259h\u0259r",afternoon:"g\xFCnd\xFCz",evening:"ax\u015Fam",night:"gec\u0259"}},f={narrow:{am:"a",pm:"p",midnight:"gec\u0259yar\u0131",noon:"g\xFCn",morning:"s\u0259h\u0259r",afternoon:"g\xFCnd\xFCz",evening:"ax\u015Fam",night:"gec\u0259"},abbreviated:{am:"AM",pm:"PM",midnight:"gec\u0259yar\u0131",noon:"g\xFCn",morning:"s\u0259h\u0259r",afternoon:"g\xFCnd\xFCz",evening:"ax\u015Fam",night:"gec\u0259"},wide:{am:"a.m.",pm:"p.m.",midnight:"gec\u0259yar\u0131",noon:"g\xFCn",morning:"s\u0259h\u0259r",afternoon:"g\xFCnd\xFCz",evening:"ax\u015Fam",night:"gec\u0259"}},O={1:"-inci",5:"-inci",8:"-inci",70:"-inci",80:"-inci",2:"-nci",7:"-nci",20:"-nci",50:"-nci",3:"-\xFCnc\xFC",4:"-\xFCnc\xFC",100:"-\xFCnc\xFC",6:"-nc\u0131",9:"-uncu",10:"-uncu",30:"-uncu",60:"-\u0131nc\u0131",90:"-\u0131nc\u0131"},h=function C(B){if(B===0)return B+"-\u0131nc\u0131";var U=B%10,X=B%100-U,I=B>=100?100:null;if(O[U])return O[U];else if(O[X])return O[X];else if(I!==null)return O[I];return""},k=function C(B,U){var X=Number(B),I=h(X);return X+I},c={ordinalNumber:k,era:E({values:v,defaultWidth:"wide"}),quarter:E({values:w,defaultWidth:"wide",argumentCallback:function C(B){return B-1}}),month:E({values:P,defaultWidth:"wide"}),day:E({values:_,defaultWidth:"wide"}),dayPeriod:E({values:F,defaultWidth:"wide",formattingValues:f,defaultFormattingWidth:"wide"})};function $(C){return function(B){var U=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=U.width,I=X&&C.matchPatterns[X]||C.matchPatterns[C.defaultMatchWidth],Y=B.match(I);if(!Y)return null;var Q=Y[0],T=X&&C.parsePatterns[X]||C.parsePatterns[C.defaultParseWidth],z=Array.isArray(T)?m(T,function(M){return M.test(Q)}):b(T,function(M){return M.test(Q)}),q;q=C.valueCallback?C.valueCallback(z):z,q=U.valueCallback?U.valueCallback(q):q;var BC=B.slice(Q.length);return{value:q,rest:BC}}}var b=function C(B,U){for(var X in B)if(Object.prototype.hasOwnProperty.call(B,X)&&U(B[X]))return X;return},m=function C(B,U){for(var X=0;X<B.length;X++)if(U(B[X]))return X;return};function y(C){return function(B){var U=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=B.match(C.matchPattern);if(!X)return null;var I=X[0],Y=B.match(C.parsePattern);if(!Y)return null;var Q=C.valueCallback?C.valueCallback(Y[0]):Y[0];Q=U.valueCallback?U.valueCallback(Q):Q;var T=B.slice(I.length);return{value:Q,rest:T}}}var p=/^(\d+)(-?(ci|inci|nci|uncu|üncü|ncı))?/i,g=/\d+/i,d={narrow:/^(b|a)$/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)$/i,wide:/^(bizim eradan əvvəl|bizim era)$/i},u={any:[/^b$/i,/^(a|c)$/i]},l={narrow:/^[1234]$/i,abbreviated:/^K[1234]$/i,wide:/^[1234](ci)? kvartal$/i},i={any:[/1/i,/2/i,/3/i,/4/i]},n={narrow:/^[(?-i)yfmaisond]$/i,abbreviated:/^(Yan|Fev|Mar|Apr|May|İyun|İyul|Avq|Sen|Okt|Noy|Dek)$/i,wide:/^(Yanvar|Fevral|Mart|Aprel|May|İyun|İyul|Avgust|Sentyabr|Oktyabr|Noyabr|Dekabr)$/i},s={narrow:[/^[(?-i)y]$/i,/^[(?-i)f]$/i,/^[(?-i)m]$/i,/^[(?-i)a]$/i,/^[(?-i)m]$/i,/^[(?-i)i]$/i,/^[(?-i)i]$/i,/^[(?-i)a]$/i,/^[(?-i)s]$/i,/^[(?-i)o]$/i,/^[(?-i)n]$/i,/^[(?-i)d]$/i],abbreviated:[/^Yan$/i,/^Fev$/i,/^Mar$/i,/^Apr$/i,/^May$/i,/^İyun$/i,/^İyul$/i,/^Avg$/i,/^Sen$/i,/^Okt$/i,/^Noy$/i,/^Dek$/i],wide:[/^Yanvar$/i,/^Fevral$/i,/^Mart$/i,/^Aprel$/i,/^May$/i,/^İyun$/i,/^İyul$/i,/^Avgust$/i,/^Sentyabr$/i,/^Oktyabr$/i,/^Noyabr$/i,/^Dekabr$/i]},o={narrow:/^(B\.|B\.e|Ç\.a|Ç\.|C\.a|C\.|Ş\.)$/i,short:/^(B\.|B\.e|Ç\.a|Ç\.|C\.a|C\.|Ş\.)$/i,abbreviated:/^(Baz\.e|Çər|Çər\.a|Cüm|Cüm\.a|Şə)$/i,wide:/^(Bazar|Bazar ertəsi|Çərşənbə axşamı|Çərşənbə|Cümə axşamı|Cümə|Şənbə)$/i},r={narrow:[/^B\.$/i,/^B\.e$/i,/^Ç\.a$/i,/^Ç\.$/i,/^C\.a$/i,/^C\.$/i,/^Ş\.$/i],abbreviated:[/^Baz$/i,/^Baz\.e$/i,/^Çər\.a$/i,/^Çər$/i,/^Cüm\.a$/i,/^Cüm$/i,/^Şə$/i],wide:[/^Bazar$/i,/^Bazar ertəsi$/i,/^Çərşənbə axşamı$/i,/^Çərşənbə$/i,/^Cümə axşamı$/i,/^Cümə$/i,/^Şənbə$/i],any:[/^B\.$/i,/^B\.e$/i,/^Ç\.a$/i,/^Ç\.$/i,/^C\.a$/i,/^C\.$/i,/^Ş\.$/i]},a={narrow:/^(a|p|gecəyarı|gün|səhər|gündüz|axşam|gecə)$/i,any:/^(am|pm|a\.m\.|p\.m\.|AM|PM|gecəyarı|gün|səhər|gündüz|axşam|gecə)$/i},e={any:{am:/^a$/i,pm:/^p$/i,midnight:/^gecəyarı$/i,noon:/^gün$/i,morning:/səhər$/i,afternoon:/gündüz$/i,evening:/axşam$/i,night:/gecə$/i}},t={ordinalNumber:y({matchPattern:p,parsePattern:g,valueCallback:function C(B){return parseInt(B,10)}}),era:$({matchPatterns:d,defaultMatchWidth:"wide",parsePatterns:u,defaultParseWidth:"any"}),quarter:$({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any",valueCallback:function C(B){return B+1}}),month:$({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"narrow"}),day:$({matchPatterns:o,defaultMatchWidth:"wide",parsePatterns:r,defaultParseWidth:"any"}),dayPeriod:$({matchPatterns:a,defaultMatchWidth:"any",parsePatterns:e,defaultParseWidth:"any"})},CC={code:"az",formatDistance:N,formatLong:L,formatRelative:j,localize:c,match:t,options:{weekStartsOn:1,firstWeekContainsDate:1}};window.dateFns=K(K({},window.dateFns),{},{locale:K(K({},(H=window.dateFns)===null||H===void 0?void 0:H.locale),{},{az:CC})})})();

//# debugId=4063C20B14077B2A64756e2164756e21
