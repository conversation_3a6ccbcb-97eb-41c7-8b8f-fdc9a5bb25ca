{"ADVANCED_SETTINGS": "高度な設定", "API_KEY": "APIキー", "AUTOMATIC_MODE": "自動モード", "AUTOMATIC_MODE_DESCRIPTION": "（デフォルト）：エージェントはすべてのタスクを自動的に実行します。", "CONTROL_MAXIMUM_OF_TOKENS_DESCRIPTION": "各API呼び出しで使用されるトークンの最大数を制御します（より高い値は詳細な応答を生成しますが、コストがかかります）。", "CONTROL_THE_MAXIMUM_NUM_OF_LOOPS": "エージェントが実行するループの最大数を制御します（高い値はより多くのAPI呼び出しを生成します）。", "GET_YOUR_OWN_APIKEY": "自分自身のOpenAI APIキーを取得してください", "HERE": "こちら", "HERE_YOU_CAN_ADD_YOUR_OPENAI_API_KEY": "ここでOpenAI APIキーを追加できます。これにより、独自のOpenAIトークンを使用するために支払いをする必要がありますが、ChatGPTにより大きなアクセス権が与えられます！また、OpenAIが提供する任意のモデルを選択できます。", "HIGHER_VALUES_MAKE_OUTPUT_MORE_RANDOM": "より高い値は出力をよりランダムにしますが、より低い値はより焦点を絞り、定義されたものにします。", "INFO_TO_USE_GPT4": "GPT-4モデルを使用するには、APIキーの指定が必要です。取得できます", "INVALID_OPENAI_API_KEY": "APIキーが無効です！", "LABEL_MODE": "モード", "LABEL_MODEL": "モデル", "LANG": "言語", "LINK": "APIキーの申請", "LOOP": "ループ", "MUST_CONNECT_CREDIT_CARD": "注意: クレジットカードをアカウントに接続する必要があります", "NOTE_API_KEY_USAGE": "このキーは現在のブラウザセッションでのみ使用されます。", "NOTE_TO_GET_OPENAI_KEY": "注意：APIキーを取得するには、次のリンクでOpenAIアカウントに登録する必要があります。", "PAUSE": "一時停止", "PAUSE_MODE": "一時停止モード", "PAUSE_MODE_DESCRIPTION": "エージェントは、各タスクセットの後に一時停止します。", "PENAI_API_KEY": "OpenAI API キーが無効です", "PLAY": "再生", "SETTINGS_DIALOG_HEADER": "設定 ⚙", "SUBSCRIPTION_WILL_NOT_WORK": "（ChatGPT Plusのサブスクリプションは機能しません）", "TEMPERATURE": "温度", "TOKENS": "トークン"}