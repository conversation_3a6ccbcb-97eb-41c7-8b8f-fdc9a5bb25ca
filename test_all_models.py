#!/usr/bin/env python3
"""
Test all available Ollama models for functionality and performance
"""

import requests
import time
import json

def test_ollama_models():
    models_to_test = [
        'deepseek-r1:latest',
        'deepseek-r1:14b', 
        'llama3.2:latest',
        'qwen2.5vl:32b',
        'hermes3:8b',
        'nemotron-mini:4b'
    ]

    print('🧪 Testing Ollama Models...')
    results = {}

    for model in models_to_test:
        print(f'\n🔍 Testing {model}...')
        try:
            start_time = time.time()
            response = requests.post('http://localhost:11434/api/generate', 
                json={
                    'model': model,
                    'prompt': 'Hello, respond with just "Working" if you understand.',
                    'stream': False
                }, 
                timeout=30
            )
            duration = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                output = data.get('response', '').strip()
                print(f'✅ {model}: {output} ({duration:.2f}s)')
                results[model] = {
                    'status': 'working', 
                    'response_time': duration, 
                    'output': output
                }
            else:
                print(f'❌ {model}: HTTP {response.status_code}')
                results[model] = {
                    'status': 'http_error', 
                    'code': response.status_code
                }
                
        except requests.exceptions.Timeout:
            print(f'⏰ {model}: Timeout (>30s)')
            results[model] = {'status': 'timeout'}
        except Exception as e:
            print(f'💥 {model}: Error - {str(e)}')
            results[model] = {'status': 'error', 'error': str(e)}

    print('\n📊 SUMMARY:')
    working_models = []
    failed_models = []
    
    for model, result in results.items():
        status = result['status']
        if status == 'working':
            time_str = f"{result['response_time']:.2f}s"
            print(f'✅ {model}: {time_str}')
            working_models.append(model)
        else:
            print(f'❌ {model}: {status}')
            failed_models.append(model)
    
    print(f'\n🎯 Results: {len(working_models)} working, {len(failed_models)} failed')
    
    # Save results to file
    with open('model_test_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    return results

if __name__ == '__main__':
    test_ollama_models()
