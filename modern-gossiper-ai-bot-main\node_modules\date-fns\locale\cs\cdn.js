function _typeof(o) {"@babel/helpers - typeof";return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) {return typeof o;} : function (o) {return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o;}, _typeof(o);}function ownKeys(e, r) {var t = Object.keys(e);if (Object.getOwnPropertySymbols) {var o = Object.getOwnPropertySymbols(e);r && (o = o.filter(function (r) {return Object.getOwnPropertyDescriptor(e, r).enumerable;})), t.push.apply(t, o);}return t;}function _objectSpread(e) {for (var r = 1; r < arguments.length; r++) {var t = null != arguments[r] ? arguments[r] : {};r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {_defineProperty(e, r, t[r]);}) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));});}return e;}function _defineProperty(obj, key, value) {key = _toPropertyKey(key);if (key in obj) {Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true });} else {obj[key] = value;}return obj;}function _toPropertyKey(t) {var i = _toPrimitive(t, "string");return "symbol" == _typeof(i) ? i : String(i);}function _toPrimitive(t, r) {if ("object" != _typeof(t) || !t) return t;var e = t[Symbol.toPrimitive];if (void 0 !== e) {var i = e.call(t, r || "default");if ("object" != _typeof(i)) return i;throw new TypeError("@@toPrimitive must return a primitive value.");}return ("string" === r ? String : Number)(t);}(function (_window$dateFns) {var __defProp = Object.defineProperty;
  var __export = function __export(target, all) {
    for (var name in all)
    __defProp(target, name, {
      get: all[name],
      enumerable: true,
      configurable: true,
      set: function set(newValue) {return all[name] = function () {return newValue;};}
    });
  };

  // lib/locale/cs/_lib/formatDistance.mjs
  var formatDistanceLocale = {
    lessThanXSeconds: {
      one: {
        regular: "m\xE9n\u011B ne\u017E 1 sekunda",
        past: "p\u0159ed m\xE9n\u011B ne\u017E 1 sekundou",
        future: "za m\xE9n\u011B ne\u017E 1 sekundu"
      },
      few: {
        regular: "m\xE9n\u011B ne\u017E {{count}} sekundy",
        past: "p\u0159ed m\xE9n\u011B ne\u017E {{count}} sekundami",
        future: "za m\xE9n\u011B ne\u017E {{count}} sekundy"
      },
      many: {
        regular: "m\xE9n\u011B ne\u017E {{count}} sekund",
        past: "p\u0159ed m\xE9n\u011B ne\u017E {{count}} sekundami",
        future: "za m\xE9n\u011B ne\u017E {{count}} sekund"
      }
    },
    xSeconds: {
      one: {
        regular: "1 sekunda",
        past: "p\u0159ed 1 sekundou",
        future: "za 1 sekundu"
      },
      few: {
        regular: "{{count}} sekundy",
        past: "p\u0159ed {{count}} sekundami",
        future: "za {{count}} sekundy"
      },
      many: {
        regular: "{{count}} sekund",
        past: "p\u0159ed {{count}} sekundami",
        future: "za {{count}} sekund"
      }
    },
    halfAMinute: {
      type: "other",
      other: {
        regular: "p\u016Fl minuty",
        past: "p\u0159ed p\u016Fl minutou",
        future: "za p\u016Fl minuty"
      }
    },
    lessThanXMinutes: {
      one: {
        regular: "m\xE9n\u011B ne\u017E 1 minuta",
        past: "p\u0159ed m\xE9n\u011B ne\u017E 1 minutou",
        future: "za m\xE9n\u011B ne\u017E 1 minutu"
      },
      few: {
        regular: "m\xE9n\u011B ne\u017E {{count}} minuty",
        past: "p\u0159ed m\xE9n\u011B ne\u017E {{count}} minutami",
        future: "za m\xE9n\u011B ne\u017E {{count}} minuty"
      },
      many: {
        regular: "m\xE9n\u011B ne\u017E {{count}} minut",
        past: "p\u0159ed m\xE9n\u011B ne\u017E {{count}} minutami",
        future: "za m\xE9n\u011B ne\u017E {{count}} minut"
      }
    },
    xMinutes: {
      one: {
        regular: "1 minuta",
        past: "p\u0159ed 1 minutou",
        future: "za 1 minutu"
      },
      few: {
        regular: "{{count}} minuty",
        past: "p\u0159ed {{count}} minutami",
        future: "za {{count}} minuty"
      },
      many: {
        regular: "{{count}} minut",
        past: "p\u0159ed {{count}} minutami",
        future: "za {{count}} minut"
      }
    },
    aboutXHours: {
      one: {
        regular: "p\u0159ibli\u017En\u011B 1 hodina",
        past: "p\u0159ibli\u017En\u011B p\u0159ed 1 hodinou",
        future: "p\u0159ibli\u017En\u011B za 1 hodinu"
      },
      few: {
        regular: "p\u0159ibli\u017En\u011B {{count}} hodiny",
        past: "p\u0159ibli\u017En\u011B p\u0159ed {{count}} hodinami",
        future: "p\u0159ibli\u017En\u011B za {{count}} hodiny"
      },
      many: {
        regular: "p\u0159ibli\u017En\u011B {{count}} hodin",
        past: "p\u0159ibli\u017En\u011B p\u0159ed {{count}} hodinami",
        future: "p\u0159ibli\u017En\u011B za {{count}} hodin"
      }
    },
    xHours: {
      one: {
        regular: "1 hodina",
        past: "p\u0159ed 1 hodinou",
        future: "za 1 hodinu"
      },
      few: {
        regular: "{{count}} hodiny",
        past: "p\u0159ed {{count}} hodinami",
        future: "za {{count}} hodiny"
      },
      many: {
        regular: "{{count}} hodin",
        past: "p\u0159ed {{count}} hodinami",
        future: "za {{count}} hodin"
      }
    },
    xDays: {
      one: {
        regular: "1 den",
        past: "p\u0159ed 1 dnem",
        future: "za 1 den"
      },
      few: {
        regular: "{{count}} dny",
        past: "p\u0159ed {{count}} dny",
        future: "za {{count}} dny"
      },
      many: {
        regular: "{{count}} dn\xED",
        past: "p\u0159ed {{count}} dny",
        future: "za {{count}} dn\xED"
      }
    },
    aboutXWeeks: {
      one: {
        regular: "p\u0159ibli\u017En\u011B 1 t\xFDden",
        past: "p\u0159ibli\u017En\u011B p\u0159ed 1 t\xFDdnem",
        future: "p\u0159ibli\u017En\u011B za 1 t\xFDden"
      },
      few: {
        regular: "p\u0159ibli\u017En\u011B {{count}} t\xFDdny",
        past: "p\u0159ibli\u017En\u011B p\u0159ed {{count}} t\xFDdny",
        future: "p\u0159ibli\u017En\u011B za {{count}} t\xFDdny"
      },
      many: {
        regular: "p\u0159ibli\u017En\u011B {{count}} t\xFDdn\u016F",
        past: "p\u0159ibli\u017En\u011B p\u0159ed {{count}} t\xFDdny",
        future: "p\u0159ibli\u017En\u011B za {{count}} t\xFDdn\u016F"
      }
    },
    xWeeks: {
      one: {
        regular: "1 t\xFDden",
        past: "p\u0159ed 1 t\xFDdnem",
        future: "za 1 t\xFDden"
      },
      few: {
        regular: "{{count}} t\xFDdny",
        past: "p\u0159ed {{count}} t\xFDdny",
        future: "za {{count}} t\xFDdny"
      },
      many: {
        regular: "{{count}} t\xFDdn\u016F",
        past: "p\u0159ed {{count}} t\xFDdny",
        future: "za {{count}} t\xFDdn\u016F"
      }
    },
    aboutXMonths: {
      one: {
        regular: "p\u0159ibli\u017En\u011B 1 m\u011Bs\xEDc",
        past: "p\u0159ibli\u017En\u011B p\u0159ed 1 m\u011Bs\xEDcem",
        future: "p\u0159ibli\u017En\u011B za 1 m\u011Bs\xEDc"
      },
      few: {
        regular: "p\u0159ibli\u017En\u011B {{count}} m\u011Bs\xEDce",
        past: "p\u0159ibli\u017En\u011B p\u0159ed {{count}} m\u011Bs\xEDci",
        future: "p\u0159ibli\u017En\u011B za {{count}} m\u011Bs\xEDce"
      },
      many: {
        regular: "p\u0159ibli\u017En\u011B {{count}} m\u011Bs\xEDc\u016F",
        past: "p\u0159ibli\u017En\u011B p\u0159ed {{count}} m\u011Bs\xEDci",
        future: "p\u0159ibli\u017En\u011B za {{count}} m\u011Bs\xEDc\u016F"
      }
    },
    xMonths: {
      one: {
        regular: "1 m\u011Bs\xEDc",
        past: "p\u0159ed 1 m\u011Bs\xEDcem",
        future: "za 1 m\u011Bs\xEDc"
      },
      few: {
        regular: "{{count}} m\u011Bs\xEDce",
        past: "p\u0159ed {{count}} m\u011Bs\xEDci",
        future: "za {{count}} m\u011Bs\xEDce"
      },
      many: {
        regular: "{{count}} m\u011Bs\xEDc\u016F",
        past: "p\u0159ed {{count}} m\u011Bs\xEDci",
        future: "za {{count}} m\u011Bs\xEDc\u016F"
      }
    },
    aboutXYears: {
      one: {
        regular: "p\u0159ibli\u017En\u011B 1 rok",
        past: "p\u0159ibli\u017En\u011B p\u0159ed 1 rokem",
        future: "p\u0159ibli\u017En\u011B za 1 rok"
      },
      few: {
        regular: "p\u0159ibli\u017En\u011B {{count}} roky",
        past: "p\u0159ibli\u017En\u011B p\u0159ed {{count}} roky",
        future: "p\u0159ibli\u017En\u011B za {{count}} roky"
      },
      many: {
        regular: "p\u0159ibli\u017En\u011B {{count}} rok\u016F",
        past: "p\u0159ibli\u017En\u011B p\u0159ed {{count}} roky",
        future: "p\u0159ibli\u017En\u011B za {{count}} rok\u016F"
      }
    },
    xYears: {
      one: {
        regular: "1 rok",
        past: "p\u0159ed 1 rokem",
        future: "za 1 rok"
      },
      few: {
        regular: "{{count}} roky",
        past: "p\u0159ed {{count}} roky",
        future: "za {{count}} roky"
      },
      many: {
        regular: "{{count}} rok\u016F",
        past: "p\u0159ed {{count}} roky",
        future: "za {{count}} rok\u016F"
      }
    },
    overXYears: {
      one: {
        regular: "v\xEDce ne\u017E 1 rok",
        past: "p\u0159ed v\xEDce ne\u017E 1 rokem",
        future: "za v\xEDce ne\u017E 1 rok"
      },
      few: {
        regular: "v\xEDce ne\u017E {{count}} roky",
        past: "p\u0159ed v\xEDce ne\u017E {{count}} roky",
        future: "za v\xEDce ne\u017E {{count}} roky"
      },
      many: {
        regular: "v\xEDce ne\u017E {{count}} rok\u016F",
        past: "p\u0159ed v\xEDce ne\u017E {{count}} roky",
        future: "za v\xEDce ne\u017E {{count}} rok\u016F"
      }
    },
    almostXYears: {
      one: {
        regular: "skoro 1 rok",
        past: "skoro p\u0159ed 1 rokem",
        future: "skoro za 1 rok"
      },
      few: {
        regular: "skoro {{count}} roky",
        past: "skoro p\u0159ed {{count}} roky",
        future: "skoro za {{count}} roky"
      },
      many: {
        regular: "skoro {{count}} rok\u016F",
        past: "skoro p\u0159ed {{count}} roky",
        future: "skoro za {{count}} rok\u016F"
      }
    }
  };
  var formatDistance = function formatDistance(token, count, options) {
    var pluralResult;
    var tokenValue = formatDistanceLocale[token];
    if (tokenValue.type === "other") {
      pluralResult = tokenValue.other;
    } else if (count === 1) {
      pluralResult = tokenValue.one;
    } else if (count > 1 && count < 5) {
      pluralResult = tokenValue.few;
    } else {
      pluralResult = tokenValue.many;
    }
    var suffixExist = (options === null || options === void 0 ? void 0 : options.addSuffix) === true;
    var comparison = options === null || options === void 0 ? void 0 : options.comparison;
    var timeResult;
    if (suffixExist && comparison === -1) {
      timeResult = pluralResult.past;
    } else if (suffixExist && comparison === 1) {
      timeResult = pluralResult.future;
    } else {
      timeResult = pluralResult.regular;
    }
    return timeResult.replace("{{count}}", String(count));
  };

  // lib/locale/_lib/buildFormatLongFn.mjs
  function buildFormatLongFn(args) {
    return function () {var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
      var width = options.width ? String(options.width) : args.defaultWidth;
      var format = args.formats[width] || args.formats[args.defaultWidth];
      return format;
    };
  }

  // lib/locale/cs/_lib/formatLong.mjs
  var dateFormats = {
    full: "EEEE, d. MMMM yyyy",
    long: "d. MMMM yyyy",
    medium: "d. M. yyyy",
    short: "dd.MM.yyyy"
  };
  var timeFormats = {
    full: "H:mm:ss zzzz",
    long: "H:mm:ss z",
    medium: "H:mm:ss",
    short: "H:mm"
  };
  var dateTimeFormats = {
    full: "{{date}} 'v' {{time}}",
    long: "{{date}} 'v' {{time}}",
    medium: "{{date}}, {{time}}",
    short: "{{date}}, {{time}}"
  };
  var formatLong = {
    date: buildFormatLongFn({
      formats: dateFormats,
      defaultWidth: "full"
    }),
    time: buildFormatLongFn({
      formats: timeFormats,
      defaultWidth: "full"
    }),
    dateTime: buildFormatLongFn({
      formats: dateTimeFormats,
      defaultWidth: "full"
    })
  };

  // lib/locale/cs/_lib/formatRelative.mjs
  var accusativeWeekdays = [
  "ned\u011Bli",
  "pond\u011Bl\xED",
  "\xFAter\xFD",
  "st\u0159edu",
  "\u010Dtvrtek",
  "p\xE1tek",
  "sobotu"];

  var formatRelativeLocale = {
    lastWeek: "'posledn\xED' eeee 've' p",
    yesterday: "'v\u010Dera v' p",
    today: "'dnes v' p",
    tomorrow: "'z\xEDtra v' p",
    nextWeek: function nextWeek(date) {
      var day = date.getDay();
      return "'v " + accusativeWeekdays[day] + " o' p";
    },
    other: "P"
  };
  var formatRelative = function formatRelative(token, date) {
    var format = formatRelativeLocale[token];
    if (typeof format === "function") {
      return format(date);
    }
    return format;
  };

  // lib/locale/_lib/buildLocalizeFn.mjs
  function buildLocalizeFn(args) {
    return function (value, options) {
      var context = options !== null && options !== void 0 && options.context ? String(options.context) : "standalone";
      var valuesArray;
      if (context === "formatting" && args.formattingValues) {
        var defaultWidth = args.defaultFormattingWidth || args.defaultWidth;
        var width = options !== null && options !== void 0 && options.width ? String(options.width) : defaultWidth;
        valuesArray = args.formattingValues[width] || args.formattingValues[defaultWidth];
      } else {
        var _defaultWidth = args.defaultWidth;
        var _width = options !== null && options !== void 0 && options.width ? String(options.width) : args.defaultWidth;
        valuesArray = args.values[_width] || args.values[_defaultWidth];
      }
      var index = args.argumentCallback ? args.argumentCallback(value) : value;
      return valuesArray[index];
    };
  }

  // lib/locale/cs/_lib/localize.mjs
  var eraValues = {
    narrow: ["p\u0159. n. l.", "n. l."],
    abbreviated: ["p\u0159. n. l.", "n. l."],
    wide: ["p\u0159ed na\u0161\xEDm letopo\u010Dtem", "na\u0161eho letopo\u010Dtu"]
  };
  var quarterValues = {
    narrow: ["1", "2", "3", "4"],
    abbreviated: ["1. \u010Dtvrtlet\xED", "2. \u010Dtvrtlet\xED", "3. \u010Dtvrtlet\xED", "4. \u010Dtvrtlet\xED"],
    wide: ["1. \u010Dtvrtlet\xED", "2. \u010Dtvrtlet\xED", "3. \u010Dtvrtlet\xED", "4. \u010Dtvrtlet\xED"]
  };
  var monthValues = {
    narrow: ["L", "\xDA", "B", "D", "K", "\u010C", "\u010C", "S", "Z", "\u0158", "L", "P"],
    abbreviated: [
    "led",
    "\xFAno",
    "b\u0159e",
    "dub",
    "kv\u011B",
    "\u010Dvn",
    "\u010Dvc",
    "srp",
    "z\xE1\u0159",
    "\u0159\xEDj",
    "lis",
    "pro"],

    wide: [
    "leden",
    "\xFAnor",
    "b\u0159ezen",
    "duben",
    "kv\u011Bten",
    "\u010Derven",
    "\u010Dervenec",
    "srpen",
    "z\xE1\u0159\xED",
    "\u0159\xEDjen",
    "listopad",
    "prosinec"]

  };
  var formattingMonthValues = {
    narrow: ["L", "\xDA", "B", "D", "K", "\u010C", "\u010C", "S", "Z", "\u0158", "L", "P"],
    abbreviated: [
    "led",
    "\xFAno",
    "b\u0159e",
    "dub",
    "kv\u011B",
    "\u010Dvn",
    "\u010Dvc",
    "srp",
    "z\xE1\u0159",
    "\u0159\xEDj",
    "lis",
    "pro"],

    wide: [
    "ledna",
    "\xFAnora",
    "b\u0159ezna",
    "dubna",
    "kv\u011Btna",
    "\u010Dervna",
    "\u010Dervence",
    "srpna",
    "z\xE1\u0159\xED",
    "\u0159\xEDjna",
    "listopadu",
    "prosince"]

  };
  var dayValues = {
    narrow: ["ne", "po", "\xFAt", "st", "\u010Dt", "p\xE1", "so"],
    short: ["ne", "po", "\xFAt", "st", "\u010Dt", "p\xE1", "so"],
    abbreviated: ["ned", "pon", "\xFAte", "st\u0159", "\u010Dtv", "p\xE1t", "sob"],
    wide: ["ned\u011Ble", "pond\u011Bl\xED", "\xFAter\xFD", "st\u0159eda", "\u010Dtvrtek", "p\xE1tek", "sobota"]
  };
  var dayPeriodValues = {
    narrow: {
      am: "dop.",
      pm: "odp.",
      midnight: "p\u016Flnoc",
      noon: "poledne",
      morning: "r\xE1no",
      afternoon: "odpoledne",
      evening: "ve\u010Der",
      night: "noc"
    },
    abbreviated: {
      am: "dop.",
      pm: "odp.",
      midnight: "p\u016Flnoc",
      noon: "poledne",
      morning: "r\xE1no",
      afternoon: "odpoledne",
      evening: "ve\u010Der",
      night: "noc"
    },
    wide: {
      am: "dopoledne",
      pm: "odpoledne",
      midnight: "p\u016Flnoc",
      noon: "poledne",
      morning: "r\xE1no",
      afternoon: "odpoledne",
      evening: "ve\u010Der",
      night: "noc"
    }
  };
  var formattingDayPeriodValues = {
    narrow: {
      am: "dop.",
      pm: "odp.",
      midnight: "p\u016Flnoc",
      noon: "poledne",
      morning: "r\xE1no",
      afternoon: "odpoledne",
      evening: "ve\u010Der",
      night: "noc"
    },
    abbreviated: {
      am: "dop.",
      pm: "odp.",
      midnight: "p\u016Flnoc",
      noon: "poledne",
      morning: "r\xE1no",
      afternoon: "odpoledne",
      evening: "ve\u010Der",
      night: "noc"
    },
    wide: {
      am: "dopoledne",
      pm: "odpoledne",
      midnight: "p\u016Flnoc",
      noon: "poledne",
      morning: "r\xE1no",
      afternoon: "odpoledne",
      evening: "ve\u010Der",
      night: "noc"
    }
  };
  var ordinalNumber = function ordinalNumber(dirtyNumber, _options) {
    var number = Number(dirtyNumber);
    return number + ".";
  };
  var localize = {
    ordinalNumber: ordinalNumber,
    era: buildLocalizeFn({
      values: eraValues,
      defaultWidth: "wide"
    }),
    quarter: buildLocalizeFn({
      values: quarterValues,
      defaultWidth: "wide",
      argumentCallback: function argumentCallback(quarter) {return quarter - 1;}
    }),
    month: buildLocalizeFn({
      values: monthValues,
      defaultWidth: "wide",
      formattingValues: formattingMonthValues,
      defaultFormattingWidth: "wide"
    }),
    day: buildLocalizeFn({
      values: dayValues,
      defaultWidth: "wide"
    }),
    dayPeriod: buildLocalizeFn({
      values: dayPeriodValues,
      defaultWidth: "wide",
      formattingValues: formattingDayPeriodValues,
      defaultFormattingWidth: "wide"
    })
  };

  // lib/locale/_lib/buildMatchFn.mjs
  function buildMatchFn(args) {
    return function (string) {var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
      var width = options.width;
      var matchPattern = width && args.matchPatterns[width] || args.matchPatterns[args.defaultMatchWidth];
      var matchResult = string.match(matchPattern);
      if (!matchResult) {
        return null;
      }
      var matchedString = matchResult[0];
      var parsePatterns = width && args.parsePatterns[width] || args.parsePatterns[args.defaultParseWidth];
      var key = Array.isArray(parsePatterns) ? findIndex(parsePatterns, function (pattern) {return pattern.test(matchedString);}) : findKey(parsePatterns, function (pattern) {return pattern.test(matchedString);});
      var value;
      value = args.valueCallback ? args.valueCallback(key) : key;
      value = options.valueCallback ? options.valueCallback(value) : value;
      var rest = string.slice(matchedString.length);
      return { value: value, rest: rest };
    };
  }
  var findKey = function findKey(object, predicate) {
    for (var key in object) {
      if (Object.prototype.hasOwnProperty.call(object, key) && predicate(object[key])) {
        return key;
      }
    }
    return;
  };
  var findIndex = function findIndex(array, predicate) {
    for (var key = 0; key < array.length; key++) {
      if (predicate(array[key])) {
        return key;
      }
    }
    return;
  };

  // lib/locale/_lib/buildMatchPatternFn.mjs
  function buildMatchPatternFn(args) {
    return function (string) {var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
      var matchResult = string.match(args.matchPattern);
      if (!matchResult)
      return null;
      var matchedString = matchResult[0];
      var parseResult = string.match(args.parsePattern);
      if (!parseResult)
      return null;
      var value = args.valueCallback ? args.valueCallback(parseResult[0]) : parseResult[0];
      value = options.valueCallback ? options.valueCallback(value) : value;
      var rest = string.slice(matchedString.length);
      return { value: value, rest: rest };
    };
  }

  // lib/locale/cs/_lib/match.mjs
  var matchOrdinalNumberPattern = /^(\d+)\.?/i;
  var parseOrdinalNumberPattern = /\d+/i;
  var matchEraPatterns = {
    narrow: /^(p[řr](\.|ed) Kr\.|p[řr](\.|ed) n\. l\.|po Kr\.|n\. l\.)/i,
    abbreviated: /^(p[řr](\.|ed) Kr\.|p[řr](\.|ed) n\. l\.|po Kr\.|n\. l\.)/i,
    wide: /^(p[řr](\.|ed) Kristem|p[řr](\.|ed) na[šs][íi]m letopo[čc]tem|po Kristu|na[šs]eho letopo[čc]tu)/i
  };
  var parseEraPatterns = {
    any: [/^p[řr]/i, /^(po|n)/i]
  };
  var matchQuarterPatterns = {
    narrow: /^[1234]/i,
    abbreviated: /^[1234]\. [čc]tvrtlet[íi]/i,
    wide: /^[1234]\. [čc]tvrtlet[íi]/i
  };
  var parseQuarterPatterns = {
    any: [/1/i, /2/i, /3/i, /4/i]
  };
  var matchMonthPatterns = {
    narrow: /^[lúubdkčcszřrlp]/i,
    abbreviated: /^(led|[úu]no|b[řr]e|dub|kv[ěe]|[čc]vn|[čc]vc|srp|z[áa][řr]|[řr][íi]j|lis|pro)/i,
    wide: /^(leden|ledna|[úu]nora?|b[řr]ezen|b[řr]ezna|duben|dubna|kv[ěe]ten|kv[ěe]tna|[čc]erven(ec|ce)?|[čc]ervna|srpen|srpna|z[áa][řr][íi]|[řr][íi]jen|[řr][íi]jna|listopad(a|u)?|prosinec|prosince)/i
  };
  var parseMonthPatterns = {
    narrow: [
    /^l/i,
    /^[úu]/i,
    /^b/i,
    /^d/i,
    /^k/i,
    /^[čc]/i,
    /^[čc]/i,
    /^s/i,
    /^z/i,
    /^[řr]/i,
    /^l/i,
    /^p/i],

    any: [
    /^led/i,
    /^[úu]n/i,
    /^b[řr]e/i,
    /^dub/i,
    /^kv[ěe]/i,
    /^[čc]vn|[čc]erven(?!\w)|[čc]ervna/i,
    /^[čc]vc|[čc]erven(ec|ce)/i,
    /^srp/i,
    /^z[áa][řr]/i,
    /^[řr][íi]j/i,
    /^lis/i,
    /^pro/i]

  };
  var matchDayPatterns = {
    narrow: /^[npuúsčps]/i,
    short: /^(ne|po|[úu]t|st|[čc]t|p[áa]|so)/i,
    abbreviated: /^(ned|pon|[úu]te|st[rř]|[čc]tv|p[áa]t|sob)/i,
    wide: /^(ned[ěe]le|pond[ěe]l[íi]|[úu]ter[ýy]|st[řr]eda|[čc]tvrtek|p[áa]tek|sobota)/i
  };
  var parseDayPatterns = {
    narrow: [/^n/i, /^p/i, /^[úu]/i, /^s/i, /^[čc]/i, /^p/i, /^s/i],
    any: [/^ne/i, /^po/i, /^[úu]t/i, /^st/i, /^[čc]t/i, /^p[áa]/i, /^so/i]
  };
  var matchDayPeriodPatterns = {
    any: /^dopoledne|dop\.?|odpoledne|odp\.?|p[ůu]lnoc|poledne|r[áa]no|odpoledne|ve[čc]er|(v )?noci?/i
  };
  var parseDayPeriodPatterns = {
    any: {
      am: /^dop/i,
      pm: /^odp/i,
      midnight: /^p[ůu]lnoc/i,
      noon: /^poledne/i,
      morning: /r[áa]no/i,
      afternoon: /odpoledne/i,
      evening: /ve[čc]er/i,
      night: /noc/i
    }
  };
  var match = {
    ordinalNumber: buildMatchPatternFn({
      matchPattern: matchOrdinalNumberPattern,
      parsePattern: parseOrdinalNumberPattern,
      valueCallback: function valueCallback(value) {return parseInt(value, 10);}
    }),
    era: buildMatchFn({
      matchPatterns: matchEraPatterns,
      defaultMatchWidth: "wide",
      parsePatterns: parseEraPatterns,
      defaultParseWidth: "any"
    }),
    quarter: buildMatchFn({
      matchPatterns: matchQuarterPatterns,
      defaultMatchWidth: "wide",
      parsePatterns: parseQuarterPatterns,
      defaultParseWidth: "any",
      valueCallback: function valueCallback(index) {return index + 1;}
    }),
    month: buildMatchFn({
      matchPatterns: matchMonthPatterns,
      defaultMatchWidth: "wide",
      parsePatterns: parseMonthPatterns,
      defaultParseWidth: "any"
    }),
    day: buildMatchFn({
      matchPatterns: matchDayPatterns,
      defaultMatchWidth: "wide",
      parsePatterns: parseDayPatterns,
      defaultParseWidth: "any"
    }),
    dayPeriod: buildMatchFn({
      matchPatterns: matchDayPeriodPatterns,
      defaultMatchWidth: "any",
      parsePatterns: parseDayPeriodPatterns,
      defaultParseWidth: "any"
    })
  };

  // lib/locale/cs.mjs
  var cs = {
    code: "cs",
    formatDistance: formatDistance,
    formatLong: formatLong,
    formatRelative: formatRelative,
    localize: localize,
    match: match,
    options: {
      weekStartsOn: 1,
      firstWeekContainsDate: 4
    }
  };

  // lib/locale/cs/cdn.js
  window.dateFns = _objectSpread(_objectSpread({},
  window.dateFns), {}, {
    locale: _objectSpread(_objectSpread({}, (_window$dateFns =
    window.dateFns) === null || _window$dateFns === void 0 ? void 0 : _window$dateFns.locale), {}, {
      cs: cs }) });



  //# debugId=139EC565817313A564756e2164756e21
})();

//# sourceMappingURL=cdn.js.map