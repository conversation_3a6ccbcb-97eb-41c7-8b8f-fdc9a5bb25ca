#!/usr/bin/env python3
"""
Interactive test interface for the working unified AI system
"""

import asyncio
import aiohttp
import json
import time
import os
from typing import Dict, Any

class SystemTester:
    def __init__(self):
        self.base_url = "http://localhost:9002"
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def test_capability(self, message: str, capability_name: str) -> Dict[str, Any]:
        """Test a specific capability"""
        start_time = time.time()
        
        try:
            async with self.session.post(
                f"{self.base_url}/chat", 
                json={"message": message},
                timeout=60
            ) as response:
                end_time = time.time()
                response_time = end_time - start_time
                
                if response.status == 200:
                    result = await response.json()
                    return {
                        "capability": capability_name,
                        "status": "SUCCESS",
                        "response_time": f"{response_time:.2f}s",
                        "response": result.get("response", ""),
                        "model_used": result.get("model_used", "unknown")
                    }
                else:
                    return {
                        "capability": capability_name,
                        "status": "ERROR",
                        "response_time": f"{response_time:.2f}s",
                        "error": f"HTTP {response.status}",
                        "response": await response.text()
                    }
        except Exception as e:
            end_time = time.time()
            response_time = end_time - start_time
            return {
                "capability": capability_name,
                "status": "ERROR",
                "response_time": f"{response_time:.2f}s",
                "error": str(e)
            }
    
    async def run_interactive_tests(self):
        """Run interactive tests for all working capabilities"""
        
        print("🚀 UNIFIED AI SYSTEM - INTERACTIVE TESTING")
        print("=" * 60)
        print("Testing all WORKING capabilities that you can use right now!")
        print()
        
        # Test cases for working capabilities
        test_cases = [
            {
                "message": "Hello! How are you doing today?",
                "capability": "💬 Fast Chat",
                "description": "Quick conversational AI using llama3.2"
            },
            {
                "message": "write a python function to reverse a string",
                "capability": "🔧 Code Generation", 
                "description": "Fast code generation using hermes3:8b"
            },
            {
                "message": "create a simple calculator in python",
                "capability": "🔧 Advanced Code Generation",
                "description": "Complex code generation"
            },
            {
                "message": "search for recent developments in artificial intelligence",
                "capability": "🔍 Web Research",
                "description": "Real web search with AI synthesis"
            }
        ]
        
        results = []
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"🧪 Test {i}/4: {test_case['capability']}")
            print(f"   Description: {test_case['description']}")
            print(f"   Query: '{test_case['message']}'")
            print("   Testing...", end="", flush=True)
            
            result = await self.test_capability(test_case["message"], test_case["capability"])
            results.append(result)
            
            if result["status"] == "SUCCESS":
                print(f" ✅ {result['response_time']} ({result['model_used']})")
                print(f"   Preview: {result['response'][:100]}...")
            else:
                print(f" ❌ {result['status']} ({result['response_time']})")
                if "error" in result:
                    print(f"   Error: {result['error']}")
            print()
        
        # Summary
        print("=" * 60)
        print("📊 SYSTEM TEST SUMMARY")
        print("=" * 60)
        
        success_count = sum(1 for r in results if r["status"] == "SUCCESS")
        
        for result in results:
            status_icon = "✅" if result["status"] == "SUCCESS" else "❌"
            print(f"{status_icon} {result['capability']}: {result['status']} ({result['response_time']})")
        
        print(f"\n🎯 SUCCESS RATE: {success_count}/4 ({success_count/4*100:.1f}%)")
        
        if success_count >= 3:
            print("\n🎉 SYSTEM IS WORKING EXCELLENTLY!")
            print("You can now use the unified AI system for:")
            for result in results:
                if result["status"] == "SUCCESS":
                    print(f"   ✅ {result['capability']}")
        elif success_count >= 2:
            print("\n⚠️ SYSTEM IS PARTIALLY WORKING")
            print("Working capabilities:")
            for result in results:
                if result["status"] == "SUCCESS":
                    print(f"   ✅ {result['capability']}")
        else:
            print("\n❌ SYSTEM NEEDS MORE WORK")
        
        return results

async def main():
    """Main test function"""
    
    # Check if server is running
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get("http://localhost:9002/health", timeout=5) as response:
                if response.status != 200:
                    print("❌ Unified AI Agent is not running on port 9002")
                    print("Please start it first: python unified_local_agent.py")
                    return
    except Exception as e:
        print("❌ Cannot connect to Unified AI Agent on port 9002")
        print("Please start it first: python unified_local_agent.py")
        return
    
    # Run tests
    async with SystemTester() as tester:
        results = await tester.run_interactive_tests()
    
    # Offer additional tests
    print("\n" + "=" * 60)
    print("🎮 WANT TO TEST MORE CAPABILITIES?")
    print("=" * 60)
    print("The system also supports:")
    print("   🎤 Voice Processing (speech-to-text, text-to-speech)")
    print("   👁️ Image Analysis (computer vision with qwen2.5vl)")
    print("   🧠 Multiple AI Models (6 different models available)")
    print()
    print("To test these, you can:")
    print("   1. Use the web interface: modern-gossiper-ai-bot-main/build/index.html")
    print("   2. Make direct API calls to http://localhost:9002")
    print("   3. Run specific test scripts for voice/vision")

if __name__ == "__main__":
    asyncio.run(main())
