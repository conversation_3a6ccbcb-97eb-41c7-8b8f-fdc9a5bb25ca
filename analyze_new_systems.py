#!/usr/bin/env python3
"""
Analyze the two new AI systems for integration capabilities
"""

import os
import json
from pathlib import Path

def analyze_openhands():
    """Analyze OpenHands system capabilities"""
    print("🔍 Analyzing OpenHands System")
    print("=" * 50)
    
    openhands_path = Path("OpenHands-main")
    
    capabilities = {
        "name": "OpenHands",
        "description": "AI-powered software development agent",
        "type": "development_assistant",
        "capabilities": [],
        "integration_points": [],
        "requirements": [],
        "api_endpoints": []
    }
    
    # Check main components
    if (openhands_path / "openhands").exists():
        print("✅ Core OpenHands module found")
        capabilities["capabilities"].append("code_generation")
        capabilities["capabilities"].append("development_assistance")
        capabilities["capabilities"].append("file_operations")
        
        # Check for specific modules
        openhands_core = openhands_path / "openhands"
        if openhands_core.exists():
            modules = [d.name for d in openhands_core.iterdir() if d.is_dir()]
            print(f"📦 Core modules: {modules[:10]}...")  # Show first 10
            
            if "agents" in modules:
                capabilities["capabilities"].append("autonomous_agents")
            if "controller" in modules:
                capabilities["capabilities"].append("task_control")
            if "runtime" in modules:
                capabilities["capabilities"].append("code_execution")
    
    # Check for API/server components
    if (openhands_path / "frontend").exists():
        print("✅ Frontend interface found")
        capabilities["integration_points"].append("web_interface")
    
    if (openhands_path / "docker-compose.yml").exists():
        print("✅ Docker deployment found")
        capabilities["integration_points"].append("docker_deployment")
    
    # Check requirements
    pyproject_file = openhands_path / "pyproject.toml"
    if pyproject_file.exists():
        print("✅ Python project configuration found")
        capabilities["requirements"].append("python_3.12+")
        capabilities["requirements"].append("poetry")
        
        # Try to read some key dependencies
        try:
            with open(pyproject_file, 'r') as f:
                content = f.read()
                if "litellm" in content:
                    capabilities["capabilities"].append("llm_integration")
                if "aiohttp" in content:
                    capabilities["capabilities"].append("async_http")
        except Exception as e:
            print(f"⚠️ Could not read pyproject.toml: {e}")
    
    print(f"🎯 Identified capabilities: {capabilities['capabilities']}")
    print(f"🔗 Integration points: {capabilities['integration_points']}")
    
    return capabilities

def analyze_openvoice():
    """Analyze OpenVoice system capabilities"""
    print("\n🔍 Analyzing OpenVoice System")
    print("=" * 50)
    
    openvoice_path = Path("OpenVoice-main")
    
    capabilities = {
        "name": "OpenVoice",
        "description": "Advanced voice cloning and synthesis system",
        "type": "voice_processing",
        "capabilities": [],
        "integration_points": [],
        "requirements": [],
        "api_endpoints": []
    }
    
    # Check main components
    if (openvoice_path / "openvoice").exists():
        print("✅ Core OpenVoice module found")
        capabilities["capabilities"].append("voice_cloning")
        capabilities["capabilities"].append("voice_synthesis")
        capabilities["capabilities"].append("multilingual_tts")
        
        # Check for specific modules
        openvoice_core = openvoice_path / "openvoice"
        if openvoice_core.exists():
            modules = [f.name for f in openvoice_core.iterdir() if f.is_file() and f.suffix == '.py']
            print(f"📦 Core modules: {modules}")
            
            if any("api" in m for m in modules):
                capabilities["capabilities"].append("api_interface")
            if any("clone" in m for m in modules):
                capabilities["capabilities"].append("voice_cloning")
    
    # Check for demo notebooks
    demo_files = [f for f in openvoice_path.iterdir() if f.name.startswith("demo_")]
    if demo_files:
        print(f"✅ Demo notebooks found: {[f.name for f in demo_files]}")
        capabilities["integration_points"].append("jupyter_notebooks")
        capabilities["capabilities"].append("interactive_demos")
    
    # Check requirements
    req_file = openvoice_path / "requirements.txt"
    if req_file.exists():
        print("✅ Requirements file found")
        try:
            with open(req_file, 'r') as f:
                requirements = f.read().strip().split('\n')
                print(f"📋 Key dependencies: {requirements[:5]}...")
                
                capabilities["requirements"].extend(requirements)
                
                # Identify key capabilities from dependencies
                if any("librosa" in req for req in requirements):
                    capabilities["capabilities"].append("audio_processing")
                if any("whisper" in req for req in requirements):
                    capabilities["capabilities"].append("speech_recognition")
                if any("gradio" in req for req in requirements):
                    capabilities["capabilities"].append("web_interface")
                    capabilities["integration_points"].append("gradio_ui")
                if any("openai" in req for req in requirements):
                    capabilities["capabilities"].append("openai_integration")
        except Exception as e:
            print(f"⚠️ Could not read requirements.txt: {e}")
    
    print(f"🎯 Identified capabilities: {capabilities['capabilities']}")
    print(f"🔗 Integration points: {capabilities['integration_points']}")
    
    return capabilities

def assess_integration_potential(openhands_caps, openvoice_caps):
    """Assess how these systems can integrate with current unified system"""
    print("\n🔗 Integration Assessment")
    print("=" * 50)
    
    current_capabilities = [
        "code_generation", "web_search", "voice_processing", 
        "multimodal_processing", "task_planning", "autonomous_execution"
    ]
    
    print("📊 Current System Capabilities:")
    for cap in current_capabilities:
        print(f"   ✅ {cap}")
    
    print(f"\n🆕 OpenHands New Capabilities:")
    oh_new = [cap for cap in openhands_caps["capabilities"] if cap not in current_capabilities]
    for cap in oh_new:
        print(f"   🆕 {cap}")
    
    print(f"\n🆕 OpenVoice New Capabilities:")
    ov_new = [cap for cap in openvoice_caps["capabilities"] if cap not in current_capabilities]
    for cap in ov_new:
        print(f"   🆕 {cap}")
    
    # Integration strategy
    print(f"\n🎯 Integration Strategy:")
    
    # OpenHands integration
    print(f"📝 OpenHands Integration:")
    print(f"   • Enhance code_generation with advanced development features")
    print(f"   • Add file_operations and development_assistance capabilities")
    print(f"   • Integrate autonomous_agents for complex development tasks")
    print(f"   • Use docker_deployment for containerized services")
    
    # OpenVoice integration  
    print(f"🎤 OpenVoice Integration:")
    print(f"   • Enhance voice_processing with advanced voice cloning")
    print(f"   • Add multilingual_tts for better language support")
    print(f"   • Integrate voice_synthesis for custom voice generation")
    print(f"   • Use gradio_ui for voice interface demos")
    
    # Unified integration approach
    print(f"\n🏗️ Unified Integration Approach:")
    print(f"   1. Extend current unified agent with new capability handlers")
    print(f"   2. Add OpenHands as 'advanced_development' capability")
    print(f"   3. Add OpenVoice as 'advanced_voice' capability")
    print(f"   4. Maintain local-first architecture with Ollama integration")
    print(f"   5. Create unified API endpoints for all 8 capabilities")
    
    return {
        "new_capabilities": oh_new + ov_new,
        "integration_complexity": "medium",
        "estimated_capabilities": len(current_capabilities) + len(oh_new) + len(ov_new)
    }

def main():
    """Analyze new systems and create integration plan"""
    print("🔍 NEW SYSTEMS ANALYSIS")
    print("=" * 70)
    
    # Analyze both systems
    openhands_caps = analyze_openhands()
    openvoice_caps = analyze_openvoice()
    
    # Assess integration potential
    integration_plan = assess_integration_potential(openhands_caps, openvoice_caps)
    
    # Save analysis results
    analysis_results = {
        "openhands": openhands_caps,
        "openvoice": openvoice_caps,
        "integration_plan": integration_plan,
        "current_system_capabilities": 6,
        "projected_system_capabilities": integration_plan["estimated_capabilities"]
    }
    
    with open("new_systems_analysis.json", "w") as f:
        json.dump(analysis_results, f, indent=2)
    
    print(f"\n" + "=" * 70)
    print("📊 ANALYSIS SUMMARY")
    print("=" * 70)
    print(f"🎯 Current System: 6 capabilities")
    print(f"🆕 OpenHands: {len(openhands_caps['capabilities'])} capabilities")
    print(f"🆕 OpenVoice: {len(openvoice_caps['capabilities'])} capabilities")
    print(f"🏆 Projected Total: {integration_plan['estimated_capabilities']} capabilities")
    print(f"📁 Analysis saved to: new_systems_analysis.json")
    
    return analysis_results

if __name__ == "__main__":
    results = main()
