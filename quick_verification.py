#!/usr/bin/env python3
"""
Quick verification that all capabilities are working
"""

import requests
import json

def quick_test():
    """Quick test of all capabilities"""
    base_url = "http://localhost:9001"
    
    print("🚀 Quick System Verification")
    print("=" * 50)
    
    # Test health
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            health = response.json()
            print(f"✅ System Health: {health.get('status', 'unknown')}")
            print(f"🤖 Ollama: {health.get('ollama', 'unknown')}")
            print(f"📊 Models: {health.get('models_loaded', 0)}")
            print(f"🛠️ Capabilities: {health.get('capabilities', 0)}")
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return
    
    # Test system info
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        if response.status_code == 200:
            info = response.json()
            capabilities = info.get('capabilities', [])
            print(f"\n📋 Available Capabilities ({len(capabilities)}):")
            for cap in capabilities:
                print(f"   ✅ {cap}")
        else:
            print(f"❌ System info failed: {response.status_code}")
    except Exception as e:
        print(f"❌ System info error: {e}")
    
    # Quick capability test
    print(f"\n🧪 Quick Capability Test:")
    
    test_cases = [
        ("code_generation", "Write hello world in Python"),
        ("multimodal_processing", "I want to analyze images"),
        ("task_planning", "Plan a simple task"),
        ("autonomous_execution", "Execute autonomously")
    ]
    
    for capability, message in test_cases:
        try:
            payload = {"message": message, "capability": capability}
            response = requests.post(f"{base_url}/chat", json=payload, timeout=15)
            
            if response.status_code == 200:
                result = response.json()
                model = result.get('model_used', 'unknown')
                cap_used = result.get('capability_used', 'unknown')
                print(f"   ✅ {capability}: {model} ({cap_used})")
            else:
                print(f"   ❌ {capability}: HTTP {response.status_code}")
        except Exception as e:
            print(f"   ❌ {capability}: {str(e)[:50]}...")
    
    print(f"\n🎉 Verification Complete!")
    print(f"🏆 Unified Local AI System is operational!")

if __name__ == "__main__":
    quick_test()
