<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Unified AI System - Chat Interface</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            width: 90%;
            max-width: 800px;
            height: 80vh;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .header {
            background: #2c3e50;
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            margin-bottom: 10px;
        }
        
        .status {
            background: #27ae60;
            color: white;
            padding: 5px 15px;
            border-radius: 15px;
            font-size: 12px;
            display: inline-block;
        }
        
        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 20px;
            overflow: hidden;
        }
        
        .messages {
            flex: 1;
            overflow-y: auto;
            margin-bottom: 20px;
            padding: 10px;
            border: 1px solid #eee;
            border-radius: 10px;
            background: #f9f9f9;
        }
        
        .message {
            margin-bottom: 15px;
            padding: 10px 15px;
            border-radius: 15px;
            max-width: 80%;
            word-wrap: break-word;
        }
        
        .user-message {
            background: #007bff;
            color: white;
            margin-left: auto;
            text-align: right;
        }
        
        .ai-message {
            background: #e9ecef;
            color: #333;
            margin-right: auto;
        }
        
        .ai-message .capability {
            font-size: 11px;
            color: #666;
            margin-bottom: 5px;
        }
        
        .input-container {
            display: flex;
            gap: 10px;
        }
        
        .message-input {
            flex: 1;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
        }
        
        .message-input:focus {
            border-color: #007bff;
        }
        
        .send-button {
            padding: 15px 25px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s;
        }
        
        .send-button:hover {
            background: #0056b3;
        }
        
        .send-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .loading {
            display: none;
            text-align: center;
            color: #666;
            font-style: italic;
            margin: 10px 0;
        }
        
        .examples {
            margin-bottom: 20px;
            padding: 15px;
            background: #fff3cd;
            border-radius: 10px;
            border-left: 4px solid #ffc107;
        }
        
        .examples h3 {
            margin-bottom: 10px;
            color: #856404;
        }
        
        .example-button {
            display: inline-block;
            margin: 5px;
            padding: 8px 12px;
            background: #ffc107;
            color: #856404;
            border: none;
            border-radius: 15px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.3s;
        }
        
        .example-button:hover {
            background: #e0a800;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 Unified AI System</h1>
            <p>8 AI Capabilities in One Agent</p>
            <span class="status" id="status">Checking...</span>
        </div>
        
        <div class="chat-container">
            <div class="examples">
                <h3>💡 Try these examples:</h3>
                <button class="example-button" onclick="sendExample('Write a Python hello world function')">💻 Code Generation</button>
                <button class="example-button" onclick="sendExample('Search for Python tutorials')">🔍 Web Search</button>
                <button class="example-button" onclick="sendExample('Plan a software project')">📋 Task Planning</button>
                <button class="example-button" onclick="sendExample('Analyze code quality')">🔧 Advanced Development</button>
                <button class="example-button" onclick="sendExample('Say hello world')">🎤 Voice Processing</button>
            </div>
            
            <div class="messages" id="messages">
                <div class="message ai-message">
                    <div class="capability">System</div>
                    <div>👋 Hello! I'm your unified AI assistant with 8 capabilities. I can help you with coding, web search, voice processing, image analysis, task planning, and more. What would you like to do?</div>
                </div>
            </div>
            
            <div class="loading" id="loading">🤖 AI is thinking...</div>
            
            <div class="input-container">
                <input type="text" class="message-input" id="messageInput" placeholder="Type your message here..." onkeypress="handleKeyPress(event)">
                <button class="send-button" id="sendButton" onclick="sendMessage()">Send</button>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:9001';
        
        // Check system status
        async function checkStatus() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                
                const statusElement = document.getElementById('status');
                if (data.status === 'healthy') {
                    statusElement.textContent = `✅ Online (${data.capabilities} capabilities)`;
                    statusElement.style.background = '#27ae60';
                } else {
                    statusElement.textContent = '⚠️ Issues detected';
                    statusElement.style.background = '#f39c12';
                }
            } catch (error) {
                const statusElement = document.getElementById('status');
                statusElement.textContent = '❌ Offline';
                statusElement.style.background = '#e74c3c';
            }
        }
        
        // Send message
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message) return;
            
            // Add user message to chat
            addMessage(message, 'user');
            input.value = '';
            
            // Show loading
            const loading = document.getElementById('loading');
            const sendButton = document.getElementById('sendButton');
            loading.style.display = 'block';
            sendButton.disabled = true;
            
            try {
                const response = await fetch(`${API_BASE}/chat`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ message: message })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    addMessage(data.response, 'ai', data.capability_used, data.execution_time);
                } else {
                    addMessage(`Error: ${data.error || 'Unknown error'}`, 'ai', 'error');
                }
            } catch (error) {
                addMessage(`Connection error: ${error.message}`, 'ai', 'error');
            } finally {
                loading.style.display = 'none';
                sendButton.disabled = false;
            }
        }
        
        // Add message to chat
        function addMessage(text, sender, capability = null, executionTime = null) {
            const messages = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}-message`;
            
            if (sender === 'ai' && capability) {
                const capabilityDiv = document.createElement('div');
                capabilityDiv.className = 'capability';
                capabilityDiv.textContent = `${capability}${executionTime ? ` (${executionTime.toFixed(1)}s)` : ''}`;
                messageDiv.appendChild(capabilityDiv);
            }
            
            const textDiv = document.createElement('div');
            textDiv.textContent = text;
            messageDiv.appendChild(textDiv);
            
            messages.appendChild(messageDiv);
            messages.scrollTop = messages.scrollHeight;
        }
        
        // Send example message
        function sendExample(message) {
            document.getElementById('messageInput').value = message;
            sendMessage();
        }
        
        // Handle Enter key
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }
        
        // Initialize
        checkStatus();
        setInterval(checkStatus, 30000); // Check status every 30 seconds
    </script>
</body>
</html>
