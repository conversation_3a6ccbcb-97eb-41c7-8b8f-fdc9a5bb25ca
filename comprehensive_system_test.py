#!/usr/bin/env python3
"""
Comprehensive System Test Suite for Unified Local AI Agent
Tests all 6 capabilities and system integration
"""

import requests
import json
import time
import asyncio
from typing import Dict, List, Any
import tempfile
import os
from PIL import Image
import io

class UnifiedSystemTester:
    """Comprehensive tester for the unified local AI system"""
    
    def __init__(self):
        self.base_url = "http://localhost:9001"
        self.test_results = {}
        self.total_tests = 0
        self.passed_tests = 0
        self.failed_tests = 0
        
    def log_test_result(self, test_name: str, success: bool, details: Dict[str, Any]):
        """Log test result"""
        self.total_tests += 1
        if success:
            self.passed_tests += 1
            print(f"✅ {test_name}")
        else:
            self.failed_tests += 1
            print(f"❌ {test_name}")
            
        self.test_results[test_name] = {
            "success": success,
            "details": details
        }
    
    def test_system_health(self):
        """Test basic system health and availability"""
        print("\n🏥 Testing System Health & Availability")
        print("=" * 60)
        
        try:
            # Test health endpoint
            response = requests.get(f"{self.base_url}/health", timeout=10)
            health_success = response.status_code == 200
            
            if health_success:
                health_data = response.json()
                print(f"   Status: {health_data.get('status', 'unknown')}")
                print(f"   Ollama: {health_data.get('ollama', 'unknown')}")
                print(f"   Models: {health_data.get('models_loaded', 0)}")
                print(f"   Capabilities: {health_data.get('capabilities', 0)}")
            
            self.log_test_result("System Health Check", health_success, {
                "status_code": response.status_code,
                "response": response.json() if health_success else response.text
            })
            
            # Test system info
            response = requests.get(f"{self.base_url}/", timeout=10)
            info_success = response.status_code == 200
            
            if info_success:
                info_data = response.json()
                expected_capabilities = 6
                actual_capabilities = len(info_data.get('capabilities', []))
                capabilities_match = actual_capabilities == expected_capabilities
                
                print(f"   Available Capabilities: {actual_capabilities}/{expected_capabilities}")
                for cap in info_data.get('capabilities', []):
                    print(f"     - {cap}")
            
            self.log_test_result("System Information", info_success and capabilities_match, {
                "status_code": response.status_code,
                "capabilities_count": actual_capabilities if info_success else 0,
                "expected_capabilities": expected_capabilities
            })
            
        except Exception as e:
            self.log_test_result("System Health Check", False, {"error": str(e)})
            self.log_test_result("System Information", False, {"error": str(e)})
    
    def test_code_generation(self):
        """Test code generation capability"""
        print("\n💻 Testing Code Generation Capability")
        print("=" * 60)
        
        test_cases = [
            {
                "name": "Python Function",
                "message": "Write a Python function to calculate fibonacci numbers",
                "expected_keywords": ["def", "fibonacci", "return"]
            },
            {
                "name": "JavaScript Code",
                "message": "Create a JavaScript function to sort an array",
                "expected_keywords": ["function", "sort", "array"]
            }
        ]
        
        for test_case in test_cases:
            try:
                payload = {
                    "message": test_case["message"],
                    "capability": "code_generation"
                }
                
                start_time = time.time()
                response = requests.post(f"{self.base_url}/chat", json=payload, timeout=30)
                end_time = time.time()
                
                success = response.status_code == 200
                response_time = end_time - start_time
                
                if success:
                    result = response.json()
                    response_text = result.get('response', '').lower()
                    keywords_found = sum(1 for keyword in test_case["expected_keywords"] 
                                       if keyword.lower() in response_text)
                    keyword_success = keywords_found >= len(test_case["expected_keywords"]) // 2
                    
                    print(f"   {test_case['name']}: {response_time:.2f}s, Keywords: {keywords_found}/{len(test_case['expected_keywords'])}")
                    
                    self.log_test_result(f"Code Generation - {test_case['name']}", 
                                       success and keyword_success, {
                        "response_time": response_time,
                        "keywords_found": keywords_found,
                        "capability_used": result.get('capability_used', 'unknown')
                    })
                else:
                    self.log_test_result(f"Code Generation - {test_case['name']}", False, {
                        "status_code": response.status_code,
                        "error": response.text
                    })
                    
            except Exception as e:
                self.log_test_result(f"Code Generation - {test_case['name']}", False, {"error": str(e)})
    
    def test_web_search(self):
        """Test web search capability"""
        print("\n🌐 Testing Web Search Capability")
        print("=" * 60)
        
        test_cases = [
            {
                "name": "Technology Search",
                "message": "Search for information about Python programming",
                "expected_indicators": ["python", "programming", "source"]
            },
            {
                "name": "General Knowledge",
                "message": "Find information about machine learning",
                "expected_indicators": ["machine learning", "algorithm", "data"]
            }
        ]
        
        for test_case in test_cases:
            try:
                payload = {
                    "message": test_case["message"],
                    "capability": "web_search"
                }
                
                start_time = time.time()
                response = requests.post(f"{self.base_url}/chat", json=payload, timeout=60)
                end_time = time.time()
                
                success = response.status_code == 200
                response_time = end_time - start_time
                
                if success:
                    result = response.json()
                    response_text = result.get('response', '').lower()
                    indicators_found = sum(1 for indicator in test_case["expected_indicators"] 
                                         if indicator.lower() in response_text)
                    
                    print(f"   {test_case['name']}: {response_time:.2f}s, Indicators: {indicators_found}/{len(test_case['expected_indicators'])}")
                    
                    self.log_test_result(f"Web Search - {test_case['name']}", success, {
                        "response_time": response_time,
                        "indicators_found": indicators_found,
                        "capability_used": result.get('capability_used', 'unknown')
                    })
                else:
                    self.log_test_result(f"Web Search - {test_case['name']}", False, {
                        "status_code": response.status_code,
                        "error": response.text
                    })
                    
            except Exception as e:
                self.log_test_result(f"Web Search - {test_case['name']}", False, {"error": str(e)})
    
    def test_voice_processing(self):
        """Test voice processing capability"""
        print("\n🎤 Testing Voice Processing Capability")
        print("=" * 60)
        
        # Test TTS endpoint
        try:
            payload = {"text": "This is a test of the text to speech system"}
            response = requests.post(f"{self.base_url}/tts", json=payload, timeout=30)
            
            tts_success = response.status_code == 200
            if tts_success:
                result = response.json()
                print(f"   TTS Test: Audio file generated at {result.get('audio_file', 'unknown')}")
            
            self.log_test_result("Voice Processing - TTS", tts_success, {
                "status_code": response.status_code,
                "response": result if tts_success else response.text
            })
            
        except Exception as e:
            self.log_test_result("Voice Processing - TTS", False, {"error": str(e)})
        
        # Test voice capability detection
        try:
            payload = {
                "message": "Please speak to me about artificial intelligence",
                "capability": "voice_processing"
            }
            
            start_time = time.time()
            response = requests.post(f"{self.base_url}/chat", json=payload, timeout=30)
            end_time = time.time()
            
            success = response.status_code == 200
            response_time = end_time - start_time
            
            if success:
                result = response.json()
                print(f"   Voice Chat: {response_time:.2f}s, Capability: {result.get('capability_used', 'unknown')}")
            
            self.log_test_result("Voice Processing - Chat", success, {
                "response_time": response_time,
                "capability_used": result.get('capability_used', 'unknown') if success else None
            })
            
        except Exception as e:
            self.log_test_result("Voice Processing - Chat", False, {"error": str(e)})
    
    def test_multimodal_processing(self):
        """Test multimodal processing capability"""
        print("\n🖼️ Testing Multimodal Processing Capability")
        print("=" * 60)
        
        # Test capability detection
        try:
            payload = {
                "message": "I want to analyze an image",
                "capability": "multimodal_processing"
            }
            
            start_time = time.time()
            response = requests.post(f"{self.base_url}/chat", json=payload, timeout=30)
            end_time = time.time()
            
            success = response.status_code == 200
            response_time = end_time - start_time
            
            if success:
                result = response.json()
                response_text = result.get('response', '')
                has_capabilities_info = "multimodal" in response_text.lower() and "image" in response_text.lower()
                
                print(f"   Capability Detection: {response_time:.2f}s, Info Available: {has_capabilities_info}")
            
            self.log_test_result("Multimodal Processing - Detection", success and has_capabilities_info, {
                "response_time": response_time,
                "capability_used": result.get('capability_used', 'unknown') if success else None,
                "has_info": has_capabilities_info if success else False
            })
            
        except Exception as e:
            self.log_test_result("Multimodal Processing - Detection", False, {"error": str(e)})
    
    def test_task_planning(self):
        """Test task planning capability"""
        print("\n📋 Testing Task Planning Capability")
        print("=" * 60)
        
        try:
            payload = {
                "message": "Help me plan a software development project",
                "capability": "task_planning"
            }
            
            start_time = time.time()
            response = requests.post(f"{self.base_url}/chat", json=payload, timeout=30)
            end_time = time.time()
            
            success = response.status_code == 200
            response_time = end_time - start_time
            
            if success:
                result = response.json()
                response_text = result.get('response', '').lower()
                planning_indicators = ["step", "plan", "objective", "priority", "timeline"]
                indicators_found = sum(1 for indicator in planning_indicators 
                                     if indicator in response_text)
                
                print(f"   Task Planning: {response_time:.2f}s, Planning Elements: {indicators_found}/{len(planning_indicators)}")
            
            self.log_test_result("Task Planning", success, {
                "response_time": response_time,
                "planning_indicators": indicators_found if success else 0,
                "capability_used": result.get('capability_used', 'unknown') if success else None
            })
            
        except Exception as e:
            self.log_test_result("Task Planning", False, {"error": str(e)})
    
    def test_autonomous_execution(self):
        """Test autonomous execution capability"""
        print("\n🤖 Testing Autonomous Execution Capability")
        print("=" * 60)
        
        try:
            payload = {
                "message": "Autonomously analyze the current system status and provide recommendations",
                "capability": "autonomous_execution"
            }
            
            start_time = time.time()
            response = requests.post(f"{self.base_url}/chat", json=payload, timeout=30)
            end_time = time.time()
            
            success = response.status_code == 200
            response_time = end_time - start_time
            
            if success:
                result = response.json()
                response_text = result.get('response', '').lower()
                autonomous_indicators = ["analysis", "recommendation", "system", "status", "autonomous"]
                indicators_found = sum(1 for indicator in autonomous_indicators 
                                     if indicator in response_text)
                
                print(f"   Autonomous Execution: {response_time:.2f}s, Autonomous Elements: {indicators_found}/{len(autonomous_indicators)}")
            
            self.log_test_result("Autonomous Execution", success, {
                "response_time": response_time,
                "autonomous_indicators": indicators_found if success else 0,
                "capability_used": result.get('capability_used', 'unknown') if success else None
            })
            
        except Exception as e:
            self.log_test_result("Autonomous Execution", False, {"error": str(e)})
    
    def test_performance_metrics(self):
        """Test system performance metrics"""
        print("\n⚡ Testing Performance Metrics")
        print("=" * 60)
        
        # Calculate average response times from previous tests
        response_times = []
        for test_name, result in self.test_results.items():
            if result["success"] and "response_time" in result["details"]:
                response_times.append(result["details"]["response_time"])
        
        if response_times:
            avg_response_time = sum(response_times) / len(response_times)
            max_response_time = max(response_times)
            min_response_time = min(response_times)
            
            print(f"   Average Response Time: {avg_response_time:.2f}s")
            print(f"   Fastest Response: {min_response_time:.2f}s")
            print(f"   Slowest Response: {max_response_time:.2f}s")
            
            # Performance is good if average is under 30s and max is under 60s
            performance_good = avg_response_time < 30 and max_response_time < 60
            
            self.log_test_result("Performance Metrics", performance_good, {
                "avg_response_time": avg_response_time,
                "max_response_time": max_response_time,
                "min_response_time": min_response_time,
                "total_tests_with_timing": len(response_times)
            })
        else:
            self.log_test_result("Performance Metrics", False, {"error": "No timing data available"})
    
    def run_comprehensive_test(self):
        """Run all tests and generate report"""
        print("🚀 Starting Comprehensive System Test Suite")
        print("=" * 80)
        
        # Run all test categories
        self.test_system_health()
        self.test_code_generation()
        self.test_web_search()
        self.test_voice_processing()
        self.test_multimodal_processing()
        self.test_task_planning()
        self.test_autonomous_execution()
        self.test_performance_metrics()
        
        # Generate final report
        self.generate_final_report()
    
    def generate_final_report(self):
        """Generate comprehensive test report"""
        print("\n" + "=" * 80)
        print("🎯 COMPREHENSIVE TEST RESULTS")
        print("=" * 80)
        
        print(f"📊 **Test Summary:**")
        print(f"   Total Tests: {self.total_tests}")
        print(f"   Passed: {self.passed_tests} ✅")
        print(f"   Failed: {self.failed_tests} ❌")
        print(f"   Success Rate: {(self.passed_tests/self.total_tests*100):.1f}%")
        
        print(f"\n🎯 **Capability Status:**")
        capabilities = {
            "System Health": ["System Health Check", "System Information"],
            "Code Generation": [k for k in self.test_results.keys() if "Code Generation" in k],
            "Web Search": [k for k in self.test_results.keys() if "Web Search" in k],
            "Voice Processing": [k for k in self.test_results.keys() if "Voice Processing" in k],
            "Multimodal Processing": [k for k in self.test_results.keys() if "Multimodal Processing" in k],
            "Task Planning": [k for k in self.test_results.keys() if "Task Planning" in k],
            "Autonomous Execution": [k for k in self.test_results.keys() if "Autonomous Execution" in k],
            "Performance": [k for k in self.test_results.keys() if "Performance" in k]
        }
        
        for capability, tests in capabilities.items():
            if tests:
                passed = sum(1 for test in tests if self.test_results.get(test, {}).get("success", False))
                total = len(tests)
                status = "✅" if passed == total else "⚠️" if passed > 0 else "❌"
                print(f"   {capability}: {passed}/{total} {status}")
        
        print(f"\n🏆 **Overall System Status:**")
        overall_success_rate = (self.passed_tests / self.total_tests) * 100
        
        if overall_success_rate >= 90:
            print("   🎉 EXCELLENT - System fully operational!")
        elif overall_success_rate >= 75:
            print("   ✅ GOOD - System mostly operational with minor issues")
        elif overall_success_rate >= 50:
            print("   ⚠️ FAIR - System partially operational, needs attention")
        else:
            print("   ❌ POOR - System has significant issues")
        
        print(f"\n📋 **Next Steps:**")
        if self.failed_tests > 0:
            print("   - Review failed tests and address issues")
            print("   - Check Ollama model availability")
            print("   - Verify network connectivity for web search")
        else:
            print("   - System is fully operational!")
            print("   - Ready for production use")
            print("   - Consider performance optimization if needed")

def main():
    """Run comprehensive system test"""
    tester = UnifiedSystemTester()
    tester.run_comprehensive_test()

if __name__ == "__main__":
    main()
