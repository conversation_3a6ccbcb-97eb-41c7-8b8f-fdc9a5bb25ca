"use strict";
exports.fr = void 0;
var _index = require("./fr/_lib/formatDistance.js");
var _index2 = require("./fr/_lib/formatLong.js");
var _index3 = require("./fr/_lib/formatRelative.js");
var _index4 = require("./fr/_lib/localize.js");
var _index5 = require("./fr/_lib/match.js");

/**
 * @category Locales
 * @summary French locale.
 * @language French
 * @iso-639-2 fra
 * <AUTHOR> [@izeau](https://github.com/izeau)
 * <AUTHOR> [@fbonzon](https://github.com/fbonzon)
 */
const fr = (exports.fr = {
  code: "fr",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
});
