#!/usr/bin/env python3
"""
Test the multimodal processor directly
"""

import asyncio
import tempfile
import os
from PIL import Image, ImageDraw
from local_multimodal_processor import LocalMultimodalProcessor

async def test_multimodal_processor():
    print("🧪 Testing Multimodal Processor Directly...")
    
    try:
        # Create processor
        processor = LocalMultimodalProcessor()
        
        # Initialize
        print("🔄 Initializing multimodal processor...")
        success = await processor.initialize()
        
        if success:
            print("✅ Multimodal processor initialized successfully!")
            
            # Create a simple test image
            print("\n🖼️ Creating test image...")
            with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp_file:
                # Create a simple image with text
                img = Image.new('RGB', (400, 200), color='white')
                draw = ImageDraw.Draw(img)
                
                # Draw some shapes and text
                draw.rectangle([50, 50, 150, 100], fill='red', outline='black')
                draw.ellipse([200, 50, 300, 150], fill='blue', outline='black')
                draw.text((50, 120), "Hello World!", fill='black')
                
                img.save(tmp_file.name)
                test_image_path = tmp_file.name
            
            print(f"   Test image created: {test_image_path}")
            
            # Test image analysis
            print("\n🔍 Testing image analysis...")
            result = await processor.analyze_image(test_image_path, "detailed")
            
            if result:
                print("✅ Image analysis successful!")
                print(f"   Description: {result.description[:200]}...")
                print(f"   Objects detected: {result.objects_detected}")
                print(f"   Text extracted: {result.text_extracted}")
                print(f"   Model used: {result.model_used}")
                print(f"   Processing time: {result.processing_time:.2f}s")
                print(f"   Confidence: {result.analysis_confidence}")
            else:
                print("❌ Image analysis failed")
            
            # Clean up
            os.unlink(test_image_path)
            
        else:
            print("❌ Multimodal processor failed to initialize")
            
    except Exception as e:
        print(f"💥 Error testing multimodal processor: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    asyncio.run(test_multimodal_processor())
