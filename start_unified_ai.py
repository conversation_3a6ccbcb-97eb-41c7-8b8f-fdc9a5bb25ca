#!/usr/bin/env python3
"""
Unified AI Startup Script
Starts Agent-Zero, AgenticSeek, and the Unified Bridge
"""

import os
import sys
import time
import subprocess
import threading
import signal
import requests
from pathlib import Path

class UnifiedAIStarter:
    def __init__(self):
        self.processes = []
        self.base_dir = Path.cwd()
        self.agent_zero_dir = self.base_dir / "agent-zero-main"
        self.agentic_seek_dir = self.base_dir / "agenticSeek-main"
        self.crew_ai_script = self.base_dir / "crew_service.py"
        self.ten_framework_script = self.base_dir / "ten_service.py"
        self.running = True
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
    
    def signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        print("\n🛑 Shutting down all systems...")
        self.running = False
        self.cleanup()
        sys.exit(0)
    
    def check_dependencies(self):
        """Check if required directories and files exist"""
        print("[DEBUG] Entering check_dependencies")
        
        if not self.agent_zero_dir.exists():
            print(f"❌ Agent-Zero directory not found: {self.agent_zero_dir}")
            return False
        
        if not self.agentic_seek_dir.exists():
            print(f"❌ AgenticSeek directory not found: {self.agentic_seek_dir}")
            return False
        
        if not (self.agent_zero_dir / "run_ui.py").exists():
            print(f"❌ Agent-Zero run_ui.py not found")
            return False
        
        if not (self.agentic_seek_dir / "api.py").exists():
            print(f"❌ AgenticSeek api.py not found")
            return False
        
        if not self.crew_ai_script.exists():
            print(f"❌ CrewAI service script not found: {self.crew_ai_script}")
            return False
        
        if not self.ten_framework_script.exists():
            print(f"❌ Ten Framework service script not found: {self.ten_framework_script}")
            return False
        
        print("[DEBUG] Exiting check_dependencies - SUCCESS")
        return True
    
    def wait_for_service(self, name, url, max_attempts=30, delay=2):
        """Wait for a service to become available"""
        print(f"⏳ Waiting for {name} to start...")
        
        for attempt in range(max_attempts):
            try:
                response = requests.get(url, timeout=5)
                if response.status_code in [200, 404]:  # 404 is OK for some endpoints
                    print(f"✅ {name} is ready!")
                    return True
            except requests.exceptions.RequestException:
                pass
            
            if not self.running:
                return False
                
            time.sleep(delay)
            print(f"   Attempt {attempt + 1}/{max_attempts}...")
        
        print(f"❌ {name} failed to start within {max_attempts * delay} seconds")
        return False
    
    def start_agent_zero(self):
        """Start Agent-Zero system"""
        print("[DEBUG] Entering start_agent_zero")
        try:
            os.chdir(self.agent_zero_dir)
            process = subprocess.Popen(
                [sys.executable, "run_ui.py"],
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            self.processes.append(('Agent-Zero', process))
            
            # Monitor stdout and stderr in separate threads
            def monitor_stream(stream, prefix):
                for line in stream:
                    if self.running:
                        print(f"[{prefix}] {line.strip()}")
            
            threading.Thread(target=monitor_stream, args=(process.stdout, 'Agent-Zero'), daemon=True).start()
            threading.Thread(target=monitor_stream, args=(process.stderr, 'Agent-Zero-ERR'), daemon=True).start()
            print("[DEBUG] Agent-Zero process started")
            return process
        except Exception as e:
            print(f"❌ Failed to start Agent-Zero: {e}")
            return None
        finally:
            os.chdir(self.base_dir)
    
    def start_agentic_seek(self):
        """Start AgenticSeek system"""
        print("[DEBUG] Entering start_agentic_seek")
        try:
            os.chdir(self.agentic_seek_dir)
            process = subprocess.Popen(
                [sys.executable, "api.py"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            self.processes.append(('AgenticSeek', process))
            
            # Monitor stdout and stderr in separate threads
            def monitor_stream(stream, prefix):
                for line in stream:
                    if self.running:
                        print(f"[{prefix}] {line.strip()}")

            threading.Thread(target=monitor_stream, args=(process.stdout, 'AgenticSeek'), daemon=True).start()
            threading.Thread(target=monitor_stream, args=(process.stderr, 'AgenticSeek-ERR'), daemon=True).start()
            print("[DEBUG] AgenticSeek process started")
            return process
        except Exception as e:
            print(f"❌ Failed to start AgenticSeek: {e}")
            return None
        finally:
            os.chdir(self.base_dir)
    
    def start_crew_ai(self):
        """Start CrewAI service"""
        print("[DEBUG] Entering start_crew_ai")
        try:
            process = subprocess.Popen(
                [sys.executable, "crew_service.py"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            self.processes.append(('CrewAI', process))
            
            # Monitor stdout and stderr in separate threads
            def monitor_stream(stream, prefix):
                for line in stream:
                    if self.running:
                        print(f"[{prefix}] {line.strip()}")

            threading.Thread(target=monitor_stream, args=(process.stdout, 'CrewAI'), daemon=True).start()
            threading.Thread(target=monitor_stream, args=(process.stderr, 'CrewAI-ERR'), daemon=True).start()
            print("[DEBUG] CrewAI process started")
            return process
        except Exception as e:
            print(f"❌ Failed to start CrewAI: {e}")
            return None
    
    def start_ten_framework(self):
        """Start Ten Framework service"""
        print("[DEBUG] Entering start_ten_framework")
        try:
            process = subprocess.Popen(
                [sys.executable, "ten_service.py"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            self.processes.append(('Ten Framework', process))
            
            # Monitor stdout and stderr in separate threads
            def monitor_stream(stream, prefix):
                for line in stream:
                    if self.running:
                        print(f"[{prefix}] {line.strip()}")

            threading.Thread(target=monitor_stream, args=(process.stdout, 'TenFW'), daemon=True).start()
            threading.Thread(target=monitor_stream, args=(process.stderr, 'TenFW-ERR'), daemon=True).start()
            print("[DEBUG] Ten Framework process started")
            return process
        except Exception as e:
            print(f"❌ Failed to start Ten Framework: {e}")
            return None
    
    def start_bridge(self):
        """Start the Unified AI Bridge"""
        print("🚀 Starting Unified AI Bridge...")
        try:
            process = subprocess.Popen(
                [sys.executable, "unified_ai_bridge.py"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            self.processes.append(('Unified Bridge', process))
            
            # Monitor stdout and stderr in separate threads
            def monitor_stream(stream, prefix):
                for line in stream:
                    if self.running:
                        print(f"[{prefix}] {line.strip()}")

            threading.Thread(target=monitor_stream, args=(process.stdout, 'Bridge'), daemon=True).start()
            threading.Thread(target=monitor_stream, args=(process.stderr, 'Bridge-ERR'), daemon=True).start()
            return process
        except Exception as e:
            print(f"❌ Failed to start Unified Bridge: {e}")
            return None
    
    def cleanup(self):
        """Clean up all processes"""
        print("\n🧹 Cleaning up processes...")
        self.running = False

        # Stop all processes in reverse order
        for name, process in reversed(self.processes):
            if process and process.poll() is None:
                print(f"   Stopping {name}...")
                try:
                    process.terminate()
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    print(f"   Force killing {name}...")
                    process.kill()
                except Exception as e:
                    print(f"   Error stopping {name}: {e}")

        print("✅ Cleanup complete")
    
    def monitor_processes(self):
        """Monitor all processes and restart if needed"""
        while self.running:
            for i, (name, process) in enumerate(self.processes):
                if process.poll() is not None:  # Process has terminated
                    print(f"⚠️  {name} has stopped unexpectedly")
                    # Could implement restart logic here
            time.sleep(5)
    
    def print_status(self):
        """Print system status and URLs"""
        print("\n" + "="*70)
        print("🎉 UNIFIED AI SYSTEM STARTED SUCCESSFULLY!")
        print("="*70)
        print("📊 System Status:")
        print("   🤖 Agent-Zero:      http://localhost:50001")
        print("   🔍 AgenticSeek:     http://localhost:8000")
        print("   👥 CrewAI:          http://localhost:8001")
        print("   🎯 Ten Framework:   http://localhost:8002")
        print("   🌉 Unified Bridge:  http://localhost:9000")
        print("\n📖 Usage:")
        print("   • Access Agent-Zero web UI at http://localhost:50001")
        print("   • Access AgenticSeek API at http://localhost:8000")
        print("   • Access CrewAI API at http://localhost:8001")
        print("   • Access Ten Framework API at http://localhost:8002")
        print("   • Use Unified Bridge API at http://localhost:9000")
        print("\n🔧 API Examples:")
        print("   # General query (auto-routed)")
        print("   curl -X POST http://localhost:9000/query \\")
        print("        -H 'Content-Type: application/json' \\")
        print("        -d '{\"query\": \"Hello, what can you do?\"}'")
        print("\n   # Force specific system")
        print("   curl -X POST http://localhost:9000/query \\")
        print("        -H 'Content-Type: application/json' \\")
        print("        -d '{\"query\": \"Complex analysis\", \"system_preference\": \"crew-ai\"}'")
        print("\n⚠️  Press Ctrl+C to stop all systems")
        print("="*70 + "\n")
    
    def run(self):
        """Main execution method"""
        print("🌟 UNIFIED AI SYSTEM STARTUP")
        print("🔗 Connecting Agent-Zero + AgenticSeek + CrewAI + Ten Framework")
        print("="*70)
        
        # Check dependencies
        if not self.check_dependencies():
            print("❌ Dependency check failed. Exiting.")
            return False
        
        try:
            # Start Agent-Zero
            agent_zero_process = self.start_agent_zero()
            if not agent_zero_process:
                return False
            
            # Wait for Agent-Zero to be ready
            if not self.wait_for_service("Agent-Zero", "http://localhost:50001"):
                return False
            
            # Start AgenticSeek
            agentic_seek_process = self.start_agentic_seek()
            if not agentic_seek_process:
                print("⚠️  AgenticSeek failed to start, continuing without it")
            else:
                if not self.wait_for_service("AgenticSeek", "http://localhost:8000/health"):
                    print("⚠️  AgenticSeek health check failed, continuing anyway")
            
            # Start CrewAI
            crew_ai_process = self.start_crew_ai()
            if not crew_ai_process:
                print("⚠️  CrewAI failed to start, continuing without it")
            else:
                if not self.wait_for_service("CrewAI", "http://localhost:8001/health"):
                    print("⚠️  CrewAI health check failed, continuing anyway")
            
            # Start Ten Framework
            ten_framework_process = self.start_ten_framework()
            if not ten_framework_process:
                print("⚠️  Ten Framework failed to start, continuing without it")
            else:
                if not self.wait_for_service("Ten Framework", "http://localhost:8002/health"):
                    print("⚠️  Ten Framework health check failed, continuing anyway")
            
            # Start Unified Bridge
            bridge_process = self.start_bridge()
            if not bridge_process:
                print("⚠️  Unified Bridge failed to start")
            else:
                if not self.wait_for_service("Unified Bridge", "http://localhost:9000/health"):
                    print("⚠️  Unified Bridge health check failed, continuing anyway")
            
            # Print success status
            self.print_status()
            
            # Start monitoring
            monitor_thread = threading.Thread(target=self.monitor_processes, daemon=True)
            monitor_thread.start()
            
            # Keep running until interrupted
            try:
                while self.running:
                    time.sleep(1)
            except KeyboardInterrupt:
                pass
            
            return True
            
        except Exception as e:
            print(f"❌ Startup failed: {e}")
            return False
        finally:
            self.cleanup()

if __name__ == "__main__":
    starter = UnifiedAIStarter()
    success = starter.run()
    sys.exit(0 if success else 1)