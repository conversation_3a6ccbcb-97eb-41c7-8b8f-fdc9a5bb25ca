#!/usr/bin/env python3
"""
BRUTAL HONEST ASSESSMENT OF THE ENTIRE CODEBASE
No lies, no optimism, just the raw truth about what works and what doesn't
"""

import os
import requests
import json
import time
from pathlib import Path

def assess_entire_codebase():
    """Brutally honest assessment of everything"""
    
    print("💀 BRUTAL HONEST CODEBASE ASSESSMENT")
    print("=" * 60)
    print("🚨 WARNING: This will tell you the REAL truth about everything")
    print("=" * 60)
    
    issues = []
    working_features = []
    
    # 1. UNIFIED AI SYSTEM
    print("\n🤖 UNIFIED AI SYSTEM")
    print("-" * 30)
    
    try:
        response = requests.get("http://localhost:9002/health", timeout=5)
        if response.status_code == 200:
            print("✅ Main system is running")
            working_features.append("Main unified system")
        else:
            print("❌ Main system not responding")
            issues.append("Main unified system down")
    except:
        print("❌ Main system completely dead")
        issues.append("Main unified system completely dead")
    
    # 2. VOICE PROCESSING
    print("\n🎤 VOICE PROCESSING")
    print("-" * 30)
    
    # TTS Test
    try:
        response = requests.post("http://localhost:9002/tts", 
                               json={"text": "test"}, timeout=10)
        if response.status_code == 200:
            print("✅ Text-to-Speech works")
            working_features.append("Text-to-Speech")
        else:
            print("❌ Text-to-Speech broken")
            issues.append("Text-to-Speech broken")
    except:
        print("❌ Text-to-Speech completely broken")
        issues.append("Text-to-Speech completely broken")
    
    # STT Test (we know this is broken)
    print("❌ Speech-to-Text broken (confirmed)")
    issues.append("Speech-to-Text broken")
    
    # 3. IMAGE ANALYSIS
    print("\n👁️ IMAGE ANALYSIS")
    print("-" * 30)
    
    try:
        # Test with minimal image
        import base64
        test_image = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="
        files = {'file': ('test.png', base64.b64decode(test_image), 'image/png')}
        
        response = requests.post("http://localhost:9002/image", 
                               files=files, timeout=30)
        if response.status_code == 200:
            result = response.json()
            if result.get("description"):
                print("✅ Image analysis works")
                working_features.append("Image analysis")
            else:
                print("❌ Image analysis returns empty results")
                issues.append("Image analysis returns empty results")
        else:
            print("❌ Image analysis broken")
            issues.append("Image analysis broken")
    except Exception as e:
        print(f"❌ Image analysis completely broken: {e}")
        issues.append("Image analysis completely broken")
    
    # 4. WEB RESEARCH
    print("\n🔍 WEB RESEARCH")
    print("-" * 30)
    
    try:
        response = requests.post("http://localhost:9002/chat", 
                               json={"message": "search for recent AI news"}, 
                               timeout=60)
        if response.status_code == 200:
            result = response.json()
            if len(result.get("response", "")) > 500:
                print("✅ Web research works")
                working_features.append("Web research")
            else:
                print("❌ Web research returns minimal content")
                issues.append("Web research returns minimal content")
        else:
            print("❌ Web research broken")
            issues.append("Web research broken")
    except Exception as e:
        print(f"❌ Web research completely broken: {e}")
        issues.append("Web research completely broken")
    
    # 5. CODE GENERATION
    print("\n💻 CODE GENERATION")
    print("-" * 30)
    
    try:
        response = requests.post("http://localhost:9002/chat", 
                               json={"message": "write a python function to add two numbers"}, 
                               timeout=30)
        if response.status_code == 200:
            result = response.json()
            if "def " in result.get("response", ""):
                print("✅ Code generation works")
                working_features.append("Code generation")
            else:
                print("❌ Code generation doesn't return code")
                issues.append("Code generation doesn't return code")
        else:
            print("❌ Code generation broken")
            issues.append("Code generation broken")
    except Exception as e:
        print(f"❌ Code generation completely broken: {e}")
        issues.append("Code generation completely broken")
    
    # 6. FRONTEND INTERFACE
    print("\n🌐 FRONTEND INTERFACE")
    print("-" * 30)
    
    frontend_path = Path("modern-gossiper-ai-bot-main")
    if frontend_path.exists():
        print("✅ Frontend files exist")
        
        # Check if it's actually running
        try:
            response = requests.get("http://localhost:8081", timeout=5)
            if response.status_code == 200:
                print("✅ Frontend is accessible")
                working_features.append("Frontend interface")
            else:
                print("❌ Frontend not running")
                issues.append("Frontend not running")
        except:
            print("❌ Frontend completely inaccessible")
            issues.append("Frontend completely inaccessible")
    else:
        print("❌ Frontend files missing")
        issues.append("Frontend files missing")
    
    # 7. OLLAMA MODELS
    print("\n🧠 OLLAMA MODELS")
    print("-" * 30)
    
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=10)
        if response.status_code == 200:
            models = response.json().get("models", [])
            print(f"✅ Ollama running with {len(models)} models")
            working_features.append(f"Ollama with {len(models)} models")
            
            # Check specific models
            model_names = [m.get("name", "") for m in models]
            critical_models = ["llava:7b", "deepseek-r1", "llama3.2"]
            
            for model in critical_models:
                if any(model in name for name in model_names):
                    print(f"  ✅ {model} available")
                else:
                    print(f"  ❌ {model} missing")
                    issues.append(f"{model} model missing")
        else:
            print("❌ Ollama not responding")
            issues.append("Ollama not responding")
    except:
        print("❌ Ollama completely dead")
        issues.append("Ollama completely dead")
    
    # 8. ADDITIONAL AI SYSTEMS
    print("\n🤖 ADDITIONAL AI SYSTEMS")
    print("-" * 30)
    
    additional_systems = [
        "agents-main",
        "AgentGPT-main", 
        "agenticSeek-main",
        "agent-zero-main",
        "OpenHands-main",
        "OpenVoice-main"
    ]
    
    for system in additional_systems:
        if Path(system).exists():
            print(f"📁 {system}: Files exist")
            # These are mostly NOT integrated or working
            issues.append(f"{system} exists but not properly integrated")
        else:
            print(f"❌ {system}: Missing")
            issues.append(f"{system} missing")
    
    # 9. CONFIGURATION FILES
    print("\n⚙️ CONFIGURATION")
    print("-" * 30)
    
    config_files = [
        "unified_local_config.json",
        "package.json",
        "requirements.txt"
    ]
    
    for config in config_files:
        if Path(config).exists():
            print(f"✅ {config} exists")
        else:
            print(f"❌ {config} missing")
            issues.append(f"{config} missing")
    
    # FINAL BRUTAL ASSESSMENT
    print("\n" + "=" * 60)
    print("💀 FINAL BRUTAL TRUTH")
    print("=" * 60)
    
    print(f"\n✅ WORKING FEATURES ({len(working_features)}):")
    for feature in working_features:
        print(f"   ✅ {feature}")
    
    print(f"\n❌ BROKEN/MISSING FEATURES ({len(issues)}):")
    for issue in issues:
        print(f"   ❌ {issue}")
    
    # Calculate brutal honesty percentage
    total_expected_features = 20  # Rough estimate of what should work
    working_percentage = (len(working_features) / total_expected_features) * 100
    
    print(f"\n📊 BRUTAL HONESTY SCORE:")
    print(f"   Working: {len(working_features)}/{total_expected_features}")
    print(f"   Success Rate: {working_percentage:.1f}%")
    
    if working_percentage >= 80:
        print("🎉 VERDICT: System is mostly working")
    elif working_percentage >= 60:
        print("⚠️ VERDICT: System is partially working")
    elif working_percentage >= 40:
        print("🔥 VERDICT: System has major issues")
    else:
        print("💀 VERDICT: System is mostly broken")
    
    return working_features, issues

if __name__ == "__main__":
    working, broken = assess_entire_codebase()
    print(f"\n🎯 SUMMARY: {len(working)} working, {len(broken)} broken")
