{"openhands": {"name": "OpenHands", "description": "AI-powered software development agent", "type": "development_assistant", "capabilities": ["code_generation", "development_assistance", "file_operations", "task_control", "code_execution", "llm_integration", "async_http"], "integration_points": ["web_interface", "docker_deployment"], "requirements": ["python_3.12+", "poetry"], "api_endpoints": []}, "openvoice": {"name": "OpenVoice", "description": "Advanced voice cloning and synthesis system", "type": "voice_processing", "capabilities": ["voice_cloning", "voice_synthesis", "multilingual_tts", "api_interface", "interactive_demos", "audio_processing", "speech_recognition", "web_interface", "openai_integration"], "integration_points": ["jupyter_notebooks", "gradio_ui"], "requirements": ["librosa==0.9.1", "faster-whisper==0.9.0", "pydub==0.25.1", "wavmark==0.0.3", "numpy==1.22.0", "eng_to_ipa==0.0.2", "inflect==7.0.0", "unidecode==1.3.7", "whisper-timestamped==1.14.2", "openai", "python-dotenv", "pypinyin==0.50.0", "cn2an==0.5.22", "jieba==0.42.1", "gradio==3.48.0", "langid==1.1.6"], "api_endpoints": []}, "integration_plan": {"new_capabilities": ["development_assistance", "file_operations", "task_control", "code_execution", "llm_integration", "async_http", "voice_cloning", "voice_synthesis", "multilingual_tts", "api_interface", "interactive_demos", "audio_processing", "speech_recognition", "web_interface", "openai_integration"], "integration_complexity": "medium", "estimated_capabilities": 21}, "current_system_capabilities": 6, "projected_system_capabilities": 21}