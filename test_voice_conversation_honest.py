#!/usr/bin/env python3
"""
HONEST test of voice conversation functionality
Tests both speech-to-text AND text-to-speech in a real conversation flow
"""

import requests
import json
import time
import base64
import os

def test_voice_conversation_honest():
    """Test complete voice conversation flow with brutal honesty"""
    
    print("🎤 HONEST VOICE CONVERSATION TEST")
    print("=" * 50)
    
    # Test 1: Check if we can actually do Speech-to-Text (STT)
    print("\n🔍 TEST 1: Speech-to-Text (STT)")
    print("-" * 30)
    
    try:
        # Try to find if there's a real STT endpoint
        response = requests.get("http://localhost:9002/health", timeout=10)
        if response.status_code == 200:
            print("✅ Backend is running")
        else:
            print("❌ Backend not responding")
            return False
            
        # Check if there's a voice endpoint
        test_audio_data = b"fake_audio_data"  # This is fake, but let's see what happens
        
        files = {'audio': ('test.wav', test_audio_data, 'audio/wav')}
        response = requests.post(
            "http://localhost:9002/voice",
            files=files,
            timeout=30
        )
        
        print(f"📊 STT Response Status: {response.status_code}")
        print(f"📄 STT Response: {response.text[:200]}...")
        
        if response.status_code == 200:
            result = response.json()
            if "transcription" in result or "text" in result:
                print("✅ STT endpoint exists and responds")
                stt_works = True
            else:
                print("❌ STT endpoint exists but doesn't return transcription")
                stt_works = False
        else:
            print("❌ STT endpoint failed or doesn't exist")
            stt_works = False
            
    except Exception as e:
        print(f"❌ STT Test Error: {str(e)}")
        stt_works = False
    
    # Test 2: Check if we can actually do Text-to-Speech (TTS)
    print("\n🔊 TEST 2: Text-to-Speech (TTS)")
    print("-" * 30)
    
    try:
        payload = {
            "message": "Hello, this is a test of text to speech functionality.",
            "voice_enabled": True
        }
        
        start_time = time.time()
        response = requests.post(
            "http://localhost:9002/chat",
            json=payload,
            timeout=30
        )
        end_time = time.time()
        
        print(f"📊 TTS Response Status: {response.status_code}")
        print(f"⏱️ Response Time: {end_time - start_time:.2f}s")
        
        if response.status_code == 200:
            result = response.json()
            print(f"📄 TTS Response Keys: {list(result.keys())}")
            
            # Check if we get actual audio data
            if "audio" in result or "audio_data" in result or "speech" in result:
                audio_data = result.get("audio") or result.get("audio_data") or result.get("speech")
                if audio_data and len(str(audio_data)) > 100:
                    print("✅ TTS returns audio data")
                    tts_works = True
                else:
                    print("❌ TTS returns empty or minimal audio data")
                    tts_works = False
            else:
                print("❌ TTS doesn't return audio data")
                print(f"📄 Available keys: {list(result.keys())}")
                tts_works = False
        else:
            print(f"❌ TTS failed: {response.text[:200]}...")
            tts_works = False
            
    except Exception as e:
        print(f"❌ TTS Test Error: {str(e)}")
        tts_works = False
    
    # Test 3: Check what voice processing actually exists
    print("\n🔍 TEST 3: Voice Processing Reality Check")
    print("-" * 40)
    
    try:
        # Check what endpoints actually exist
        endpoints_to_check = [
            "/voice",
            "/speech",
            "/tts",
            "/stt", 
            "/audio",
            "/transcribe"
        ]
        
        working_endpoints = []
        for endpoint in endpoints_to_check:
            try:
                response = requests.get(f"http://localhost:9002{endpoint}", timeout=5)
                if response.status_code != 404:
                    working_endpoints.append(f"{endpoint} ({response.status_code})")
            except:
                pass
        
        if working_endpoints:
            print(f"✅ Found voice endpoints: {working_endpoints}")
        else:
            print("❌ No voice endpoints found")
            
    except Exception as e:
        print(f"❌ Endpoint check error: {str(e)}")
    
    # HONEST SUMMARY
    print("\n" + "=" * 50)
    print("🎯 BRUTAL HONEST VOICE CONVERSATION ASSESSMENT")
    print("=" * 50)
    
    if stt_works and tts_works:
        print("✅ VOICE CONVERSATION: FULLY WORKING")
        print("   - Speech-to-Text: ✅ Working")
        print("   - Text-to-Speech: ✅ Working")
        print("   - Full conversation flow possible")
        return True
    elif tts_works and not stt_works:
        print("⚠️ VOICE CONVERSATION: PARTIALLY WORKING")
        print("   - Speech-to-Text: ❌ Not working")
        print("   - Text-to-Speech: ✅ Working")
        print("   - Only one-way voice output possible")
        return False
    elif stt_works and not tts_works:
        print("⚠️ VOICE CONVERSATION: PARTIALLY WORKING")
        print("   - Speech-to-Text: ✅ Working")
        print("   - Text-to-Speech: ❌ Not working")
        print("   - Only voice input possible")
        return False
    else:
        print("❌ VOICE CONVERSATION: NOT WORKING")
        print("   - Speech-to-Text: ❌ Not working")
        print("   - Text-to-Speech: ❌ Not working")
        print("   - No voice capabilities available")
        return False

if __name__ == "__main__":
    success = test_voice_conversation_honest()
    print(f"\n🎯 FINAL VERDICT: {'SUCCESS' if success else 'FAILED'}")
