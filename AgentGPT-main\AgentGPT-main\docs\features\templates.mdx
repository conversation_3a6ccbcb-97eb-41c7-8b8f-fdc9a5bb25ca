---
title: Templates
description: Re-use scrapers across identical sites
---

<Note>Note: Templates are not available for hobby plan customers</Note>

When scraping, you'll often encounter multiple websites using identical underlying structures or sharing the same web provider.
Instead of repeatedly writing new scrapers for each of these websites—which consumes both your time and LLM tokens—use Templates.

Templates allow you to save and re-use pre-built scraper code.
Once created, templates can be applied to multiple websites with matching structures, without any extra effort required.
This means whenever the structure of these websites changes, you only need to update the template once to instantly fix the issue for all associated sites, rather than manually updating each individual scraper.
