name: Documentation Improvement / Clarity
description: Make a suggestion to improve the project documentation.
labels: ['needs triage']
body:
  - type: markdown
    attributes:
      value: '## :book: Documentation :book:'
  - type: markdown
    attributes:
      value: |
        * Ask questions in [Discord](https://discord.gg/jjYCfaqu).
        * Before you file an issue read the [Contributing guide](https://github.com/reworkd/AgentGPT/tree/main/.github/CONTRIBUTING.md).
        * Check to make sure someone hasn't already opened a [similar issue](https://github.com/reworkd/AgentGPT/issues).
  - type: textarea
    attributes:
      label: What piece of documentation is affected?
      description: Please link to the article you'd like to see updated.
    validations:
      required: true
  - type: textarea
    attributes:
      label: What part(s) of the article would you like to see updated?
      description: |
        - Give as much detail as you can to help us understand the change you want to see. 
        - Why should the docs be changed? What use cases does it support? 
        - What is the expected outcome?
    validations:
      required: true
  - type: textarea
    attributes:
      label: Additional Information
      description: Add any other context or screenshots about the feature request here.
    validations:
      required: false
  - type: checkboxes
    id: acknowledgements
    attributes:
      label: 'Acknowledgements'
      description: 'Please confirm the following:'
      options:
        - label: 'My issue title is concise, descriptive, and in title casing.'
          required: true
        - label: 'I have searched the existing issues to make sure this feature has not been requested yet.'
          required: true
        - label: 'I have provided enough information for the maintainers to understand and evaluate this request.'
          required: true
