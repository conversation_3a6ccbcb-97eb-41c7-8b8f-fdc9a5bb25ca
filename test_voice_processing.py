#!/usr/bin/env python3
"""
Test voice processing capabilities of the unified agent
"""

import requests
import json
import time
from local_voice_processor import get_voice_processor
import asyncio

async def test_voice_processor_direct():
    """Test the voice processor directly"""
    print("🎤 Testing Voice Processor Directly")
    print("=" * 50)
    
    try:
        voice_proc = await get_voice_processor()
        status = voice_proc.get_status()
        
        print(f"Voice Processor Status: {json.dumps(status, indent=2)}")
        
        if status["initialized"]:
            print("✅ Voice processor initialized successfully")
            
            # Test TTS
            print("\n🔊 Testing Text-to-Speech...")
            test_text = "Hello! This is a test of the local text to speech system."
            success = await voice_proc.text_to_speech(test_text)
            
            if success:
                print("✅ TTS test successful")
            else:
                print("❌ TTS test failed")
        else:
            print("❌ Voice processor not initialized")
            print("Available capabilities:", status.get("capabilities", []))
            
    except Exception as e:
        print(f"❌ Voice processor test failed: {e}")

def test_voice_capability_via_api():
    """Test voice processing via the unified agent API"""
    print("\n🤖 Testing Voice Processing via Unified Agent API")
    print("=" * 60)
    
    base_url = "http://localhost:9001"
    
    # Test voice-related text requests
    test_cases = [
        {
            "name": "Voice Processing Request",
            "message": "I want to have a voice conversation with you",
            "capability": "voice_processing"
        },
        {
            "name": "Speech-related Query",
            "message": "Can you speak to me?",
            "capability": None  # Let it auto-detect
        },
        {
            "name": "Audio Processing Query", 
            "message": "Process this audio message",
            "capability": None  # Let it auto-detect
        }
    ]
    
    for test_case in test_cases:
        print(f"\n🧪 Testing: {test_case['name']}")
        print(f"📝 Message: {test_case['message']}")
        
        try:
            payload = {
                "message": test_case["message"],
                "capability": test_case["capability"]
            }
            
            start_time = time.time()
            response = requests.post(
                f"{base_url}/chat",
                json=payload,
                timeout=30
            )
            end_time = time.time()
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Success! ({end_time - start_time:.2f}s)")
                print(f"🤖 Model Used: {result.get('model_used', 'unknown')}")
                print(f"🎯 Capability: {result.get('capability_used', 'unknown')}")
                print(f"💬 Response: {result.get('response', 'No response')[:200]}...")
            else:
                print(f"❌ Failed with status: {response.status_code}")
                print(f"Error: {response.text}")
                
        except requests.exceptions.Timeout:
            print(f"⏰ Timeout after 30 seconds")
        except Exception as e:
            print(f"❌ Error: {e}")
        
        print("-" * 60)

def test_tts_endpoint():
    """Test the TTS endpoint"""
    print("\n🔊 Testing TTS Endpoint")
    print("=" * 40)
    
    base_url = "http://localhost:9001"
    
    try:
        payload = {
            "text": "Hello! This is a test of the text to speech API endpoint."
        }
        
        response = requests.post(
            f"{base_url}/tts",
            json=payload,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ TTS endpoint test successful")
            print(f"Response: {json.dumps(result, indent=2)}")
        else:
            print(f"❌ TTS endpoint failed with status: {response.status_code}")
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"❌ TTS endpoint error: {e}")

async def main():
    """Run all voice processing tests"""
    print("🚀 Testing Voice Processing Capabilities")
    print("=" * 70)
    
    # Test 1: Direct voice processor
    await test_voice_processor_direct()
    
    # Test 2: Voice capability via API
    test_voice_capability_via_api()
    
    # Test 3: TTS endpoint
    test_tts_endpoint()
    
    print("\n🎉 Voice processing tests completed!")

if __name__ == "__main__":
    asyncio.run(main())
