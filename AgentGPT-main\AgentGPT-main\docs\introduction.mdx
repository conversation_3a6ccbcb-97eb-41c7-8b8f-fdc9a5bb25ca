---
title: Introduction
description: Reworkd - Extract web data at scale
---

<Frame>
    <img src="/images/banner.png" />
</Frame>

Reworkd uses LLMs to parse, understand, and interact with web pages to help users scrape web data at ***scale***.
Reworkd customers are extracting millions of rows of data to help build data constrained products, fine tune domain specific language models, and enrich existing data pipelines.

<Info>
    We also built AgentGPT! If you're looking for info on AgentGPT, please visit our
    [Github](https://github.com/reworkd/AgentGPT)
</Info>

<CardGroup cols={2}>
    <Card title="Build your first scraper" icon="lightbulb" href="https://app.arcade.software/share/6GkFKiXytq3lXQU3JWAx">
        Learn how to scrape a website and its sub pages with Reworkd
    </Card>
    <Card title="Key terms" icon="brain" href="/key-concepts">
        Understand all of the key terms required to get started
    </Card>
    <Card title="Exporting data" icon="globe" href="/features/exports/overview">
        Learn how to export the data you scrape
    </Card>
    <Card title="Blog" icon="rss" href="https://www.reworkd.ai/blog">
        Read the latest updates on the Reworkd blog
    </Card>
</CardGroup>
