import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Send, MessageSquare, Plus, Menu, Image, Mic, MicOff, Paperclip, Search, Bot, User, Loader2, Globe } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';

interface Message {
  id: string;
  text: string;
  sender: 'user' | 'ai';
  timestamp: Date;
  type?: 'text' | 'image' | 'reasoning';
  imageUrl?: string;
  reasoning?: {
    steps: string[];
    currentStep: number;
    isComplete: boolean;
  };
}

interface ChatBoxProps {
  className?: string;
}

export function ChatBox({ className }: ChatBoxProps) {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      text: 'Hello! I\'m your Unified AI Assistant with advanced capabilities including web search, code generation, task planning, voice processing, and multimodal analysis. I can help with research, programming, planning, and much more. How can I assist you today?',
      sender: 'ai',
      timestamp: new Date(),
      type: 'text'
    }
  ]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [apiKey, setApiKey] = useState('');
  const [showApiKeyInput, setShowApiKeyInput] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSend = async () => {
    if (!input.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      text: input.trim(),
      sender: 'user',
      timestamp: new Date(),
      type: 'text'
    };

    setMessages(prev => [...prev, userMessage]);
    const userInput = input.trim();
    setInput('');
    setIsLoading(true);

    try {
      const response = await fetch('http://localhost:9001/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: userInput
        })
      });

      if (response.ok) {
        const data = await response.json();
        
        const aiMessage: Message = {
          id: (Date.now() + 1).toString(),
          text: data.response || 'I apologize, but I encountered an issue processing your request. Please try again.',
          sender: 'ai',
          timestamp: new Date(),
          type: 'text'
        };

        setMessages(prev => [...prev, aiMessage]);
      } else {
        throw new Error('Failed to get response');
      }
    } catch (error) {
      console.error('Error sending message:', error);
      
      const fallbackMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: `I understand you're asking: "${userInput}"\n\nI'm currently running in demo mode since I can't connect to the backend server. In the full system, I would analyze your request and provide comprehensive responses with web search, code generation, and more.\n\nTo activate the full system, please ensure the Unified AI backend is running on localhost:9001.`,
        sender: 'ai',
        timestamp: new Date(),
        type: 'text'
      };

      setMessages(prev => [...prev, fallbackMessage]);
      
      toast({
        title: "Connection Error",
        description: "Running in demo mode. Start the backend server for full functionality.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const clearChat = () => {
    setMessages([
      {
        id: '1',
        text: 'Hello! How can I assist you today?',
        sender: 'ai',
        timestamp: new Date(),
        type: 'text'
      }
    ]);
  };

  const toggleRecording = () => {
    setIsRecording(!isRecording);
    toast({
      title: isRecording ? "Recording stopped" : "Recording started",
      description: isRecording ? "Processing your voice..." : "Speak now...",
    });
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const imageUrl = e.target?.result as string;
        const imageMessage: Message = {
          id: Date.now().toString(),
          text: 'Image uploaded',
          sender: 'user',
          timestamp: new Date(),
          type: 'image',
          imageUrl
        };
        setMessages(prev => [...prev, imageMessage]);
      };
      reader.readAsDataURL(file);
    }
  };

  return (
    <div className={cn("h-screen flex bg-white relative overflow-hidden", className)}>
      {/* Modern Sidebar - Chat History */}
      <div className="w-80 bg-gray-50 border-r border-gray-200 flex flex-col relative z-10">
        {/* Sidebar Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900">Chat Results</h2>
            <Button variant="ghost" size="sm" className="text-gray-500 hover:text-gray-700">
              <Plus className="h-4 w-4" />
            </Button>
          </div>
          
          <Button
            onClick={clearChat}
            variant="outline"
            className="w-full justify-start gap-2 text-gray-700 border-gray-300 hover:bg-gray-100 transition-all duration-200"
          >
            <Plus className="h-4 w-4" />
            New Chat
          </Button>
        </div>

        {/* Chat History */}
        <div className="flex-1 overflow-y-auto p-4">
          <div className="space-y-4">
            {/* Today Section */}
            <div>
              <h3 className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-3">Today</h3>
              <div className="space-y-2">
                <div className="p-3 rounded-lg bg-white border border-gray-200 hover:shadow-sm transition-shadow cursor-pointer">
                  <div className="flex items-start gap-3">
                    <div className="w-8 h-8 rounded-lg bg-blue-100 flex items-center justify-center flex-shrink-0">
                      <Image className="h-4 w-4 text-blue-600" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h4 className="text-sm font-medium text-gray-900 truncate">Image Generation</h4>
                      <p className="text-xs text-gray-500 mt-1">Today • 2 October</p>
                    </div>
                  </div>
                </div>
                
                <div className="p-3 rounded-lg bg-white border border-gray-200 hover:shadow-sm transition-shadow cursor-pointer">
                  <div className="flex items-start gap-3">
                    <div className="w-8 h-8 rounded-lg bg-green-100 flex items-center justify-center flex-shrink-0">
                      <Search className="h-4 w-4 text-green-600" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h4 className="text-sm font-medium text-gray-900 truncate">Parrot Images</h4>
                      <p className="text-xs text-gray-500 mt-1">Ara parrot, photorealistic, grey background</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Yesterday Section */}
            <div>
              <h3 className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-3">Yesterday</h3>
              <div className="space-y-2">
                <div className="p-3 rounded-lg bg-white border border-gray-200 hover:shadow-sm transition-shadow cursor-pointer">
                  <div className="flex items-start gap-3">
                    <div className="w-8 h-8 rounded-lg bg-purple-100 flex items-center justify-center flex-shrink-0">
                      <Search className="h-4 w-4 text-purple-600" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h4 className="text-sm font-medium text-gray-900 truncate">AI Search</h4>
                      <p className="text-xs text-gray-500 mt-1">Yesterday • 1 October</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Settings Section */}
        <div className="p-4 border-t border-gray-200">
          {showApiKeyInput && (
            <div className="space-y-2 mb-4">
              <Input
                placeholder="ElevenLabs API Key"
                type="password"
                value={apiKey}
                onChange={(e) => setApiKey(e.target.value)}
                className="text-xs"
              />
              <Button
                onClick={() => setShowApiKeyInput(false)}
                size="sm"
                className="w-full"
              >
                Save
              </Button>
            </div>
          )}
          
          <div className="flex items-center gap-2 px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer">
            <div className="h-8 w-8 rounded-full bg-blue-500 text-white flex items-center justify-center text-sm font-semibold">
              U
            </div>
            <span className="text-sm text-gray-900">User</span>
          </div>
        </div>
      </div>

      {/* Modern Main Chat Area */}
      <div className="flex-1 flex flex-col relative z-10 bg-white">
        {/* Modern Header */}
        <div className="border-b border-gray-200 p-6 bg-white">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center">
                <img 
                  src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=40&h=40&fit=crop&crop=face&auto=format" 
                  alt="AI Assistant" 
                  className="w-10 h-10 rounded-full object-cover"
                />
              </div>
              <div>
                <h1 className="text-xl font-semibold text-gray-900">New Chat</h1>
                <p className="text-sm text-gray-500">Hi, Marry! How can I help you?</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button 
                variant="ghost" 
                size="sm"
                onClick={() => setShowApiKeyInput(!showApiKeyInput)}
                className="text-gray-500 hover:text-gray-700 hover:bg-gray-100"
              >
                <Plus className="h-4 w-4" />
              </Button>
              <Button 
                variant="ghost" 
                size="sm"
                className="text-gray-500 hover:text-gray-700 hover:bg-gray-100"
              >
                <Menu className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Messages Area */}
        <div className="flex-1 overflow-y-auto p-6 space-y-6">
          {messages.map((message) => (
            <div key={message.id} className={cn(
              "flex gap-3 max-w-[80%]",
              message.sender === 'user' ? 'flex-row-reverse ml-auto' : 'flex-row'
            )}>
              {/* Avatar */}
              {message.sender === 'ai' ? (
                <img
                  src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=32&h=32&fit=crop&crop=face&auto=format"
                  alt="AI Assistant"
                  className="w-8 h-8 rounded-full object-cover flex-shrink-0"
                />
              ) : (
                <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center flex-shrink-0">
                  <User className="h-4 w-4 text-white" />
                </div>
              )}

              {/* Message Bubble */}
              <div className={cn(
                "rounded-2xl px-4 py-3 max-w-lg shadow-sm",
                message.sender === 'ai'
                  ? 'bg-white border border-gray-200 text-gray-900'
                  : 'bg-blue-500 text-white'
              )}>
                {message.type === 'image' && message.imageUrl && (
                  <img
                    src={message.imageUrl}
                    alt="Uploaded"
                    className="max-w-full h-auto rounded-lg mb-2"
                  />
                )}
                <p className="text-sm leading-relaxed whitespace-pre-wrap">{message.text}</p>
                <div className={cn(
                  "text-xs mt-2 opacity-70",
                  message.sender === 'ai' ? 'text-gray-500' : 'text-blue-100'
                )}>
                  {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                </div>
              </div>
            </div>
          ))}

          {isLoading && (
            <div className="flex gap-3">
              <img
                src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=32&h=32&fit=crop&crop=face&auto=format"
                alt="AI Assistant"
                className="w-8 h-8 rounded-full object-cover flex-shrink-0"
              />
              <div className="bg-white border border-gray-200 rounded-2xl px-4 py-3 shadow-sm">
                <div className="flex items-center gap-2">
                  <Loader2 className="h-4 w-4 animate-spin text-gray-500" />
                  <span className="text-sm text-gray-500">Thinking...</span>
                </div>
              </div>
            </div>
          )}

          <div ref={messagesEndRef} />
        </div>

        {/* Attachment Bar */}
        <div className="border-t border-gray-200 px-6 py-3 bg-gray-50">
          <div className="flex items-center gap-4">
            <Button
              onClick={() => fileInputRef.current?.click()}
              variant="ghost"
              size="sm"
              className="flex items-center gap-2 text-gray-600 hover:text-gray-800 hover:bg-gray-200 px-3 py-2 rounded-lg transition-colors"
            >
              <Paperclip className="h-4 w-4" />
              <span className="text-sm">Chat Files</span>
            </Button>

            <Button
              variant="ghost"
              size="sm"
              className="flex items-center gap-2 text-gray-600 hover:text-gray-800 hover:bg-gray-200 px-3 py-2 rounded-lg transition-colors"
            >
              <Image className="h-4 w-4" />
              <span className="text-sm">Images</span>
            </Button>

            <Button
              variant="ghost"
              size="sm"
              className="flex items-center gap-2 text-gray-600 hover:text-gray-800 hover:bg-gray-200 px-3 py-2 rounded-lg transition-colors"
            >
              <Globe className="h-4 w-4" />
              <span className="text-sm">Translate</span>
            </Button>

            <Button
              onClick={toggleRecording}
              variant="ghost"
              size="sm"
              className={cn(
                "flex items-center gap-2 px-3 py-2 rounded-lg transition-colors",
                isRecording
                  ? "text-red-600 hover:text-red-800 hover:bg-red-100"
                  : "text-gray-600 hover:text-gray-800 hover:bg-gray-200"
              )}
            >
              {isRecording ? <MicOff className="h-4 w-4" /> : <Mic className="h-4 w-4" />}
              <span className="text-sm">Audio Chat</span>
            </Button>
          </div>
        </div>

        {/* Input Area */}
        <div className="border-t border-gray-200 p-6 bg-white">
          <div className="relative">
            <Input
              ref={inputRef}
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Ask me anything ..."
              className="w-full pr-12 py-3 text-sm border-gray-300 rounded-xl focus:border-blue-500 focus:ring-blue-500"
              disabled={isLoading}
            />

            <Button
              onClick={handleSend}
              disabled={!input.trim() || isLoading}
              size="sm"
              className={cn(
                "absolute right-3 top-1/2 -translate-y-1/2 h-8 w-8 p-0 rounded-lg transition-all duration-200",
                input.trim() && !isLoading
                  ? "bg-blue-500 hover:bg-blue-600 text-white"
                  : "bg-gray-300 text-gray-500 cursor-not-allowed"
              )}
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>

        {/* Hidden File Input */}
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleImageUpload}
          className="hidden"
        />
      </div>
    </div>
  );
}
