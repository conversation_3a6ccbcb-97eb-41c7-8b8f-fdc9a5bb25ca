import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Send, MessageSquare, Plus, Menu, Image, Mic, MicOff, Paperclip } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';

interface Message {
  id: string;
  text: string;
  sender: 'user' | 'ai';
  timestamp: Date;
  type?: 'text' | 'image' | 'reasoning';
  imageUrl?: string;
  reasoning?: {
    steps: string[];
    currentStep: number;
    isComplete: boolean;
  };
}

interface ChatBoxProps {
  className?: string;
}

export function ChatBox({ className }: ChatBoxProps) {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      text: 'Hello! I\'m ChatGPT, a large language model by OpenAI. I can help with text, images, voice chat, and complex reasoning. How can I help you today?',
      sender: 'ai',
      timestamp: new Date()
    }
  ]);
  const [input, setInput] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [apiKey, setApiKey] = useState('');
  const [showApiKeyInput, setShowApiKeyInput] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSend = async () => {
    if (!input.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      text: input.trim(),
      sender: 'user',
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInput('');
    setIsTyping(true);

    // Simulate reasoning for complex questions
    const isComplexQuestion = input.toLowerCase().includes('why') || 
                            input.toLowerCase().includes('how') || 
                            input.toLowerCase().includes('explain') ||
                            input.toLowerCase().includes('analyze');

    if (isComplexQuestion) {
      // Add reasoning message
      const reasoningMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: '',
        sender: 'ai',
        timestamp: new Date(),
        type: 'reasoning',
        reasoning: {
          steps: [
            'Analyzing your question...',
            'Breaking down the problem...',
            'Considering multiple perspectives...',
            'Synthesizing information...',
            'Formulating response...'
          ],
          currentStep: 0,
          isComplete: false
        }
      };

      setMessages(prev => [...prev, reasoningMessage]);

      // Animate through reasoning steps
      for (let i = 0; i < reasoningMessage.reasoning!.steps.length; i++) {
        await new Promise(resolve => setTimeout(resolve, 800));
        setMessages(prev => prev.map(msg => 
          msg.id === reasoningMessage.id 
            ? { ...msg, reasoning: { ...msg.reasoning!, currentStep: i } }
            : msg
        ));
      }

      // Complete reasoning
      setMessages(prev => prev.map(msg => 
        msg.id === reasoningMessage.id 
          ? { ...msg, reasoning: { ...msg.reasoning!, isComplete: true } }
          : msg
      ));

      await new Promise(resolve => setTimeout(resolve, 500));
    }

    // Generate AI response
    setTimeout(() => {
      const aiMessage: Message = {
        id: (Date.now() + 2).toString(),
        text: generateAIResponse(userMessage.text),
        sender: 'ai',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, aiMessage]);
      setIsTyping(false);
    }, isComplexQuestion ? 500 : 1500);
  };

  const generateAIResponse = (userInput: string): string => {
    if (userInput.toLowerCase().includes('voice') || userInput.toLowerCase().includes('speak')) {
      return "I support voice chat! You can use the microphone button to speak with me. For the best experience, please provide your ElevenLabs API key in the settings.";
    }
    if (userInput.toLowerCase().includes('image') || userInput.toLowerCase().includes('picture')) {
      return "I can analyze and discuss images! Use the attachment button to upload images, and I'll help you understand what's in them.";
    }
    if (userInput.toLowerCase().includes('reasoning') || userInput.toLowerCase().includes('think')) {
      return "I show my reasoning process for complex questions. When you ask analytical questions, you'll see my step-by-step thinking process with animated indicators.";
    }
    
    const responses = [
      "That's an interesting question! Let me help you with that.",
      "I understand what you're asking. Here's my perspective on this topic.",
      "Great point! I'd be happy to assist you further.",
      "Thanks for sharing that with me. Let me provide some insights.",
      "I appreciate your question. Here's what I think about this.",
    ];
    return responses[Math.floor(Math.random() * responses.length)];
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    if (!file.type.startsWith('image/')) {
      toast({
        title: "Invalid file type",
        description: "Please upload an image file.",
        variant: "destructive"
      });
      return;
    }

    const reader = new FileReader();
    reader.onload = (event) => {
      const imageUrl = event.target?.result as string;
      const imageMessage: Message = {
        id: Date.now().toString(),
        text: 'Uploaded an image',
        sender: 'user',
        timestamp: new Date(),
        type: 'image',
        imageUrl
      };

      setMessages(prev => [...prev, imageMessage]);

      // AI response to image
      setTimeout(() => {
        const aiResponse: Message = {
          id: (Date.now() + 1).toString(),
          text: "I can see the image you've uploaded! However, I'm currently in demo mode. With a full AI integration, I could analyze the contents, describe what I see, and answer questions about the image.",
          sender: 'ai',
          timestamp: new Date()
        };
        setMessages(prev => [...prev, aiResponse]);
      }, 1000);
    };
    reader.readAsDataURL(file);
  };

  const toggleRecording = () => {
    if (!apiKey) {
      setShowApiKeyInput(true);
      toast({
        title: "ElevenLabs API Key Required",
        description: "Please provide your ElevenLabs API key to use voice chat.",
      });
      return;
    }

    setIsRecording(!isRecording);
    if (!isRecording) {
      toast({
        title: "Voice recording started",
        description: "Speak now, I'm listening...",
      });
    } else {
      toast({
        title: "Voice recording stopped",
        description: "Processing your voice message...",
      });
    }
  };

  const clearChat = () => {
    setMessages([
      {
        id: '1',
        text: 'Hello! I\'m ChatGPT, a large language model by OpenAI. I can help with text, images, voice chat, and complex reasoning. How can I help you today?',
        sender: 'ai',
        timestamp: new Date()
      }
    ]);
  };

  return (
    <div className={cn("h-screen flex bg-background", className)}>
      {/* Sidebar */}
      <div className="w-64 bg-card border-r border-border flex flex-col">
        <div className="p-4 border-b border-border">
          <Button
            onClick={clearChat}
            variant="outline"
            className="w-full justify-start gap-2 mb-3"
          >
            <Plus className="h-4 w-4" />
            New chat
          </Button>
          
          {/* API Key Input */}
          {showApiKeyInput && (
            <div className="space-y-2">
              <Input
                placeholder="ElevenLabs API Key"
                type="password"
                value={apiKey}
                onChange={(e) => setApiKey(e.target.value)}
                className="text-xs"
              />
              <Button
                onClick={() => setShowApiKeyInput(false)}
                size="sm"
                className="w-full"
              >
                Save
              </Button>
            </div>
          )}
        </div>
        
        <div className="flex-1 p-2">
          <div className="space-y-1">
            <div className="px-3 py-2 rounded-lg bg-accent/50 text-sm text-foreground cursor-pointer hover:bg-accent transition-colors">
              Current conversation
            </div>
          </div>
        </div>
        
        <div className="p-4 border-t border-border">
          <div className="flex items-center gap-2 px-3 py-2 rounded-lg hover:bg-accent/50 transition-colors cursor-pointer">
            <div className="h-8 w-8 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-sm font-semibold">
              U
            </div>
            <span className="text-sm text-foreground">User</span>
          </div>
        </div>
      </div>

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <div className="border-b border-border p-4 bg-card/50">
          <div className="flex items-center justify-between">
            <h1 className="text-lg font-semibold text-foreground">ChatGPT</h1>
            <div className="flex items-center gap-2">
              <Button 
                variant="ghost" 
                size="sm"
                onClick={() => setShowApiKeyInput(!showApiKeyInput)}
              >
                🔑
              </Button>
              <Button variant="ghost" size="sm">
                <Menu className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Messages */}
        <div className="flex-1 overflow-y-auto">
          <div className="max-w-3xl mx-auto">
            {messages.map((message) => (
              <div
                key={message.id}
                className={cn(
                  "px-6 py-8 border-b border-border/50 animate-fade-in",
                  message.sender === 'ai' ? 'bg-muted/30' : 'bg-background'
                )}
              >
                <div className="flex gap-6 max-w-full">
                  <div className="flex-shrink-0">
                    <div className={cn(
                      "h-8 w-8 rounded-full flex items-center justify-center text-sm font-semibold",
                      message.sender === 'ai' 
                        ? 'bg-primary text-primary-foreground' 
                        : 'bg-accent text-accent-foreground'
                    )}>
                      {message.sender === 'ai' ? 'AI' : 'U'}
                    </div>
                  </div>
                  <div className="flex-1 min-w-0 space-y-3">
                    {/* Reasoning Animation */}
                    {message.type === 'reasoning' && message.reasoning && (
                      <div className="bg-card border border-border rounded-lg p-4">
                        <div className="text-sm font-medium text-foreground mb-3 flex items-center gap-2">
                          <div className="w-2 h-2 bg-primary rounded-full animate-pulse"></div>
                          Thinking step by step...
                        </div>
                        <div className="space-y-2">
                          {message.reasoning.steps.map((step, index) => (
                            <div 
                              key={index}
                              className={cn(
                                "flex items-center gap-3 text-sm transition-all duration-500",
                                index <= message.reasoning!.currentStep 
                                  ? "text-foreground opacity-100" 
                                  : "text-muted-foreground opacity-50"
                              )}
                            >
                              <div className={cn(
                                "w-2 h-2 rounded-full transition-all duration-300",
                                index < message.reasoning!.currentStep
                                  ? "bg-primary scale-100"
                                  : index === message.reasoning!.currentStep
                                  ? "bg-primary scale-125 animate-pulse"
                                  : "bg-muted scale-75"
                              )}></div>
                              <span className={cn(
                                "transition-all duration-300",
                                index === message.reasoning!.currentStep && "animate-pulse"
                              )}>
                                {step}
                              </span>
                            </div>
                          ))}
                        </div>
                        {message.reasoning.isComplete && (
                          <div className="mt-3 text-xs text-primary font-medium animate-fade-in">
                            ✓ Analysis complete
                          </div>
                        )}
                      </div>
                    )}

                    {/* Image Display */}
                    {message.type === 'image' && message.imageUrl && (
                      <div className="max-w-md">
                        <img 
                          src={message.imageUrl} 
                          alt="Uploaded image" 
                          className="rounded-lg shadow-sm max-w-full h-auto border border-border"
                        />
                      </div>
                    )}

                    {/* Text Content */}
                    {message.text && message.type !== 'reasoning' && (
                      <div className="text-foreground leading-7 whitespace-pre-wrap break-words">
                        {message.text}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
            
            {isTyping && (
              <div className="px-6 py-8 border-b border-border/50 bg-muted/30 animate-fade-in">
                <div className="flex gap-6 max-w-full">
                  <div className="flex-shrink-0">
                    <div className="h-8 w-8 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-sm font-semibold">
                      AI
                    </div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex gap-1">
                      <div className="w-2 h-2 bg-primary/60 rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 bg-primary/60 rounded-full animate-bounce [animation-delay:0.1s]"></div>
                      <div className="w-2 h-2 bg-primary/60 rounded-full animate-bounce [animation-delay:0.2s]"></div>
                    </div>
                  </div>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>
        </div>

        {/* Input */}
        <div className="p-4 bg-background border-t border-border">
          <div className="max-w-3xl mx-auto">
            <div className="relative">
              <Input
                ref={inputRef}
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Message ChatGPT..."
                className="w-full pr-32 pl-12 rounded-xl border-border/40 bg-muted/30 text-foreground placeholder:text-muted-foreground focus:ring-primary/50 min-h-[52px] py-3"
              />
              
              {/* Attachment Button */}
              <Button
                onClick={() => fileInputRef.current?.click()}
                variant="ghost"
                size="sm"
                className="absolute left-2 top-1/2 -translate-y-1/2 h-8 w-8 p-0 rounded-lg"
              >
                <Paperclip className="h-4 w-4" />
              </Button>

              {/* Voice Button */}
              <Button
                onClick={toggleRecording}
                variant="ghost"
                size="sm"
                className={cn(
                  "absolute right-16 top-1/2 -translate-y-1/2 h-8 w-8 p-0 rounded-lg",
                  isRecording && "bg-destructive text-destructive-foreground animate-pulse"
                )}
              >
                {isRecording ? <MicOff className="h-4 w-4" /> : <Mic className="h-4 w-4" />}
              </Button>

              {/* Send Button */}
              <Button
                onClick={handleSend}
                disabled={!input.trim()}
                size="sm"
                className="absolute right-2 top-1/2 -translate-y-1/2 h-8 w-8 p-0 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Send className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Hidden file input */}
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleImageUpload}
          className="hidden"
        />
      </div>
    </div>
  );
}