#!/usr/bin/env python3
"""
Unified AI Bridge - Connects Agent-Zero and AgenticSeek
This bridge allows both systems to work together seamlessly.
"""

import os
import sys
import asyncio
import threading
import time
import json
import requests
from typing import Dict, Any, Optional, List, List
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor
import uvicorn
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

# Add paths for both systems
sys.path.append(os.path.join(os.getcwd(), 'agent-zero-main'))
sys.path.append(os.path.join(os.getcwd(), 'agenticSeek-main'))

@dataclass
class SystemStatus:
    name: str
    status: str
    port: int
    health_endpoint: str
    last_check: float
    error_message: Optional[str] = None

class UnifiedRequest(BaseModel):
    query: str
    system_preference: Optional[str] = None  # 'agent-zero', 'agentic-seek', or 'auto'
    context: Optional[Dict[str, Any]] = None
    session_id: Optional[str] = None

class UnifiedResponse(BaseModel):
    success: bool
    response: str
    system_used: str
    reasoning: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    session_id: Optional[str] = None

class UnifiedAIBridge:
    def __init__(self):
        self.app = FastAPI(title="Unified AI Bridge", version="1.0.0")
        self.systems = {
            'agent-zero': SystemStatus(
                name='Agent-Zero',
                status='unknown',
                port=50001,
                health_endpoint='/',
                last_check=0
            ),
            'agentic-seek': SystemStatus(
                name='AgenticSeek',
                status='unknown',
                port=7777,
                health_endpoint='/health',
                last_check=0
            ),
            'crew-ai': SystemStatus(
                name='CrewAI',
                status='unknown',
                port=8001,
                health_endpoint='/health',
                last_check=0
            ),
            'ten-framework': SystemStatus(
                name='Ten Framework',
                status='unknown',
                port=8002,
                health_endpoint='/health',
                last_check=0
            )
        }
        self.executor = ThreadPoolExecutor(max_workers=4)
        self.setup_middleware()
        self.setup_routes()
        
    def setup_middleware(self):
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
    
    def setup_routes(self):
        @self.app.get("/")
        async def root():
            return {"message": "Unified AI Bridge is running", "systems": self.get_systems_status()}
        
        @self.app.get("/health")
        async def health():
            return {"status": "healthy", "systems": self.get_systems_status()}
        
        @self.app.post("/query", response_model=UnifiedResponse)
        async def unified_query(request: UnifiedRequest, background_tasks: BackgroundTasks):
            return await self.process_unified_query(request)
        
        @self.app.get("/systems/status")
        async def systems_status():
            await self.check_all_systems()
            return self.get_systems_status()
        
        @self.app.post("/systems/start")
        async def start_systems(background_tasks: BackgroundTasks):
            background_tasks.add_task(self.start_all_systems)
            return {"message": "Starting all systems..."}
    
    async def check_system_health(self, system_key: str) -> bool:
        """Check if a system is healthy"""
        system = self.systems[system_key]
        try:
            response = requests.get(
                f"http://localhost:{system.port}{system.health_endpoint}",
                timeout=5
            )
            system.last_check = time.time()
            if response.status_code == 200:
                system.status = 'healthy'
                system.error_message = None
                return True
            else:
                system.status = 'unhealthy'
                system.error_message = f"HTTP {response.status_code}"
                return False
        except Exception as e:
            system.status = 'unhealthy'
            system.error_message = str(e)
            system.last_check = time.time()
            return False
    
    async def check_all_systems(self):
        """Check health of all systems"""
        tasks = [self.check_system_health(key) for key in self.systems.keys()]
        await asyncio.gather(*tasks, return_exceptions=True)
    
    def get_systems_status(self) -> Dict[str, Any]:
        """Get status of all systems"""
        return {
            key: {
                'name': system.name,
                'status': system.status,
                'port': system.port,
                'last_check': system.last_check,
                'error': system.error_message
            }
            for key, system in self.systems.items()
        }
    
    async def route_to_agent_zero(self, query: str, context: Optional[Dict] = None) -> Dict[str, Any]:
        """Route query to Agent-Zero"""
        try:
            payload = {
                'message': query,
                'context': context or {}
            }
            response = requests.post(
                f"http://localhost:50001/api/message",
                json=payload,
                timeout=30
            )
            if response.status_code == 200:
                return {
                    'success': True,
                    'response': response.json().get('response', ''),
                    'system_used': 'agent-zero',
                    'metadata': response.json()
                }
            else:
                return {
                    'success': False,
                    'response': f"Agent-Zero error: HTTP {response.status_code}",
                    'system_used': 'agent-zero',
                    'metadata': {'error': f"HTTP {response.status_code}", 'response_text': response.text}
                }
        except Exception as e:
            return {
                'success': False,
                'response': f"Agent-Zero error: {str(e)}",
                'system_used': 'agent-zero',
                'metadata': {'error': str(e)}
            }
    
    async def route_to_agentic_seek(self, query: str, context: Optional[Dict] = None) -> Dict[str, Any]:
        """Route query to AgenticSeek"""
        try:
            payload = {
                'query': query,
                'tts_enabled': False
            }
            response = requests.post(
                f"http://localhost:7777/query",
                json=payload,
                timeout=30
            )
            if response.status_code == 200:
                data = response.json()
                return {
                    'success': True,
                    'response': data.get('answer', ''),
                    'system_used': 'agentic-seek',
                    'reasoning': data.get('reasoning', ''),
                    'metadata': data
                }
            else:
                return {
                    'success': False,
                    'response': f"AgenticSeek error: HTTP {response.status_code}",
                    'system_used': 'agentic-seek',
                    'metadata': {'error': f"HTTP {response.status_code}", 'response_text': response.text}
                }
        except Exception as e:
            return {
                'success': False,
                'response': f"AgenticSeek error: {str(e)}",
                'system_used': 'agentic-seek',
                'metadata': {'error': str(e)}
            }
    
    async def route_to_crew_ai(self, query: str, context: Optional[Dict] = None) -> Dict[str, Any]:
        """Route query to CrewAI"""
        try:
            # Create a proper crew configuration matching the expected schema
            payload = {
                'name': f'Query Analysis Crew - {query[:50]}...' if len(query) > 50 else f'Query Analysis Crew - {query}',
                'agents': [
                    {
                        'role': 'Research Analyst',
                        'goal': 'Analyze and provide comprehensive insights',
                        'backstory': 'Expert analyst with deep research capabilities',
                        'tools': [],
                        'llm': {
                            'model': 'gpt-3.5-turbo',
                            'temperature': 0.7
                        }
                    },
                    {
                        'role': 'Content Writer',
                        'goal': 'Create clear and engaging responses',
                        'backstory': 'Professional writer skilled in clear communication',
                        'tools': [],
                        'llm': {
                            'model': 'gpt-3.5-turbo',
                            'temperature': 0.7
                        }
                    }
                ],
                'tasks': [
                    {
                        'description': f'Analyze this request: {query}',
                        'agent_role': 'Research Analyst',
                        'expected_output': 'A detailed analysis of the request with key insights and findings',
                        'tools': [],
                        'context': []
                    },
                    {
                        'description': 'Create a comprehensive response based on the analysis',
                        'agent_role': 'Content Writer',
                        'expected_output': 'A clear, well-structured response that addresses the original query',
                        'tools': [],
                        'context': []
                    }
                ],
                'process': 'sequential',
                'verbose': True,
                'memory': False
            }
            response = requests.post(
                f"http://localhost:8001/crew/execute",
                json=payload,
                timeout=60
            )
            if response.status_code == 200:
                data = response.json()
                return {
                    'success': True,
                    'response': data.get('result', ''),
                    'system_used': 'crew-ai',
                    'metadata': data
                }
            else:
                return {
                    'success': False,
                    'response': f"CrewAI error: HTTP {response.status_code}",
                    'system_used': 'crew-ai',
                    'metadata': {'error': f"HTTP {response.status_code}", 'response_text': response.text}
                }
        except Exception as e:
            return {
                'success': False,
                'response': f"CrewAI error: {str(e)}",
                'system_used': 'crew-ai',
                'metadata': {'error': str(e)}
            }
    
    async def route_to_ten_framework(self, query: str, context: Optional[Dict] = None) -> Dict[str, Any]:
        """Route query to Ten Framework"""
        try:
            # Create a session first with proper SessionConfig structure
            session_payload = {
                'session_id': None,
                'user_id': None,
                'language': 'en-US',
                'voice_enabled': False,  # Disable voice for text-only queries
                'video_enabled': False,
                'real_time_processing': True,
                'context_memory': True,
                'integration_services': ['agent_zero', 'agentic_seek']
            }
            session_response = requests.post(
                f"http://localhost:8002/session/create",
                json=session_payload,
                timeout=10
            )

            if session_response.status_code == 200:
                session_data = session_response.json()
                session_id = session_data.get('session_id')

                # Process the query with proper MediaInput structure
                media_input = {
                    'type': 'text',
                    'content': query,
                    'metadata': context or {},
                    'timestamp': None
                }

                process_response = requests.post(
                    f"http://localhost:8002/session/{session_id}/process",
                    json=media_input,
                    timeout=30
                )
                
                if process_response.status_code == 200:
                    data = process_response.json()
                    return {
                        'success': True,
                        'response': data.get('response', ''),
                        'system_used': 'ten-framework',
                        'session_id': session_id,
                        'metadata': data
                    }
                else:
                    return {
                        'success': False,
                        'response': f"Ten Framework process error: HTTP {process_response.status_code}",
                        'system_used': 'ten-framework',
                        'metadata': {'error': f"Process HTTP {process_response.status_code}", 'response_text': process_response.text}
                    }
            else:
                return {
                    'success': False,
                    'response': f"Ten Framework session error: HTTP {session_response.status_code}",
                    'system_used': 'ten-framework',
                    'metadata': {'error': f"Session HTTP {session_response.status_code}", 'response_text': session_response.text}
                }
        except Exception as e:
            return {
                'success': False,
                'response': f"Ten Framework error: {str(e)}",
                'system_used': 'ten-framework',
                'metadata': {'error': str(e)}
            }
    
    def determine_best_system(self, query: str, preference: Optional[str] = None) -> str:
        """Determine which system to use based on query content"""
        if preference and preference in self.systems:
            return preference
        
        # Enhanced routing logic based on query content
        query_lower = query.lower()
        
        # PRIORITY 1: Agent-Zero strong patterns (check first to avoid conflicts)
        agent_zero_strong_patterns = [
            'create a python script', 'write a script', 'generate code', 'build a program',
            'create a function', 'write a function', 'make a script', 'python code',
            'javascript code', 'html code', 'css code', 'sql query', 'create file',
            'write file', 'save file', 'modify file', 'debug code', 'fix code',
            'install package', 'run command', 'execute command', 'terminal command',
            'create a', 'write a', 'generate a', 'build a', 'make a'
        ]
        if any(pattern in query_lower for pattern in agent_zero_strong_patterns):
            return 'agent-zero'

        # PRIORITY 2: Ten Framework for real-time, voice, video, multimodal
        ten_keywords = ['voice', 'audio', 'video', 'real-time', 'streaming', 'conversation',
                       'multimodal', 'live', 'interactive', 'session', 'speak', 'listen',
                       'watch', 'camera', 'microphone']
        if any(keyword in query_lower for keyword in ten_keywords):
            return 'ten-framework'

        # PRIORITY 3: CrewAI for complex workflows, team coordination, multi-step tasks
        crew_keywords = ['crew', 'team', 'collaborate', 'workflow', 'orchestrate', 'delegate',
                        'coordinate', 'multi-agent', 'sequential', 'hierarchical', 'complex task',
                        'multiple steps', 'project', 'research project', 'analysis']
        if any(keyword in query_lower for keyword in crew_keywords):
            return 'crew-ai'

        # PRIORITY 4: AgenticSeek for web search, research, browsing
        agentic_keywords = ['search', 'browse', 'web', 'internet', 'research', 'find online',
                           'look up', 'website', 'url', 'google', 'information', 'news',
                           'current', 'latest', 'online']
        if any(keyword in query_lower for keyword in agentic_keywords):
            return 'agentic-seek'

        # PRIORITY 5: Agent-Zero for coding, file operations, system tasks (basic keywords)
        agent_zero_keywords = ['code', 'file', 'script', 'program', 'execute', 'run',
                              'terminal', 'command', 'python', 'javascript', 'programming']
        if any(keyword in query_lower for keyword in agent_zero_keywords):
            return 'agent-zero'
        
        # Default to Agent-Zero for general queries
        return 'agent-zero'

    async def _try_system(self, system_name: str, query: str, context: Optional[Dict] = None) -> Optional[Dict[str, Any]]:
        """Try to route query to a specific system"""
        try:
            if system_name == 'agent-zero':
                return await self.route_to_agent_zero(query, context)
            elif system_name == 'agentic-seek':
                return await self.route_to_agentic_seek(query, context)
            elif system_name == 'crew-ai':
                return await self.route_to_crew_ai(query, context)
            elif system_name == 'ten-framework':
                return await self.route_to_ten_framework(query, context)
            else:
                return None
        except Exception as e:
            return {
                'success': False,
                'response': f"System {system_name} error: {str(e)}",
                'system_used': system_name,
                'metadata': {'error': str(e)}
            }

    def _get_fallback_systems(self, primary_system: str, attempted_systems: List[str]) -> List[str]:
        """Get fallback systems based on the primary system that failed"""
        all_systems = ['agent-zero', 'agentic-seek', 'crew-ai', 'ten-framework']

        # Define intelligent fallback chains
        fallback_chains = {
            'agentic-seek': ['agent-zero', 'crew-ai'],  # If search fails, try general AI or analysis
            'crew-ai': ['agent-zero', 'agentic-seek'],  # If complex task fails, try general AI or search
            'ten-framework': ['agent-zero'],  # If multimodal fails, try general AI
            'agent-zero': ['agentic-seek', 'crew-ai']  # If general AI fails, try search or analysis
        }

        # Get fallback chain for primary system
        fallbacks = fallback_chains.get(primary_system, [])

        # Add any remaining healthy systems not yet attempted
        for system in all_systems:
            if system not in attempted_systems and system not in fallbacks:
                fallbacks.append(system)

        # Filter out unhealthy systems
        healthy_fallbacks = []
        for system in fallbacks:
            if system in self.systems and self.systems[system].status == 'healthy':
                healthy_fallbacks.append(system)

        return healthy_fallbacks

    async def process_unified_query(self, request: UnifiedRequest) -> UnifiedResponse:
        """Process a unified query through the appropriate system"""
        # Check system health first
        await self.check_all_systems()
        
        # Determine which system to use
        target_system = self.determine_best_system(request.query, request.system_preference)
        
        # Check if target system is healthy, find alternative if needed
        if self.systems[target_system].status != 'healthy':
            # Try to find a healthy alternative system
            healthy_systems = [key for key, system in self.systems.items() 
                             if system.status == 'healthy']
            if healthy_systems:
                target_system = healthy_systems[0]
            else:
                raise HTTPException(status_code=503, detail="No healthy systems available")
        
        # Route to appropriate system with intelligent fallback
        result = None
        attempted_systems = []

        # Try primary system
        result = await self._try_system(target_system, request.query, request.context)
        attempted_systems.append(target_system)

        # If primary system failed with rate limiting or other issues, try fallbacks
        if not result or not result.get('success'):
            fallback_systems = self._get_fallback_systems(target_system, attempted_systems)

            for fallback_system in fallback_systems:
                if fallback_system not in attempted_systems:
                    fallback_result = await self._try_system(fallback_system, request.query, request.context)
                    attempted_systems.append(fallback_system)

                    if fallback_result and fallback_result.get('success'):
                        result = fallback_result
                        result['fallback_used'] = True
                        result['original_system'] = target_system
                        result['attempted_systems'] = attempted_systems
                        break

        if result is None:
            raise HTTPException(status_code=500, detail=f"All systems failed. Attempted: {attempted_systems}")

        # Ensure we have a valid response string
        response_text = result.get('response', 'No response available')
        if response_text is None:
            response_text = 'System returned empty response'

        return UnifiedResponse(
            success=result['success'],
            response=response_text,
            system_used=result['system_used'],
            reasoning=result.get('reasoning'),
            metadata=result.get('metadata'),
            session_id=request.session_id
        )
    
    def start_all_systems(self):
        """Start all AI systems"""
        def start_agent_zero():
            os.chdir('agent-zero-main')
            os.system('python run_ui.py')
        
        def start_agentic_seek():
            os.chdir('agenticSeek-main')
            os.system('python api.py')
        
        def start_crew_ai():
            os.system('python crew_service.py')
        
        def start_ten_framework():
            os.system('python ten_service.py')
        
        # Start systems in separate threads with delays
        threading.Thread(target=start_agent_zero, daemon=True).start()
        time.sleep(2)  # Give Agent-Zero time to start
        threading.Thread(target=start_agentic_seek, daemon=True).start()
        time.sleep(1)
        threading.Thread(target=start_crew_ai, daemon=True).start()
        time.sleep(1)
        threading.Thread(target=start_ten_framework, daemon=True).start()
    
    def run(self, host: str = "0.0.0.0", port: int = 9000):
        """Run the unified bridge"""
        print(f"Starting Unified AI Bridge on http://{host}:{port}")
        print("Connecting multiple AI systems:")
        print("   - Agent-Zero (port 50001)")
        print("   - AgenticSeek (port 8000)")
        print("   - CrewAI (port 8001)")
        print("   - Ten Framework (port 8002)")
        uvicorn.run(self.app, host=host, port=port)

if __name__ == "__main__":
    bridge = UnifiedAIBridge()
    bridge.run()