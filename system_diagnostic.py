#!/usr/bin/env python3
"""
Comprehensive system diagnostic to identify current issues
"""

import requests
import json
import time
import subprocess
import os
from pathlib import Path

def check_ollama_status():
    """Check Ollama server and models"""
    print("🤖 Checking Ollama Status")
    print("=" * 40)
    
    try:
        # Check if Ollama is running
        response = requests.get("http://localhost:11434/api/tags", timeout=10)
        if response.status_code == 200:
            models = response.json()
            print(f"✅ Ollama server running")
            print(f"📊 Models available: {len(models.get('models', []))}")
            
            # List key models
            model_names = [m['name'] for m in models.get('models', [])]
            required_models = [
                'deepseek-r1:14b', 'llama3.2:latest', 'qwen2.5vl:32b', 
                'nomic-embed-text:latest', 'magistral:24b'
            ]
            
            print(f"🔍 Required models status:")
            for model in required_models:
                status = "✅" if model in model_names else "❌"
                print(f"   {status} {model}")
            
            return True, model_names
        else:
            print(f"❌ Ollama server error: {response.status_code}")
            return False, []
    except Exception as e:
        print(f"❌ Ollama connection failed: {e}")
        return False, []

def check_unified_agent_health():
    """Check unified agent health and capabilities"""
    print("\n🎯 Checking Unified Agent Health")
    print("=" * 40)
    
    base_url = "http://localhost:9001"
    
    try:
        # Health check
        response = requests.get(f"{base_url}/health", timeout=10)
        if response.status_code == 200:
            health = response.json()
            print(f"✅ Agent health: {health.get('status', 'unknown')}")
            print(f"🤖 Ollama connected: {health.get('ollama', False)}")
            print(f"📊 Models loaded: {health.get('models_loaded', 0)}")
            print(f"🛠️ Capabilities: {health.get('capabilities', 0)}")
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
        
        # System info
        response = requests.get(f"{base_url}/", timeout=10)
        if response.status_code == 200:
            info = response.json()
            capabilities = info.get('capabilities', [])
            print(f"\n📋 Available capabilities ({len(capabilities)}):")
            for cap in capabilities:
                print(f"   ✅ {cap}")
            return True
        else:
            print(f"❌ System info failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Agent connection failed: {e}")
        return False

def test_capability_performance():
    """Test each capability for performance issues"""
    print("\n⚡ Testing Capability Performance")
    print("=" * 40)
    
    base_url = "http://localhost:9001"
    
    test_cases = [
        ("code_generation", "Write hello world", 30),
        ("web_search", "Search for AI", 60),
        ("voice_processing", "Say hello", 30),
        ("multimodal_processing", "Analyze images", 30),
        ("task_planning", "Plan a task", 60),
        ("autonomous_execution", "Execute task", 60)
    ]
    
    issues = []
    
    for capability, message, timeout in test_cases:
        print(f"\n🧪 Testing {capability}...")
        
        try:
            payload = {"message": message, "capability": capability}
            start_time = time.time()
            
            response = requests.post(
                f"{base_url}/chat", 
                json=payload, 
                timeout=timeout
            )
            
            end_time = time.time()
            response_time = end_time - start_time
            
            if response.status_code == 200:
                if response_time > timeout * 0.8:  # If taking >80% of timeout
                    print(f"⚠️ {capability}: SLOW ({response_time:.1f}s)")
                    issues.append(f"{capability} is slow ({response_time:.1f}s)")
                else:
                    print(f"✅ {capability}: OK ({response_time:.1f}s)")
            else:
                print(f"❌ {capability}: FAILED (HTTP {response.status_code})")
                issues.append(f"{capability} failed with HTTP {response.status_code}")
                
        except requests.exceptions.Timeout:
            print(f"⏰ {capability}: TIMEOUT (>{timeout}s)")
            issues.append(f"{capability} timeout after {timeout}s")
        except Exception as e:
            print(f"❌ {capability}: ERROR ({str(e)[:50]})")
            issues.append(f"{capability} error: {str(e)[:50]}")
    
    return issues

def check_file_system_issues():
    """Check for file system and dependency issues"""
    print("\n📁 Checking File System Issues")
    print("=" * 40)
    
    issues = []
    
    # Check key files exist
    key_files = [
        'unified_local_agent.py',
        'unified_local_config.json',
        'local_voice_processor.py',
        'local_web_search.py',
        'local_multimodal_processor.py'
    ]
    
    for file in key_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} MISSING")
            issues.append(f"Missing file: {file}")
    
    # Check for Python import issues
    print(f"\n🐍 Checking Python Dependencies...")
    
    try:
        import whisper
        print(f"✅ whisper")
    except ImportError:
        print(f"❌ whisper")
        issues.append("Missing dependency: whisper")
    
    try:
        import requests
        print(f"✅ requests")
    except ImportError:
        print(f"❌ requests")
        issues.append("Missing dependency: requests")
    
    try:
        from PIL import Image
        print(f"✅ PIL")
    except ImportError:
        print(f"❌ PIL")
        issues.append("Missing dependency: PIL")
    
    try:
        import numpy
        print(f"✅ numpy")
    except ImportError:
        print(f"❌ numpy")
        issues.append("Missing dependency: numpy")
    
    return issues

def check_process_issues():
    """Check for process and resource issues"""
    print("\n⚙️ Checking Process Issues")
    print("=" * 40)
    
    issues = []
    
    try:
        # Check if multiple instances running
        result = subprocess.run(
            ['tasklist', '/FI', 'IMAGENAME eq python.exe'],
            capture_output=True, text=True, shell=True
        )
        
        python_processes = result.stdout.count('python.exe')
        print(f"🐍 Python processes running: {python_processes}")
        
        if python_processes > 10:
            issues.append(f"Too many Python processes ({python_processes})")
        
        # Check Ollama processes
        result = subprocess.run(
            ['tasklist', '/FI', 'IMAGENAME eq ollama.exe'],
            capture_output=True, text=True, shell=True
        )
        
        ollama_processes = result.stdout.count('ollama.exe')
        print(f"🤖 Ollama processes running: {ollama_processes}")
        
        if ollama_processes == 0:
            issues.append("Ollama not running")
        elif ollama_processes > 3:
            issues.append(f"Too many Ollama processes ({ollama_processes})")
        
    except Exception as e:
        print(f"❌ Process check failed: {e}")
        issues.append(f"Process check error: {e}")
    
    return issues

def main():
    """Run comprehensive system diagnostic"""
    print("🔍 COMPREHENSIVE SYSTEM DIAGNOSTIC")
    print("=" * 60)
    
    all_issues = []
    
    # Check Ollama
    ollama_ok, models = check_ollama_status()
    if not ollama_ok:
        all_issues.append("Ollama server not responding")
    
    # Check unified agent
    agent_ok = check_unified_agent_health()
    if not agent_ok:
        all_issues.append("Unified agent not responding")
    
    # Test performance
    if agent_ok:
        perf_issues = test_capability_performance()
        all_issues.extend(perf_issues)
    
    # Check file system
    fs_issues = check_file_system_issues()
    all_issues.extend(fs_issues)
    
    # Check processes
    proc_issues = check_process_issues()
    all_issues.extend(proc_issues)
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 DIAGNOSTIC SUMMARY")
    print("=" * 60)
    
    if not all_issues:
        print("🎉 No issues found! System appears healthy.")
    else:
        print(f"⚠️ Found {len(all_issues)} issues:")
        for i, issue in enumerate(all_issues, 1):
            print(f"   {i}. {issue}")
    
    print(f"\n🔧 RECOMMENDATIONS:")
    if "Ollama server not responding" in all_issues:
        print("   • Restart Ollama server")
    if "Unified agent not responding" in all_issues:
        print("   • Restart unified agent")
    if any("slow" in issue.lower() for issue in all_issues):
        print("   • Optimize model selection for performance")
    if any("timeout" in issue.lower() for issue in all_issues):
        print("   • Increase timeout values or optimize models")
    if any("missing" in issue.lower() for issue in all_issues):
        print("   • Install missing dependencies")
    
    return all_issues

if __name__ == "__main__":
    issues = main()
