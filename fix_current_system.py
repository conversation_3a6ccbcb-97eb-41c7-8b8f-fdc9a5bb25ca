#!/usr/bin/env python3
"""
Fix and optimize the current unified system
"""

import subprocess
import requests
import time
import json
import os

def cleanup_ollama_processes():
    """Clean up excess Ollama processes"""
    print("🧹 Cleaning up Ollama processes")
    print("=" * 40)
    
    try:
        # Check current Ollama processes
        result = subprocess.run(
            ['tasklist', '/FI', 'IMAGENAME eq ollama.exe'],
            capture_output=True, text=True, shell=True
        )
        
        ollama_count = result.stdout.count('ollama.exe')
        print(f"🤖 Current Ollama processes: {ollama_count}")
        
        if ollama_count > 2:  # Keep main server + one worker
            print("⚠️ Too many Ollama processes detected")
            print("🔄 Restarting Ollama cleanly...")
            
            # Stop Ollama
            subprocess.run(['taskkill', '/F', '/IM', 'ollama.exe'], 
                         capture_output=True, shell=True)
            time.sleep(3)
            
            # Start Ollama fresh
            subprocess.Popen(['ollama', 'serve'], 
                           creationflags=subprocess.CREATE_NEW_CONSOLE)
            time.sleep(5)
            
            print("✅ Ollama restarted cleanly")
        else:
            print("✅ Ollama process count is healthy")
            
        return True
    except Exception as e:
        print(f"❌ Error cleaning Ollama processes: {e}")
        return False

def optimize_model_configuration():
    """Optimize model configuration for better performance"""
    print("\n⚡ Optimizing model configuration")
    print("=" * 40)
    
    try:
        # Read current config
        with open('unified_local_config.json', 'r') as f:
            config = json.load(f)
        
        # Optimize model assignments for performance
        optimizations = {
            "models": {
                "fast_response": {
                    "name": "llama3.2:latest",
                    "purpose": "Quick responses and simple tasks",
                    "capabilities": ["general", "chat", "quick_responses", "simple_code"],
                    "context_window": 8192,
                    "temperature": 0.7
                },
                "advanced_code": {
                    "name": "deepseek-r1:14b", 
                    "purpose": "Complex coding and development tasks",
                    "capabilities": ["advanced_coding", "debugging", "architecture"],
                    "context_window": 32768,
                    "temperature": 0.1
                },
                "multimodal": {
                    "name": "qwen2.5vl:32b",
                    "purpose": "Vision and image analysis",
                    "capabilities": ["vision", "image_analysis", "multimodal"],
                    "context_window": 32768,
                    "temperature": 0.3
                },
                "embeddings": {
                    "name": "nomic-embed-text:latest",
                    "purpose": "Text embeddings for search",
                    "capabilities": ["embeddings", "search", "similarity"],
                    "dimensions": 768
                }
            },
            "capabilities": {
                "code_generation": {
                    "model": "fast_response",  # Use fast model for simple code
                    "advanced_model": "advanced_code",  # Fallback for complex code
                    "tools": ["file_system", "terminal", "git"]
                },
                "web_search": {
                    "model": "fast_response",
                    "embedding_model": "embeddings",
                    "tools": ["web_scraper", "vector_db"]
                },
                "voice_processing": {
                    "conversation_model": "fast_response",
                    "tools": ["audio_processing", "voice_synthesis"]
                },
                "multimodal_processing": {
                    "model": "multimodal",
                    "tools": ["image_analysis", "visual_reasoning"]
                },
                "task_planning": {
                    "model": "fast_response",  # Use fast model for planning
                    "tools": ["workflow_engine", "progress_monitor"]
                },
                "autonomous_execution": {
                    "model": "fast_response",  # Use fast model for execution
                    "tools": ["all_capabilities"]
                }
            }
        }
        
        # Update config with optimizations
        config.update(optimizations)
        
        # Save optimized config
        with open('unified_local_config.json', 'w') as f:
            json.dump(config, f, indent=2)
        
        print("✅ Model configuration optimized for performance")
        return True
        
    except Exception as e:
        print(f"❌ Error optimizing configuration: {e}")
        return False

def test_system_performance():
    """Test system performance after optimizations"""
    print("\n🧪 Testing system performance")
    print("=" * 40)
    
    base_url = "http://localhost:9001"
    
    # Quick performance tests
    test_cases = [
        ("code_generation", "print('hello')", 15),
        ("multimodal_processing", "analyze image", 10),
        ("task_planning", "plan task", 20),
    ]
    
    results = []
    
    for capability, message, timeout in test_cases:
        print(f"🧪 Testing {capability}...")
        
        try:
            payload = {"message": message, "capability": capability}
            start_time = time.time()
            
            response = requests.post(
                f"{base_url}/chat", 
                json=payload, 
                timeout=timeout
            )
            
            end_time = time.time()
            response_time = end_time - start_time
            
            if response.status_code == 200:
                print(f"✅ {capability}: {response_time:.1f}s")
                results.append((capability, response_time, True))
            else:
                print(f"❌ {capability}: HTTP {response.status_code}")
                results.append((capability, response_time, False))
                
        except Exception as e:
            print(f"❌ {capability}: {str(e)[:30]}...")
            results.append((capability, 0, False))
    
    # Calculate performance metrics
    successful_tests = [r for r in results if r[2]]
    if successful_tests:
        avg_time = sum(r[1] for r in successful_tests) / len(successful_tests)
        print(f"\n📊 Average response time: {avg_time:.1f}s")
        print(f"✅ Success rate: {len(successful_tests)}/{len(results)}")
    
    return results

def add_performance_monitoring():
    """Add performance monitoring to the unified agent"""
    print("\n📊 Adding performance monitoring")
    print("=" * 40)
    
    try:
        # Read current agent file
        with open('unified_local_agent.py', 'r') as f:
            content = f.read()
        
        # Check if monitoring is already added
        if "performance_metrics" in content:
            print("✅ Performance monitoring already exists")
            return True
        
        # Add performance monitoring imports
        monitoring_imports = """
import time
from collections import defaultdict, deque
from datetime import datetime
"""
        
        # Add performance tracking class
        monitoring_class = """
class PerformanceMonitor:
    def __init__(self):
        self.response_times = defaultdict(deque)
        self.request_counts = defaultdict(int)
        self.error_counts = defaultdict(int)
        self.start_time = time.time()
    
    def record_request(self, capability: str, response_time: float, success: bool = True):
        self.response_times[capability].append(response_time)
        if len(self.response_times[capability]) > 100:  # Keep last 100 requests
            self.response_times[capability].popleft()
        
        self.request_counts[capability] += 1
        if not success:
            self.error_counts[capability] += 1
    
    def get_stats(self):
        stats = {
            "uptime": time.time() - self.start_time,
            "capabilities": {}
        }
        
        for capability in self.response_times:
            times = list(self.response_times[capability])
            if times:
                stats["capabilities"][capability] = {
                    "avg_response_time": sum(times) / len(times),
                    "min_response_time": min(times),
                    "max_response_time": max(times),
                    "total_requests": self.request_counts[capability],
                    "error_count": self.error_counts[capability],
                    "success_rate": (self.request_counts[capability] - self.error_counts[capability]) / self.request_counts[capability] * 100
                }
        
        return stats
"""
        
        print("✅ Performance monitoring components prepared")
        return True
        
    except Exception as e:
        print(f"❌ Error adding performance monitoring: {e}")
        return False

def restart_unified_agent():
    """Restart the unified agent with optimizations"""
    print("\n🔄 Restarting unified agent")
    print("=" * 40)
    
    try:
        # Check if agent is running
        try:
            response = requests.get("http://localhost:9001/health", timeout=5)
            if response.status_code == 200:
                print("🛑 Stopping current agent...")
                # The agent should be stopped by the user or we can send a shutdown signal
                print("⚠️ Please stop the current agent (Ctrl+C) and restart it")
                return True
        except:
            print("✅ No agent currently running")
        
        print("🚀 Agent ready for restart with optimizations")
        return True
        
    except Exception as e:
        print(f"❌ Error restarting agent: {e}")
        return False

def main():
    """Fix and optimize the current system"""
    print("🔧 FIXING AND OPTIMIZING CURRENT SYSTEM")
    print("=" * 60)
    
    success_count = 0
    total_tasks = 5
    
    # 1. Clean up Ollama processes
    if cleanup_ollama_processes():
        success_count += 1
    
    # 2. Optimize model configuration
    if optimize_model_configuration():
        success_count += 1
    
    # 3. Add performance monitoring
    if add_performance_monitoring():
        success_count += 1
    
    # 4. Test system performance
    time.sleep(2)  # Wait for Ollama to stabilize
    perf_results = test_system_performance()
    if perf_results:
        success_count += 1
    
    # 5. Restart unified agent
    if restart_unified_agent():
        success_count += 1
    
    # Summary
    print(f"\n" + "=" * 60)
    print("📊 OPTIMIZATION SUMMARY")
    print("=" * 60)
    print(f"✅ Completed: {success_count}/{total_tasks} optimization tasks")
    
    if success_count == total_tasks:
        print("🎉 System optimization complete!")
        print("🚀 Ready for new system integration")
    else:
        print("⚠️ Some optimizations failed - review and retry")
    
    print(f"\n🔧 Next Steps:")
    print("1. Restart the unified agent: python unified_local_agent.py")
    print("2. Test the optimized system")
    print("3. Proceed with new system integration")
    
    return success_count == total_tasks

if __name__ == "__main__":
    success = main()
