#!/usr/bin/env python3
"""
Test script for the Unified Local AI Agent
"""

import requests
import json
import time

def test_agent():
    """Test the unified agent with different capabilities"""
    base_url = "http://localhost:9001"
    
    # Test health endpoint
    print("🔍 Testing health endpoint...")
    try:
        response = requests.get(f"{base_url}/health", timeout=10)
        print(f"Health Status: {response.json()}")
    except Exception as e:
        print(f"Health check failed: {e}")
        return
    
    # Test cases
    test_cases = [
        {
            "name": "General Chat",
            "message": "Hello! How are you?",
            "capability": "general_chat"
        },
        {
            "name": "Code Generation", 
            "message": "write a simple python function to add two numbers",
            "capability": "code_generation"
        },
        {
            "name": "Task Planning",
            "message": "help me plan a project to build a web application",
            "capability": "task_planning"
        },
        {
            "name": "Web Search",
            "message": "find information about artificial intelligence",
            "capability": "web_search"
        }
    ]
    
    for test_case in test_cases:
        print(f"\n🧪 Testing: {test_case['name']}")
        print(f"📝 Message: {test_case['message']}")
        
        try:
            payload = {
                "message": test_case["message"],
                "capability": test_case["capability"]
            }
            
            start_time = time.time()
            response = requests.post(
                f"{base_url}/chat",
                json=payload,
                timeout=30
            )
            end_time = time.time()
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Success! ({end_time - start_time:.2f}s)")
                print(f"🤖 Model Used: {result.get('model_used', 'unknown')}")
                print(f"🎯 Capability: {result.get('capability_used', 'unknown')}")
                print(f"💬 Response: {result.get('response', 'No response')[:200]}...")
            else:
                print(f"❌ Failed with status: {response.status_code}")
                print(f"Error: {response.text}")
                
        except requests.exceptions.Timeout:
            print(f"⏰ Timeout after 30 seconds")
        except Exception as e:
            print(f"❌ Error: {e}")
        
        print("-" * 80)

if __name__ == "__main__":
    print("🚀 Testing Unified Local AI Agent")
    print("=" * 80)
    test_agent()
